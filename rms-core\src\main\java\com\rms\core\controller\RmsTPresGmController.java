package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTPresGm;
import com.rms.core.service.IRmsTPresGmService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 处方过敏信息Controller
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/rms/tpresgm")
public class RmsTPresGmController extends BaseController
{
    @Autowired
    private IRmsTPresGmService rmsTPresGmService;

    /**
     * 查询处方过敏信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresgm:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTPresGm rmsTPresGm)
    {
        startPage();
        List<RmsTPresGm> list = rmsTPresGmService.selectRmsTPresGmList(rmsTPresGm);
        return getDataTable(list);
    }

    /**
     * 导出处方过敏信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresgm:export')")
    @Log(title = "处方过敏信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTPresGm rmsTPresGm)
    {
        List<RmsTPresGm> list = rmsTPresGmService.selectRmsTPresGmList(rmsTPresGm);
        ExcelUtil<RmsTPresGm> util = new ExcelUtil<RmsTPresGm>(RmsTPresGm.class);
        util.exportExcel(response, list, "处方过敏信息数据");
    }

    /**
     * 获取处方过敏信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresgm:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(rmsTPresGmService.selectRmsTPresGmByCode(code));
    }

    /**
     * 新增处方过敏信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresgm:add')")
    @Log(title = "处方过敏信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTPresGm rmsTPresGm)
    {
        return toAjax(rmsTPresGmService.insertRmsTPresGm(rmsTPresGm));
    }

    /**
     * 修改处方过敏信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresgm:edit')")
    @Log(title = "处方过敏信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTPresGm rmsTPresGm)
    {
        return toAjax(rmsTPresGmService.updateRmsTPresGm(rmsTPresGm));
    }

    /**
     * 删除处方过敏信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresgm:remove')")
    @Log(title = "处方过敏信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(rmsTPresGmService.deleteRmsTPresGmByCodes(codes));
    }
}
