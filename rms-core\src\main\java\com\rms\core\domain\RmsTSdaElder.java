package com.rms.core.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 老年人用药规则库对象 rms_t_sda_elder
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public class RmsTSdaElder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long sdaId;

    /** 最小年龄 */
    @Excel(name = "最小年龄")
    private BigDecimal ageMin;

    /** 最大年龄 */
    @Excel(name = "最大年龄")
    private BigDecimal ageMax;

    /** 禁慎标识：0-禁用，1-慎用，2-提示，9-忽略/禁用 */
    @Excel(name = "禁慎标识：0-禁用，1-慎用，2-提示，9-忽略/禁用")
    private Long jsbs;

    /** 是否屏蔽 */
    @Excel(name = "是否屏蔽")
    private String ispb;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setAgeMin(BigDecimal ageMin)
    {
        this.ageMin = ageMin;
    }

    public BigDecimal getAgeMin()
    {
        return ageMin;
    }

    public void setAgeMax(BigDecimal ageMax)
    {
        this.ageMax = ageMax;
    }

    public BigDecimal getAgeMax()
    {
        return ageMax;
    }

    public void setJsbs(Long jsbs)
    {
        this.jsbs = jsbs;
    }

    public Long getJsbs()
    {
        return jsbs;
    }

    public void setIspb(String ispb)
    {
        this.ispb = ispb;
    }

    public String getIspb()
    {
        return ispb;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sdaId", getSdaId())
            .append("ageMin", getAgeMin())
            .append("ageMax", getAgeMax())
            .append("jsbs", getJsbs())
            .append("remark", getRemark())
            .append("ispb", getIspb())
            .toString();
    }
}
