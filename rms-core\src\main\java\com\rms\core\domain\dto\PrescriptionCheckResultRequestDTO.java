package com.rms.core.domain.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 获取处方审核结果请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class PrescriptionCheckResultRequestDTO {

    /**
     * 基本信息
     */
    @Valid
    @NotNull(message = "基本信息不能为空")
    private BaseInfoDTO baseInfo;

    /**
     * 详细信息
     */
    @Valid
    @NotNull(message = "详细信息不能为空")
    private PrescriptionCheckResultDetailsDTO details;

    public BaseInfoDTO getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseInfoDTO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public PrescriptionCheckResultDetailsDTO getDetails() {
        return details;
    }

    public void setDetails(PrescriptionCheckResultDetailsDTO details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "PrescriptionCheckResultRequestDTO{" +
                "baseInfo=" + baseInfo +
                ", details=" + details +
                '}';
    }
}
