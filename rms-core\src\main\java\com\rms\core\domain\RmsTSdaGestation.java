package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 孕期用药规则库对象 rms_t_sda_gestation
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public class RmsTSdaGestation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 药品说明书ID */
    @Excel(name = "药品说明书ID")
    private Long sdaId;

    /** 开始孕周 */
    @Excel(name = "开始孕周")
    private Long dayup;

    /** 结束孕周 */
    @Excel(name = "结束孕周")
    private Long daydown;

    /** 单位代码（未用） */
    @Excel(name = "单位代码", readConverterExp = "未=用")
    private String dwcode;

    /** 单位（未用） */
    @Excel(name = "单位", readConverterExp = "未=用")
    private String dw;

    /** 标识：0-禁用；1-慎用；2-提示；9-忽略/停用 */
    @Excel(name = "标识：0-禁用；1-慎用；2-提示；9-忽略/停用")
    private String bs;

    /** 是否屏蔽 */
    @Excel(name = "是否屏蔽")
    private String ispb;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setDayup(Long dayup)
    {
        this.dayup = dayup;
    }

    public Long getDayup()
    {
        return dayup;
    }

    public void setDaydown(Long daydown)
    {
        this.daydown = daydown;
    }

    public Long getDaydown()
    {
        return daydown;
    }

    public void setDwcode(String dwcode)
    {
        this.dwcode = dwcode;
    }

    public String getDwcode()
    {
        return dwcode;
    }

    public void setDw(String dw)
    {
        this.dw = dw;
    }

    public String getDw()
    {
        return dw;
    }

    public void setBs(String bs)
    {
        this.bs = bs;
    }

    public String getBs()
    {
        return bs;
    }

    public void setIspb(String ispb)
    {
        this.ispb = ispb;
    }

    public String getIspb()
    {
        return ispb;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sdaId", getSdaId())
            .append("dayup", getDayup())
            .append("daydown", getDaydown())
            .append("dwcode", getDwcode())
            .append("dw", getDw())
            .append("bs", getBs())
            .append("remark", getRemark())
            .append("ispb", getIspb())
            .toString();
    }
}
