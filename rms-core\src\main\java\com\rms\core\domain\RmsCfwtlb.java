package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 处方问题类别对象 rms_cfwtlb
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class RmsCfwtlb extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 处方问题编号 */
    private String cfwtbh;

    /** 处方问题名称 */
    @Excel(name = "处方问题名称")
    private String cfwtname;

    /** 处方问题详情 */
    @Excel(name = "处方问题详情")
    private String cfwtxq;

    /** 处方问题类别 */
    @Excel(name = "处方问题类别")
    private String cfwtlb;

    /** 处方问题类别名称 */
    @Excel(name = "处方问题类别名称")
    private String cfwtlbname;

    /** 问题代码 */
    @Excel(name = "问题代码")
    private String wtcode;

    /** 备注 */
    @Excel(name = "备注")
    private String bz;

    public void setCfwtbh(String cfwtbh)
    {
        this.cfwtbh = cfwtbh;
    }

    public String getCfwtbh()
    {
        return cfwtbh;
    }

    public void setCfwtname(String cfwtname)
    {
        this.cfwtname = cfwtname;
    }

    public String getCfwtname()
    {
        return cfwtname;
    }

    public void setCfwtxq(String cfwtxq)
    {
        this.cfwtxq = cfwtxq;
    }

    public String getCfwtxq()
    {
        return cfwtxq;
    }

    public void setCfwtlb(String cfwtlb)
    {
        this.cfwtlb = cfwtlb;
    }

    public String getCfwtlb()
    {
        return cfwtlb;
    }

    public void setCfwtlbname(String cfwtlbname)
    {
        this.cfwtlbname = cfwtlbname;
    }

    public String getCfwtlbname()
    {
        return cfwtlbname;
    }

    public void setWtcode(String wtcode)
    {
        this.wtcode = wtcode;
    }

    public String getWtcode()
    {
        return wtcode;
    }

    public void setBz(String bz)
    {
        this.bz = bz;
    }

    public String getBz()
    {
        return bz;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("cfwtbh", getCfwtbh())
            .append("cfwtname", getCfwtname())
            .append("cfwtxq", getCfwtxq())
            .append("cfwtlb", getCfwtlb())
            .append("cfwtlbname", getCfwtlbname())
            .append("wtcode", getWtcode())
            .append("bz", getBz())
            .toString();
    }
}
