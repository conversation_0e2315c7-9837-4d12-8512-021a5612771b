package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.rms.core.service.IRmsDrugKnowledgeService;
import com.rms.core.domain.DrugQueryResult;
import com.rms.core.domain.RmsTSdaChd;
import com.rms.core.domain.RmsTSdaElder;
import com.rms.core.domain.RmsTSdaGestation;
import com.rms.core.domain.RmsTSdaSex;
import com.rms.core.domain.RmsTSdaGytj;
import com.rms.core.domain.RmsTTjBase;
import com.rms.core.domain.RmsItfHosDrug;
import com.rms.core.mapper.RmsDrugKnowledgeMapper;
import com.rms.core.mapper.RmsTSdaChdMapper;
import com.rms.core.mapper.RmsTSdaElderMapper;
import com.rms.core.mapper.RmsTSdaGestationMapper;
import com.rms.core.mapper.RmsTSdaSexMapper;
import com.rms.core.mapper.RmsTSdaGytjMapper;
import com.rms.core.mapper.RmsTTjBaseMapper;
import com.rms.core.mapper.RmsTXhzyEdiMapper;
import com.rms.core.mapper.RmsTXhzyEdiZyMapper;
import com.rms.core.mapper.RmsTSdaIcd10InfoMapper;
import com.rms.core.mapper.RmsTIcd10BaseMapper;
import com.rms.core.domain.RmsTXhzyEdi;
import com.rms.core.domain.RmsTXhzyEdiZy;
import com.rms.core.domain.IncompatibleDrugResult;
import com.rms.core.domain.RmsTSdaIcd10Info;
import com.rms.core.domain.RmsTIcd10Base;
import com.rms.core.domain.DiagnosisRuleResult;
import com.rms.core.domain.RmsTSdaGm;
import com.rms.core.domain.RmsTGmbase;
import com.rms.core.mapper.RmsTSdaGmMapper;
import com.rms.core.mapper.RmsTGmbaseMapper;

/**
 * 药品知识库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class RmsDrugKnowledgeServiceImpl implements IRmsDrugKnowledgeService
{
    @Autowired
    private RmsDrugKnowledgeMapper rmsDrugKnowledgeMapper;

    @Autowired
    private RmsTSdaChdMapper rmsTSdaChdMapper;

    @Autowired
    private RmsTSdaElderMapper rmsTSdaElderMapper;

    @Autowired
    private RmsTSdaGestationMapper rmsTSdaGestationMapper;

    @Autowired
    private RmsTSdaSexMapper rmsTSdaSexMapper;

    @Autowired
    private RmsTSdaGytjMapper rmsTSdaGytjMapper;

    @Autowired
    private RmsTTjBaseMapper rmsTTjBaseMapper;

    @Autowired
    private RmsTXhzyEdiMapper rmsTXhzyEdiMapper;

    @Autowired
    private RmsTXhzyEdiZyMapper rmsTXhzyEdiZyMapper;

    @Autowired
    private RmsTSdaIcd10InfoMapper rmsTSdaIcd10InfoMapper;

    @Autowired
    private RmsTIcd10BaseMapper rmsTIcd10BaseMapper;

    @Autowired
    private RmsTSdaGmMapper rmsTSdaGmMapper;

    @Autowired
    private RmsTGmbaseMapper rmsTGmbaseMapper;

    /**
     * 根据关键字查询药品
     *
     * @param keyword 检索关键字
     * @return 药品查询结果集合
     */
    @Override
    public List<DrugQueryResult> searchDrugs(String keyword)
    {
        return rmsDrugKnowledgeMapper.searchDrugs(keyword);
    }

    /**
     * 根据sdaId查询儿童用药规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 儿童用药规则库集合
     */
    @Override
    public List<RmsTSdaChd> getChildDrugRulesBySdaId(Long sdaId)
    {
        RmsTSdaChd query = new RmsTSdaChd();
        query.setSdaId(sdaId);
        return rmsTSdaChdMapper.selectRmsTSdaChdList(query);
    }

    /**
     * 新增儿童用药规则库
     *
     * @param rmsTSdaChd 儿童用药规则库
     * @return 结果
     */
    @Override
    public int insertChildDrugRule(RmsTSdaChd rmsTSdaChd)
    {
        return rmsTSdaChdMapper.insertRmsTSdaChd(rmsTSdaChd);
    }

    /**
     * 修改儿童用药规则库
     *
     * @param rmsTSdaChd 儿童用药规则库
     * @return 结果
     */
    @Override
    public int updateChildDrugRule(RmsTSdaChd rmsTSdaChd)
    {
        return rmsTSdaChdMapper.updateRmsTSdaChd(rmsTSdaChd);
    }

    /**
     * 删除儿童用药规则库
     *
     * @param id 儿童用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteChildDrugRule(Long id)
    {
        return rmsTSdaChdMapper.deleteRmsTSdaChdById(id);
    }

    /**
     * 批量删除儿童用药规则库
     *
     * @param ids 儿童用药规则库主键数组
     * @return 结果
     */
    @Override
    public int deleteChildDrugRules(Long[] ids)
    {
        return rmsTSdaChdMapper.deleteRmsTSdaChdByIds(ids);
    }

    /**
     * 根据sdaId查询老年人用药规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 老年人用药规则库集合
     */
    @Override
    public List<RmsTSdaElder> getElderDrugRulesBySdaId(Long sdaId)
    {
        RmsTSdaElder query = new RmsTSdaElder();
        query.setSdaId(sdaId);
        return rmsTSdaElderMapper.selectRmsTSdaElderList(query);
    }

    /**
     * 新增老年人用药规则库
     *
     * @param rmsTSdaElder 老年人用药规则库
     * @return 结果
     */
    @Override
    public int insertElderDrugRule(RmsTSdaElder rmsTSdaElder)
    {
        return rmsTSdaElderMapper.insertRmsTSdaElder(rmsTSdaElder);
    }

    /**
     * 修改老年人用药规则库
     *
     * @param rmsTSdaElder 老年人用药规则库
     * @return 结果
     */
    @Override
    public int updateElderDrugRule(RmsTSdaElder rmsTSdaElder)
    {
        return rmsTSdaElderMapper.updateRmsTSdaElder(rmsTSdaElder);
    }

    /**
     * 删除老年人用药规则库
     *
     * @param id 老年人用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteElderDrugRule(Long id)
    {
        return rmsTSdaElderMapper.deleteRmsTSdaElderById(id);
    }

    /**
     * 批量删除老年人用药规则库
     *
     * @param ids 老年人用药规则库主键数组
     * @return 结果
     */
    @Override
    public int deleteElderDrugRules(Long[] ids)
    {
        return rmsTSdaElderMapper.deleteRmsTSdaElderByIds(ids);
    }

    /**
     * 根据sdaId查询孕妇用药规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 孕妇用药规则库集合
     */
    @Override
    public List<RmsTSdaGestation> getPregnancyDrugRulesBySdaId(Long sdaId)
    {
        RmsTSdaGestation query = new RmsTSdaGestation();
        query.setSdaId(sdaId);
        return rmsTSdaGestationMapper.selectRmsTSdaGestationList(query);
    }

    /**
     * 新增孕妇用药规则库
     *
     * @param rmsTSdaGestation 孕妇用药规则库
     * @return 结果
     */
    @Override
    public int insertPregnancyDrugRule(RmsTSdaGestation rmsTSdaGestation)
    {
        return rmsTSdaGestationMapper.insertRmsTSdaGestation(rmsTSdaGestation);
    }

    /**
     * 修改孕妇用药规则库
     *
     * @param rmsTSdaGestation 孕妇用药规则库
     * @return 结果
     */
    @Override
    public int updatePregnancyDrugRule(RmsTSdaGestation rmsTSdaGestation)
    {
        return rmsTSdaGestationMapper.updateRmsTSdaGestation(rmsTSdaGestation);
    }

    /**
     * 删除孕妇用药规则库
     *
     * @param id 孕妇用药规则库主键
     * @return 结果
     */
    @Override
    public int deletePregnancyDrugRule(Long id)
    {
        return rmsTSdaGestationMapper.deleteRmsTSdaGestationById(id);
    }

    /**
     * 批量删除孕妇用药规则库
     *
     * @param ids 孕妇用药规则库主键数组
     * @return 结果
     */
    @Override
    public int deletePregnancyDrugRules(Long[] ids)
    {
        return rmsTSdaGestationMapper.deleteRmsTSdaGestationByIds(ids);
    }

    /**
     * 根据sdaId查询性别用药规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 性别用药规则库集合
     */
    @Override
    public List<RmsTSdaSex> getSexDrugRulesBySdaId(Long sdaId)
    {
        RmsTSdaSex query = new RmsTSdaSex();
        query.setSdaId(sdaId);
        return rmsTSdaSexMapper.selectRmsTSdaSexList(query);
    }

    /**
     * 新增性别用药规则库
     *
     * @param rmsTSdaSex 性别用药规则库
     * @return 结果
     */
    @Override
    public int insertSexDrugRule(RmsTSdaSex rmsTSdaSex)
    {
        return rmsTSdaSexMapper.insertRmsTSdaSex(rmsTSdaSex);
    }

    /**
     * 修改性别用药规则库
     *
     * @param rmsTSdaSex 性别用药规则库
     * @return 结果
     */
    @Override
    public int updateSexDrugRule(RmsTSdaSex rmsTSdaSex)
    {
        return rmsTSdaSexMapper.updateRmsTSdaSex(rmsTSdaSex);
    }

    /**
     * 删除性别用药规则库
     *
     * @param id 性别用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteSexDrugRule(Long id)
    {
        return rmsTSdaSexMapper.deleteRmsTSdaSexById(id);
    }

    /**
     * 批量删除性别用药规则库
     *
     * @param ids 性别用药规则库主键数组
     * @return 结果
     */
    @Override
    public int deleteSexDrugRules(Long[] ids)
    {
        return rmsTSdaSexMapper.deleteRmsTSdaSexByIds(ids);
    }

    /**
     * 获取所有给药途径基础数据
     *
     * @return 给药途径基础数据集合
     */
    @Override
    public List<RmsTTjBase> getAllRouteTypes()
    {
        return rmsTTjBaseMapper.selectRmsTTjBaseList(new RmsTTjBase());
    }

    /**
     * 根据sdaId查询药品的给药途径信息
     *
     * @param sdaId 标准数据ID
     * @return 给药途径集合
     */
    @Override
    public List<RmsTSdaGytj> getRouteDrugRulesBySdaId(Long sdaId)
    {
        RmsTSdaGytj query = new RmsTSdaGytj();
        query.setSdaId(sdaId);
        return rmsTSdaGytjMapper.selectRmsTSdaGytjList(query);
    }

    /**
     * 为药品添加给药途径
     *
     * @param sdaId 标准数据ID
     * @param routeCodes 给药途径代码数组
     * @return 结果
     */
    @Override
    public int addRouteDrugRules(Long sdaId, String[] routeCodes)
    {
        int result = 0;
        for (String routeCode : routeCodes) {
            RmsTSdaGytj routeRule = new RmsTSdaGytj();
            routeRule.setSdaId(sdaId);
            routeRule.setGytjCode(routeCode);
            routeRule.setIspb("0"); // 默认不屏蔽
            result += rmsTSdaGytjMapper.insertRmsTSdaGytj(routeRule);
        }
        return result;
    }

    /**
     * 删除药品的给药途径
     *
     * @param ids 给药途径ID数组
     * @return 结果
     */
    @Override
    public int deleteRouteDrugRules(Long[] ids)
    {
        return rmsTSdaGytjMapper.deleteRmsTSdaGytjByIds(ids);
    }

    /**
     * 根据drugCode查询药品标识信息
     *
     * @param drugCode 药品编码
     * @return 药品标识信息
     */
    @Override
    public RmsItfHosDrug getDrugIdentityByDrugCode(String drugCode)
    {
        return rmsDrugKnowledgeMapper.getDrugIdentityByDrugCode(drugCode);
    }

    /**
     * 更新药品标识信息
     *
     * @param rmsItfHosDrug 药品标识信息
     * @return 结果
     */
    @Override
    public int updateDrugIdentity(RmsItfHosDrug rmsItfHosDrug)
    {
        return rmsDrugKnowledgeMapper.updateDrugIdentity(rmsItfHosDrug);
    }

    /**
     * 获取给药途径基础数据
     *
     * @return 给药途径基础数据集合
     */
    @Override
    public List<RmsTTjBase> getRouteBaseList()
    {
        return rmsTTjBaseMapper.selectRmsTTjBaseList(new RmsTTjBase());
    }

    /**
     * 根据sdaId查询常规用量规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 常规用量规则库集合
     */
    @Override
    public List<java.util.Map<String, Object>> getDoseRulesBySdaId(Long sdaId)
    {
        return rmsDrugKnowledgeMapper.getDoseRulesBySdaId(sdaId);
    }

    /**
     * 新增常规用量规则库
     *
     * @param params 常规用量规则参数
     * @return 结果
     */
    @Override
    public int insertDoseRule(java.util.Map<String, Object> params)
    {
        // 先插入条件表，获取生成的ID
        int conditionResult = rmsDrugKnowledgeMapper.insertDoseCondition(params);
        if (conditionResult > 0) {
            // 再插入结果表
            return rmsDrugKnowledgeMapper.insertDoseResult(params);
        }
        return 0;
    }

    /**
     * 修改常规用量规则库
     *
     * @param params 常规用量规则参数
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDoseRule(java.util.Map<String, Object> params)
    {
        // 更新条件表
        int conditionResult = rmsDrugKnowledgeMapper.updateDoseCondition(params);
        // 更新结果表
        int resultResult = rmsDrugKnowledgeMapper.updateDoseResult(params);
        return conditionResult; // 返回条件表的更新结果
    }

    /**
     * 删除常规用量规则库
     *
     * @param id 常规用量规则库主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDoseRule(Long id)
    {
        // 先删除结果表
        int resultCount = rmsDrugKnowledgeMapper.deleteDoseResult(id);
        // 再删除条件表
        int conditionCount = rmsDrugKnowledgeMapper.deleteDoseCondition(id);
        return conditionCount; // 返回条件表的删除结果
    }

    /**
     * 批量删除常规用量规则库
     *
     * @param ids 常规用量规则库主键数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDoseRules(Long[] ids)
    {
        // 先删除结果表
        int resultCount = rmsDrugKnowledgeMapper.deleteDoseResults(ids);
        // 再删除条件表
        int conditionCount = rmsDrugKnowledgeMapper.deleteDoseConditions(ids);
        return conditionCount; // 返回条件表的删除结果
    }

    // ========== 配伍禁忌相关方法实现 ==========

    /**
     * 根据sdaId查询配伍禁忌信息（西药）
     *
     * @param sdaId 标准数据ID
     * @return 配伍禁忌集合
     */
    @Override
    public List<IncompatibleDrugResult> getIncompatibleRulesBySdaId(Long sdaId)
    {
        return rmsTXhzyEdiMapper.selectIncompatibleRulesBySdaId(sdaId);
    }

    /**
     * 新增配伍禁忌规则（西药）
     *
     * @param rmsTXhzyEdi 配伍禁忌规则
     * @return 结果
     */
    @Override
    public int insertIncompatibleRule(RmsTXhzyEdi rmsTXhzyEdi)
    {
        return rmsTXhzyEdiMapper.insertRmsTXhzyEdi(rmsTXhzyEdi);
    }

    /**
     * 修改配伍禁忌规则（西药）
     *
     * @param rmsTXhzyEdi 配伍禁忌规则
     * @return 结果
     */
    @Override
    public int updateIncompatibleRule(RmsTXhzyEdi rmsTXhzyEdi)
    {
        return rmsTXhzyEdiMapper.updateRmsTXhzyEdi(rmsTXhzyEdi);
    }

    /**
     * 删除配伍禁忌规则（西药）
     *
     * @param id 配伍禁忌规则主键
     * @return 结果
     */
    @Override
    public int deleteIncompatibleRule(Long id)
    {
        return rmsTXhzyEdiMapper.deleteRmsTXhzyEdiById(id);
    }

    /**
     * 批量删除配伍禁忌规则（西药）
     *
     * @param ids 配伍禁忌规则主键数组
     * @return 结果
     */
    @Override
    public int deleteIncompatibleRules(Long[] ids)
    {
        return rmsTXhzyEdiMapper.deleteRmsTXhzyEdiByIds(ids);
    }

    /**
     * 根据sdaId查询配伍禁忌信息（中药）
     *
     * @param sdaId 标准数据ID
     * @return 配伍禁忌集合
     */
    @Override
    public List<IncompatibleDrugResult> getIncompatibleRulesZyBySdaId(Long sdaId)
    {
        return rmsTXhzyEdiZyMapper.selectIncompatibleRulesZyBySdaId(sdaId);
    }

    /**
     * 新增配伍禁忌规则（中药）
     *
     * @param rmsTXhzyEdiZy 配伍禁忌规则
     * @return 结果
     */
    @Override
    public int insertIncompatibleRuleZy(RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return rmsTXhzyEdiZyMapper.insertRmsTXhzyEdiZy(rmsTXhzyEdiZy);
    }

    /**
     * 修改配伍禁忌规则（中药）
     *
     * @param rmsTXhzyEdiZy 配伍禁忌规则
     * @return 结果
     */
    @Override
    public int updateIncompatibleRuleZy(RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return rmsTXhzyEdiZyMapper.updateRmsTXhzyEdiZy(rmsTXhzyEdiZy);
    }

    /**
     * 删除配伍禁忌规则（中药）
     *
     * @param id 配伍禁忌规则主键
     * @return 结果
     */
    @Override
    public int deleteIncompatibleRuleZy(Long id)
    {
        return rmsTXhzyEdiZyMapper.deleteRmsTXhzyEdiZyById(id);
    }

    /**
     * 批量删除配伍禁忌规则（中药）
     *
     * @param ids 配伍禁忌规则主键数组
     * @return 结果
     */
    @Override
    public int deleteIncompatibleRulesZy(Long[] ids)
    {
        return rmsTXhzyEdiZyMapper.deleteRmsTXhzyEdiZyByIds(ids);
    }

    // ========== 药物与诊断相关方法实现 ==========

    /**
     * 根据sdaId查询药物与诊断信息（包含ICD信息）
     *
     * @param sdaId 标准数据ID
     * @return 药物与诊断信息集合
     */
    @Override
    public List<DiagnosisRuleResult> getDiagnosisRulesBySdaId(Long sdaId)
    {
        return rmsTSdaIcd10InfoMapper.selectDiagnosisRulesWithIcdBySdaId(sdaId);
    }

    /**
     * 新增药物与诊断信息
     *
     * @param rmsTSdaIcd10Info 药物与诊断信息
     * @return 结果
     */
    @Override
    public int insertDiagnosisRule(RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return rmsTSdaIcd10InfoMapper.insertRmsTSdaIcd10Info(rmsTSdaIcd10Info);
    }

    /**
     * 删除药物与诊断信息
     *
     * @param id 药物与诊断信息主键
     * @return 结果
     */
    @Override
    public int deleteDiagnosisRule(Long id)
    {
        return rmsTSdaIcd10InfoMapper.deleteRmsTSdaIcd10InfoById(id);
    }

    /**
     * 批量删除药物与诊断信息
     *
     * @param ids 药物与诊断信息主键数组
     * @return 结果
     */
    @Override
    public int deleteDiagnosisRules(Long[] ids)
    {
        return rmsTSdaIcd10InfoMapper.deleteRmsTSdaIcd10InfoByIds(ids);
    }

    /**
     * 根据关键字搜索ICD诊断信息
     *
     * @param keyword 搜索关键字
     * @return ICD诊断信息集合
     */
    @Override
    public List<RmsTIcd10Base> searchDiagnosis(String keyword)
    {
        return rmsTIcd10BaseMapper.searchByKeyword(keyword);
    }

    /**
     * 根据sdaId查询药物禁忌症信息（包含ICD信息）
     *
     * @param sdaId 标准数据ID
     * @return 药物禁忌症信息集合
     */
    @Override
    public List<DiagnosisRuleResult> getContraindicationRulesBySdaId(Long sdaId)
    {
        return rmsTSdaIcd10InfoMapper.selectContraindicationRulesWithIcdBySdaId(sdaId);
    }

    /**
     * 新增药物禁忌症信息
     *
     * @param rmsTSdaIcd10Info 药物禁忌症信息
     * @return 结果
     */
    @Override
    public int insertContraindicationRule(RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return rmsTSdaIcd10InfoMapper.insertRmsTSdaIcd10Info(rmsTSdaIcd10Info);
    }

    /**
     * 删除药物禁忌症信息
     *
     * @param id 药物禁忌症信息主键
     * @return 结果
     */
    @Override
    public int deleteContraindicationRule(Long id)
    {
        return rmsTSdaIcd10InfoMapper.deleteRmsTSdaIcd10InfoById(id);
    }

    /**
     * 批量删除药物禁忌症信息
     *
     * @param ids 药物禁忌症信息主键数组
     * @return 结果
     */
    @Override
    public int deleteContraindicationRules(Long[] ids)
    {
        return rmsTSdaIcd10InfoMapper.deleteRmsTSdaIcd10InfoByIds(ids);
    }

    // ========== 过敏知识库相关方法实现 ==========

    /**
     * 获取所有过敏基础数据
     *
     * @return 过敏基础数据集合
     */
    @Override
    public List<RmsTGmbase> getAllergyBaseList()
    {
        RmsTGmbase query = new RmsTGmbase();
        return rmsTGmbaseMapper.selectRmsTGmbaseList(query);
    }

    /**
     * 根据sdaId查询药品过敏信息（包含过敏名称）
     *
     * @param sdaId 标准数据ID
     * @return 药品过敏信息集合
     */
    @Override
    public List<java.util.Map<String, Object>> getAllergyRulesBySdaId(Long sdaId)
    {
        return rmsTSdaGmMapper.selectAllergyRulesWithNameBySdaId(sdaId);
    }

    /**
     * 新增药品过敏信息
     *
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    @Override
    public int insertAllergyRule(RmsTSdaGm rmsTSdaGm)
    {
        return rmsTSdaGmMapper.insertRmsTSdaGm(rmsTSdaGm);
    }

    /**
     * 修改药品过敏信息
     *
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    @Override
    public int updateAllergyRule(RmsTSdaGm rmsTSdaGm)
    {
        return rmsTSdaGmMapper.updateRmsTSdaGm(rmsTSdaGm);
    }

    /**
     * 删除药品过敏信息
     *
     * @param id 药品过敏信息主键
     * @return 结果
     */
    @Override
    public int deleteAllergyRule(Long id)
    {
        return rmsTSdaGmMapper.deleteRmsTSdaGmById(id);
    }

    /**
     * 批量删除药品过敏信息
     *
     * @param ids 药品过敏信息主键数组
     * @return 结果
     */
    @Override
    public int deleteAllergyRules(Long[] ids)
    {
        return rmsTSdaGmMapper.deleteRmsTSdaGmByIds(ids);
    }

    /**
     * 根据关键字搜索过敏信息
     *
     * @param keyword 搜索关键字
     * @return 过敏信息集合
     */
    @Override
    public List<java.util.Map<String, Object>> searchAllergy(String keyword)
    {
        return rmsTSdaGmMapper.searchByKeyword(keyword);
    }

    /**
     * 根据关键字搜索过敏基础数据
     *
     * @param keyword 搜索关键字
     * @return 过敏基础数据集合
     */
    @Override
    public List<RmsTGmbase> searchAllergyBase(String keyword)
    {
        return rmsTGmbaseMapper.searchAllergyBaseByKeyword(keyword);
    }
}
