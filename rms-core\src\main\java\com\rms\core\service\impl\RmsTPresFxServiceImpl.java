package com.rms.core.service.impl;

import java.util.List;
import com.rms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTPresFxMapper;
import com.rms.core.domain.RmsTPresFx;
import com.rms.core.service.IRmsTPresFxService;

/**
 * 处方分析结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class RmsTPresFxServiceImpl implements IRmsTPresFxService
{
    @Autowired
    private RmsTPresFxMapper rmsTPresFxMapper;

    /**
     * 查询处方分析结果
     *
     * @param id 处方分析结果主键
     * @return 处方分析结果
     */
    @Override
    public RmsTPresFx selectRmsTPresFxById(Long id)
    {
        return rmsTPresFxMapper.selectRmsTPresFxById(id);
    }

    /**
     * 查询处方分析结果列表
     *
     * @param rmsTPresFx 处方分析结果
     * @return 处方分析结果
     */
    @Override
    public List<RmsTPresFx> selectRmsTPresFxList(RmsTPresFx rmsTPresFx)
    {
        return rmsTPresFxMapper.selectRmsTPresFxList(rmsTPresFx);
    }

    /**
     * 新增处方分析结果
     *
     * @param rmsTPresFx 处方分析结果
     * @return 结果
     */
    @Override
    public int insertRmsTPresFx(RmsTPresFx rmsTPresFx)
    {
        rmsTPresFx.setCreateTime(DateUtils.getNowDate());
        return rmsTPresFxMapper.insertRmsTPresFx(rmsTPresFx);
    }

    /**
     * 修改处方分析结果
     *
     * @param rmsTPresFx 处方分析结果
     * @return 结果
     */
    @Override
    public int updateRmsTPresFx(RmsTPresFx rmsTPresFx)
    {
        return rmsTPresFxMapper.updateRmsTPresFx(rmsTPresFx);
    }

    /**
     * 批量删除处方分析结果
     *
     * @param ids 需要删除的处方分析结果主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresFxByIds(Long[] ids)
    {
        return rmsTPresFxMapper.deleteRmsTPresFxByIds(ids);
    }

    /**
     * 删除处方分析结果信息
     *
     * @param id 处方分析结果主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresFxById(Long id)
    {
        return rmsTPresFxMapper.deleteRmsTPresFxById(id);
    }
}
