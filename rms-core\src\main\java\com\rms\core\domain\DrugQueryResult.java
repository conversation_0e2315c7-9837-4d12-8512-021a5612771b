package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;

/**
 * 药品查询结果对象 DrugQueryResult
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public class DrugQueryResult
{
    private static final long serialVersionUID = 1L;

    /** 药品名称 */
    @Excel(name = "药品名称")
    private String drugName;

    /** 药品编码 */
    @Excel(name = "药品编码")
    private String ypCode;

    /** 标准数据ID */
    @Excel(name = "标准数据ID")
    private Long sdaId;

    /** 生产厂家 */
    @Excel(name = "生产厂家")
    private String drugManuf;

    /** 药品规格 */
    @Excel(name = "药品规格")
    private String drugSpec;

    /** 药品单位 */
    @Excel(name = "药品单位")
    private String unitRem;

    public void setDrugName(String drugName)
    {
        this.drugName = drugName;
    }

    public String getDrugName()
    {
        return drugName;
    }

    public void setYpCode(String ypCode)
    {
        this.ypCode = ypCode;
    }

    public String getYpCode()
    {
        return ypCode;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setDrugManuf(String drugManuf)
    {
        this.drugManuf = drugManuf;
    }

    public String getDrugManuf()
    {
        return drugManuf;
    }

    public void setDrugSpec(String drugSpec)
    {
        this.drugSpec = drugSpec;
    }

    public String getDrugSpec()
    {
        return drugSpec;
    }

    public void setUnitRem(String unitRem)
    {
        this.unitRem = unitRem;
    }

    public String getUnitRem()
    {
        return unitRem;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("drugName", getDrugName())
            .append("ypCode", getYpCode())
            .append("sdaId", getSdaId())
            .append("drugManuf", getDrugManuf())
            .append("drugSpec", getDrugSpec())
            .append("unitRem", getUnitRem())
            .toString();
    }
} 