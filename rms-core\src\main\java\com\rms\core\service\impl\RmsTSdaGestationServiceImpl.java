package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaGestationMapper;
import com.rms.core.domain.RmsTSdaGestation;
import com.rms.core.service.IRmsTSdaGestationService;

/**
 * 孕期用药规则库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class RmsTSdaGestationServiceImpl implements IRmsTSdaGestationService
{
    @Autowired
    private RmsTSdaGestationMapper rmsTSdaGestationMapper;

    /**
     * 查询孕期用药规则库
     *
     * @param id 孕期用药规则库主键
     * @return 孕期用药规则库
     */
    @Override
    public RmsTSdaGestation selectRmsTSdaGestationById(Long id)
    {
        return rmsTSdaGestationMapper.selectRmsTSdaGestationById(id);
    }

    /**
     * 查询孕期用药规则库列表
     *
     * @param rmsTSdaGestation 孕期用药规则库
     * @return 孕期用药规则库
     */
    @Override
    public List<RmsTSdaGestation> selectRmsTSdaGestationList(RmsTSdaGestation rmsTSdaGestation)
    {
        return rmsTSdaGestationMapper.selectRmsTSdaGestationList(rmsTSdaGestation);
    }

    /**
     * 新增孕期用药规则库
     *
     * @param rmsTSdaGestation 孕期用药规则库
     * @return 结果
     */
    @Override
    public int insertRmsTSdaGestation(RmsTSdaGestation rmsTSdaGestation)
    {
        return rmsTSdaGestationMapper.insertRmsTSdaGestation(rmsTSdaGestation);
    }

    /**
     * 修改孕期用药规则库
     *
     * @param rmsTSdaGestation 孕期用药规则库
     * @return 结果
     */
    @Override
    public int updateRmsTSdaGestation(RmsTSdaGestation rmsTSdaGestation)
    {
        return rmsTSdaGestationMapper.updateRmsTSdaGestation(rmsTSdaGestation);
    }

    /**
     * 批量删除孕期用药规则库
     *
     * @param ids 需要删除的孕期用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaGestationByIds(Long[] ids)
    {
        return rmsTSdaGestationMapper.deleteRmsTSdaGestationByIds(ids);
    }

    /**
     * 删除孕期用药规则库信息
     *
     * @param id 孕期用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaGestationById(Long id)
    {
        return rmsTSdaGestationMapper.deleteRmsTSdaGestationById(id);
    }
}
