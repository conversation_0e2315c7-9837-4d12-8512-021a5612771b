import request from '@/utils/request'

// 查询中药相互作用列表
export function listTxhzyedizy(query) {
  return request({
    url: '/rms/txhzyedizy/list',
    method: 'get',
    params: query
  })
}

// 查询中药相互作用详细
export function getTxhzyedizy(id) {
  return request({
    url: '/rms/txhzyedizy/' + id,
    method: 'get'
  })
}

// 新增中药相互作用
export function addTxhzyedizy(data) {
  return request({
    url: '/rms/txhzyedizy',
    method: 'post',
    data: data
  })
}

// 修改中药相互作用
export function updateTxhzyedizy(data) {
  return request({
    url: '/rms/txhzyedizy',
    method: 'put',
    data: data
  })
}

// 删除中药相互作用
export function delTxhzyedizy(id) {
  return request({
    url: '/rms/txhzyedizy/' + id,
    method: 'delete'
  })
}
