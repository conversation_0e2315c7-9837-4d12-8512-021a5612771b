package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaAge;
import com.rms.core.service.IRmsTSdaAgeService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 年龄代码Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/rms/tsdaage")
public class RmsTSdaAgeController extends BaseController
{
    @Autowired
    private IRmsTSdaAgeService rmsTSdaAgeService;

    /**
     * 查询年龄代码列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaage:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaAge rmsTSdaAge)
    {
        startPage();
        List<RmsTSdaAge> list = rmsTSdaAgeService.selectRmsTSdaAgeList(rmsTSdaAge);
        return getDataTable(list);
    }

    /**
     * 导出年龄代码列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaage:export')")
    @Log(title = "年龄代码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaAge rmsTSdaAge)
    {
        List<RmsTSdaAge> list = rmsTSdaAgeService.selectRmsTSdaAgeList(rmsTSdaAge);
        ExcelUtil<RmsTSdaAge> util = new ExcelUtil<RmsTSdaAge>(RmsTSdaAge.class);
        util.exportExcel(response, list, "年龄代码数据");
    }

    /**
     * 获取年龄代码详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaAgeService.selectRmsTSdaAgeById(id));
    }

    /**
     * 新增年龄代码
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaage:add')")
    @Log(title = "年龄代码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaAge rmsTSdaAge)
    {
        return toAjax(rmsTSdaAgeService.insertRmsTSdaAge(rmsTSdaAge));
    }

    /**
     * 修改年龄代码
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaage:edit')")
    @Log(title = "年龄代码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaAge rmsTSdaAge)
    {
        return toAjax(rmsTSdaAgeService.updateRmsTSdaAge(rmsTSdaAge));
    }

    /**
     * 删除年龄代码
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaage:remove')")
    @Log(title = "年龄代码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaAgeService.deleteRmsTSdaAgeByIds(ids));
    }
}
