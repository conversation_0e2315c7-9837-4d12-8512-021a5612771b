// MSSQL存储过程：
USE [hlyy]
GO
/****** Object:  StoredProcedure [dbo].[fx_yuliu_med]    Script Date: 2025/8/25 11:44:55 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- [fx_yuliu_med] 'f670c516-a262-432f-92c4-89aa36f5aa941' ,'455210'
ALTER proc [dbo].[fx_yuliu_med]
@code nvarchar(50),
@yp_code nvarchar(60)
WITH RECOMPILE
as

-- 药品用药总量分析
--if(dbo.fn_get_yyzl(@code,@yp_code)<0)
declare @分析开关 nvarchar(50)
select @分析开关=当前开关状态 from 分析过程控制 where 过程名= OBJECT_NAME(@@PROCID)
if(@分析开关<>'1')
begin
select top 0 * from t_pres_fx
return
end

declare @sda_id nvarchar(20)
declare @yp_name nvarchar(200)
declare @yp_name2 nvarchar(200)
declare @yp_yyzl nvarchar(50)-- 用户自定义的用药总量最大值
declare @ord_qty nvarchar(50)-- his传来的用药总量值
declare @ord_uom nvarchar(50)
declare @n_count2 int
declare @n_count11 int
declare @ks nvarchar(50)
declare @hosp_flag nvarchar(50)
declare @value float
declare @len_value int
declare @pres_type2 nvarchar(50)
declare @n_count6 int
declare @mid nvarchar(50)

select @n_count6=count(1) from  ITF_HOS_DRUG b
where b.DRUG_CODE=@yp_code and b.ZX_FLAG='3'
if @n_count6 >0
begin
return
end

select @sda_id=sda_id,@mid=cid from t_byyydzb where yp_code =@yp_code

SELECT @yp_name =med_name, @ord_qty=ord_qty, @ord_uom=ord_uom from t_pres_med where code=@code AND his_code =@yp_code
SELECT @yp_yyzl=zdfyl FROM  t_drug_zdfyl
WHERE drug_code= @yp_code and unit=@ord_uom
select @hosp_flag=hosp_flag,@pres_type2=pres_type from t_pres  where Code=@code;



if  @hosp_flag ='op' and (@yp_yyzl <>'' or @yp_yyzl is not null)
begin
set @value= CONVERT(float,@yp_yyzl)-CONVERT(float,@ord_qty)
if(@value<0)
insert into t_pres_fx select @code,@yp_name,'','1','一般提示','RLT047','yyzlwt','用药总量','用药总量问题', @yp_name+'超出最大发药量,医院要求门诊发药最大量为'+@yp_yyzl+@ord_uom,'0','用药总量'
end

--外用软膏最大发药量不能超过5支
declare @n_count36 int
select @n_count36=count(1) from  ITF_HOS_DRUG b
where b.DRUG_CODE=@yp_code and  b.DRUG_FOR_NAME in (
'软膏剂',
'乳膏剂',
'凝胶(胶浆)剂',
'眼膏剂',
'茶剂',
'滴眼剂')
if @hosp_flag ='op'and  @n_count36>0 and CONVERT(float,@ord_qty) >5
begin
insert into t_pres_fx select @code,@yp_name,'','1','一般提示','RLT047','yyzlwt','用药总量','用药总量问题', @yp_name+'超出最大发药量,医院要求门诊软膏、滴眼剂发药最大量为5支','0','用药总量'
end



-----药品自定义科室
declare @ksdm nvarchar(20)
declare @n_count int
declare @n_count1 int
SELECT @n_count=count(1) FROM  t_med_zdy_dept  WHERE yp_code= @yp_code;
if @n_count >0
begin
SELECT @ksdm =dept_code  from t_pres where code=@code;

SELECT @n_count1=count(1) FROM  t_med_zdy_dept     WHERE yp_code= @yp_code and dept_code =@ksdm ;

if @n_count1 =0
insert into t_pres_fx select @code, @yp_name ,'','1','严重警示','RLT048','yyks','用药科室','用药科室问题', '医院规定：'+@yp_name+'不能在该科室使用！','0','用药科室'
end

-----药品自定义医生
declare @ysdm nvarchar(20)
declare @n_count5 int
declare @n_count3 int
SELECT @n_count2=count(1) FROM  t_med_zdy_doct  WHERE yp_code= @yp_code;
if @n_count5 >0
begin
SELECT @ysdm =doct_code  from t_pres where code=@code;

SELECT @n_count3=count(1) FROM  t_med_zdy_doct     WHERE yp_code= @yp_code and doct_code =@ysdm ;

if @n_count3 =0
insert into t_pres_fx select @code, @yp_name ,'','2','一般提示','RLT049','yyys','用药医生','用药医生问题', @yp_name+'不能该医生使用！','0','用药科室'
end

----解决立刻执行频次问题
--select @n_count2=count(1) from t_pres_med a ,t_pres b where a.Code =b.code  and a.code =@Code and freq ='13' and his_code =@yp_code and b.hosp_flag ='op'  and (a.med_name not like '%针%' or a.med_name not like '%注射%');
----select @ks=dept_code from t_pres where code =@Code ;
--select @yp_name= med_name from t_pres_med where code =@Code   and his_code =@yp_code;


--if (@n_count2 >0 )
--begin
--insert into t_pres_fx select @code, @yp_name ,'','1','一般提示','RLT030','YHGXHCGYFYLWT_PC','药品用法用量','说明书未提及该用药频次.频次不能使用【立即执行】！', @yp_name+'说明书未提及该用药频次！','0','用药频次'
--end



----门诊输液智能急诊科开，其他科室阻断
--declare @n_count7 int
--declare @n_count8 int
--declare @n_count14 int

--select @n_count7=count(1) from ITF_HOS_SPEC a,t_pres b where a.SPEC_CODE =b.dept_code and b.code =@Code  and a.SPEC_NAME like '%急诊%' ;
--select @n_count8=count(1) from t_pres_med where code =@Code and administer in (select ADM_CODE from ITF_HOS_ADMIN_ROUTE where jmsybs ='1' );
--select @n_count14=COUNT(1)  from t_pres b where   b.code =@Code and hosp_flag ='op'
--if @n_count7 =0 and @n_count8 >0 and @n_count14 >0
--begin
--insert into t_pres_fx select @code, @yp_name ,'','2','严重警示','RLT048','yyks','用药科室','用药科室问题', '门诊输液只能在急诊科使用！','0','用药科室'
--end

----取药用、遵医嘱、自用拦截
declare @n_count9 int
select @n_count9=count(1) from t_pres_med where code =@Code and administer in (select ADM_CODE from ITF_HOS_ADMIN_ROUTE where jzbs ='1' );
if  @n_count9 >0
begin
insert into t_pres_fx select @code, @yp_name ,'','2','一般提示','RLT003','GYTJJJ','给药途径错误','给药途径错误', '所有药品不能使用【取药用、遵医嘱、自用】给药途径！','0','给药途径错误'
end

--草药一张处方开2次拦截
declare @n_count10 int
--select  @n_count10= count(1) from t_pres_med  where code =@Code and his_code =@yp_code
--and not exists (select 1 from ITF_HOS_DRUG b where b.DRUG_CODE =t_pres_med.his_code and b.is_rm ='1');
--if  @n_count10 >1
--begin
--insert into t_pres_fx select @code, @yp_name ,'','2','一般提示','RLT025','CFYYTS','重复用药','重复用药', '一张处方禁止开2个相同药品！','0','重复用药'
--end

----草药常规量自定义

--declare @cgl nvarchar(50)-- 用户自定义的用药总量最大值
--declare @dose nvarchar(50)-- 用户自定义的用药总量最大值
----declare @yp_name nvarchar(50)-- 用户自定义的用药总量最大值

--SELECT @cgl=cgl FROM  t_med_zdy_jlfw  WHERE yp_code= @yp_code
--select @dose=dose from t_pres_med where Code=@code and his_code=@yp_code
--set @value= CONVERT(float,@dose)-CONVERT(float,@cgl)
--if @cgl <>'' and @value>0
--begin
--insert into t_pres_fx select @code, @yp_name ,'','1','一般提示','RLT047','yyzlwt','用量问题','用药常规量问题', @yp_name+'超出医院自定义常规量,医院要求门诊发药常规量为'+@cgl,'0','用药总量'
--end

---自定义给药途径分析
declare @n_count12 int
declare @n_count13 int

select @n_count12 =count(1) from t_med_zdy_gytj where yp_code =@yp_code
select @n_count13=count(1)  from t_pres_med where code =@code and his_code =@yp_code and administer not in (select gytj_code from t_med_zdy_gytj where yp_code =@yp_code)
if @n_count12 >0 and @n_count13>0
begin
insert into t_pres_fx select @code, @yp_name ,'','1','一般提示','RLT026','GYTJWT','给药途径错误','给药途径问题', @yp_name+'不符合医院规定给药途径！','0','给药途径'
end



-----住院临时医嘱频次分析
declare @n_count18 int
declare @n_count19 int
declare @tymc varchar(50)
select @n_count18=count(1) from t_pres_med a ,t_pres b where a.code =@Code and a.Code =b.code  and freq IN ('01','13','15','14' )and his_code =@yp_code
select @tymc=DRUG_NAME from itf_hos_drug where DRUG_CODE= @yp_code

if @hosp_flag='ip' and @pres_type2 ='T' AND @n_count18>=1
begin
	DELETE FROM t_pres_fx WHERE Code =@Code AND wtcode='RLT030' AND title LIKE '%频次%' and ywa=@tymc
	select @n_count19=count(1) from t_pres_fx a ,t_pres b where a.Code =b.code  and a.code =@Code
	if  @n_count19 =0
	begin
	update t_pres set flag =0 where code =@Code
	end
end


-----门诊注射剂"立即使用"频次分析
declare @n_count20 int
declare @n_count21 int
select @n_count20=count(1) from t_pres_med a  where  a.code =@Code and freq IN ('01','13','15','14' )and his_code =@yp_code and (a.med_name like '%针%' or a.med_name like '%注射%')


if @hosp_flag='op'  AND @n_count20>=1
begin
	DELETE FROM t_pres_fx WHERE Code =@Code AND wtcode='RLT030' AND title LIKE '%频次%' and ywa=@tymc
	--select @n_count21=count(1) from t_pres_fx a ,t_pres b where a.Code =b.code  and a.code =@Code
	--if  @n_count21 =0
	--begin
	--update t_pres set flag =0 where code =@Code
	--end
end

-----门诊抗菌药物注射剂允许低于正常频次
declare @n_count22 int
declare @n_count23 int
declare @DAILY_TIMES int
declare @yl_min int
select @n_count22=count(1) from itf_hos_drug b where  b.IS_ANTIBAC='1' and drug_code =@yp_code and b.DRUG_PRODUCT_NAME like '%注射%'

select @DAILY_TIMES=b.DAILY_TIMES from t_pres_med a,ITF_HOS_FREQUENCY b
where
code =@code and
 a.his_code =@yp_code
and
a.freq=b.FREQ_CODE




select @yl_min=max(yl_min) from t_sda_cgl_result a,t_byyydzb b
where a.sda_id =b.sda_id
and b.yp_code=@yp_code
and a.reco_type='2'


if @hosp_flag='op'  AND @n_count22>=1 and @DAILY_TIMES <@yl_min
begin
	DELETE FROM t_pres_fx WHERE Code =@Code AND wtcode='RLT030' AND title LIKE '%频次%' and ywa=@tymc
	select @n_count23=count(1) from t_pres_fx a ,t_pres b where a.Code =b.code  and a.code =@Code
	if  @n_count21 =0
	begin
	update t_pres set flag =0 where code =@Code
	end
end


-- 距离上次开药时间间隔小于开药天数报错
-- 距离上次开药时间间隔小于开药天数报错
declare @ts_int int
declare @zdts decimal(14,2)
declare @sj_zdts decimal(14,2)
declare @sdate nvarchar(50)
declare @his_time nvarchar(50)
declare @his_time2 nvarchar(50)
declare @last_his_time nvarchar(50)
declare @last_code nvarchar(50)
declare @dept_name nvarchar(50)
declare @doct_name nvarchar(50)
declare @n_count24 int
declare @card_code nvarchar(50)
declare @pres_id nvarchar(50)

if  @hosp_flag ='ip' or @n_count6 >0
begin
return
end

select @card_code=card_code,@his_time=pres_time,@pres_id=pres_id from t_pres where code=@code
select @n_count24=COUNT(1)
from t_drug_zdfyl
where DRUG_CODE =@yp_code

/****张扬20240829注释，计划和历史相互作用合并***/
--if @n_count24 =0
--begin
--select @yp_name2=b.med_name,@dept_name=d.SPEC_NAME,@doct_name=a.doct_name,@his_time2=CONVERT(varchar(10),a.his_time,20)
--from t_pres a,t_pres_med b ,t_byyydzb c,ITF_HOS_SPEC d
--where a.code=b.Code
--and b.his_code =c.yp_code
--and a.dept_code =d.SPEC_CODE
--and pres_time >CONVERT(varchar(10),GETDATE()-1,20)
--and hosp_flag ='op'
--and flag >-1
--and flag <>9
--and c.CID =@mid
--and a.card_code =@card_code
--and a.pres_id <>@pres_id

--if @yp_name2 <>''
--begin
--insert into t_pres_fx select @code, @yp_name ,'','1','一般提示','RLT025','CFYYTS','重复用药','重复用药', @yp_name+': 该患者【'+@his_time2+'】已经在【'+@dept_name+'】开过此药品！ 医生为：'+@doct_name ,'0','药品用法用量'
--end
--end

/****张扬20240829注释，计划和历史相互作用合并***/


--if @n_count24 >0
--begin
--select top 1 @last_code=a.code,@dept_name=c.spec_name,
--@last_his_time=convert(nvarchar(20),a.pres_time,120) from
--t_pres a inner join t_pres_med b on b.Code=a.code
--inner join itf_hos_spec c on a.dept_code=c.SPEC_CODE
--where a.card_code=@card_code
--and a.pres_time<@his_time and a.flag>-1 and a.flag<>9
--and b.his_code=@yp_code
--order by pres_time desc
--select @sj_zdts=cast(ts as decimal(14,2)) from v_pres_med_ts2
--where Code=@last_code and his_code=@yp_code
----if(@sj_zdts_str is not null and @sj_zdts_str<>'')
----set @sj_zdts=cast(@sj_zdts as decimal(14,2))
--if( @n_count24>0 and  @sj_zdts>0 and @sj_zdts>DATEDIFF(day,@last_his_time,getdate()))
--begin
--insert into t_pres_fx select @code, @yp_name ,'','1','一般提示','RLT030','GYTJWT','药品用法用量','用法、用量不适宜', @yp_name+': 该患者上次开药尚未吃完！最近一次开'+@yp_name+'的时间为:'+@last_his_time+'，科室为：'+@dept_name+'。开药量为'+cast(@sj_zdts as nvarchar(50))+'天用量！','0','药品用法用量'
--end
--end

---历史处方相互作用

CREATE TABLE #t_xhzy_ls(
	[drug_name] [nvarchar](50) NULL,
	[dept_name] [nvarchar](50) NULL,
	[doct_name] [nvarchar](50) NULL,
	[mid] [nvarchar](50) NULL
)

insert into #t_xhzy_ls
select b.med_name,d.SPEC_NAME,a.doct_name,c.CID
from t_pres a,t_pres_med b ,t_byyydzb c,ITF_HOS_SPEC d
where a.code=b.Code
and b.his_code =c.yp_code
and a.dept_code =d.SPEC_CODE
and pres_time >CONVERT(varchar(10),GETDATE()-1,20)
and hosp_flag ='op'
and flag >-1
and flag <>9
and a.prescription_type ='1'
and a.card_code =@card_code
and a.pres_id <>@pres_id



insert into t_pres_fx
select  @code,@yp_name ywa, b.drug_name ywB,
--cast(a.result as varchar)  wtlvlcode,
'1' wtlvlcode,
'一般提示' wtlvl,
'RLT014' wtcode,
 'XHZYTS'   as wtsp,
 '相互作用'  AS wtname,
'【'+@yp_name+'】和【'+CONVERT(varchar(10),GETDATE(),20)+'】在'+b.dept_name+'的【'+b.doct_name+'】开的【'+ b.drug_name+'】存在相互作用'    as title,
'结果: '+ isnull(cast(a.effect as varchar(2000)),'')+'; 机制：' +isnull(CAST(mechanism as varchar(4000)),'')+';'+ isnull('参考文献:' +CAST(reference as varchar(4000)),'')   as detail,0,'配伍问题'
from t_xhzy_edi a,#t_xhzy_ls b
where   yaowuA=@mid and yaowuB=b.mid
and sugflag ='xhzy'
union
select  @code,@yp_name ywa, b.drug_name ywB,
--cast(a.result as varchar)  wtlvlcode,
'1' wtlvlcode,
'一般提示' wtlvl,
'RLT014' wtcode,
 'XHZYTS'   as wtsp,
 '相互作用'  AS wtname,
'【'+@yp_name+'】和【'+CONVERT(varchar(10),GETDATE(),20)+'】在'+b.dept_name+'的【'+b.doct_name+'】开的【'+ b.drug_name+'】存在相互作用'    as title,
'结果: '+ isnull(cast(a.effect as varchar(2000)),'')+'; 机制：' +isnull(CAST(mechanism as varchar(4000)),'')+';'+ isnull('参考文献:' +CAST(reference as varchar(4000)),'')    as detail,0,'配伍问题'
from t_xhzy_edi a,#t_xhzy_ls b
where   yaowuA=b.mid and yaowuB=@mid
and sugflag ='xhzy'









// MSSQL函数
CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_yuliu_med`(
		IN p_code VARCHAR(50),
    IN p_yp_code VARCHAR(60)
)
    COMMENT '药品余留/用药总量分析存储过程'
main_block: BEGIN
		-- 声明变量
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_yp_name VARCHAR(200);
		DECLARE v_yp_name2 VARCHAR(200);
		DECLARE v_yp_yyzl VARCHAR(50);    -- 用户自定义的用药总量最大值
		DECLARE v_ord_qty VARCHAR(50);    -- his传来的用药总量值
		DECLARE v_ord_uom VARCHAR(50);
		DECLARE v_n_count2 INT;
		DECLARE v_n_count11 INT;
		DECLARE v_ks VARCHAR(50);
		DECLARE v_hosp_flag VARCHAR(50);
		DECLARE v_value DECIMAL(10,2);
		DECLARE v_len_value INT;
		DECLARE v_pres_type2 VARCHAR(50);
		DECLARE v_n_count6 INT;
		DECLARE v_mid VARCHAR(50);
		DECLARE v_ksdm VARCHAR(20);
		DECLARE v_n_count INT;
		DECLARE v_n_count1 INT;
		DECLARE v_ysdm VARCHAR(20);
		DECLARE v_n_count5 INT;
		DECLARE v_n_count3 INT;
		DECLARE v_n_count36 INT;
		DECLARE v_n_count9 INT;
		DECLARE v_n_count10 INT;
		DECLARE v_n_count12 INT;
		DECLARE v_n_count13 INT;
		DECLARE v_n_count18 INT;
		DECLARE v_n_count19 INT;
		DECLARE v_tymc VARCHAR(50);
		DECLARE v_n_count20 INT;
		DECLARE v_n_count21 INT;
		DECLARE v_n_count22 INT;
		DECLARE v_n_count23 INT;
		DECLARE v_DAILY_TIMES INT;
		DECLARE v_yl_min INT;
		DECLARE v_ts_int INT;
		DECLARE v_zdts DECIMAL(14,2);
		DECLARE v_sj_zdts DECIMAL(14,2);
		DECLARE v_sdate VARCHAR(50);
		DECLARE v_his_time VARCHAR(50);
		DECLARE v_his_time2 VARCHAR(50);
		DECLARE v_last_his_time VARCHAR(50);
		DECLARE v_last_code VARCHAR(50);
		DECLARE v_dept_name VARCHAR(50);
		DECLARE v_doct_name VARCHAR(50);
		DECLARE v_n_count24 INT;
		DECLARE v_card_code VARCHAR(50);
		DECLARE v_pres_id VARCHAR(50);

		-- 声明异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				-- 如果出现异常，直接返回
				ROLLBACK;
		END;

		-- 检查药品是否为停用状态
		SELECT COUNT(1) INTO v_n_count6
		FROM rms_itf_hos_drug b
		WHERE b.DRUG_CODE = p_yp_code AND b.ZX_FLAG = '3';

		IF v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;

		-- 获取药品基本信息
		SELECT sda_id, cid INTO v_sda_id, v_mid
		FROM rms_t_byyydzb
		WHERE yp_code = p_yp_code LIMIT 1;

		-- 获取处方药品信息
		SELECT med_name, ord_qty, ord_uom INTO v_yp_name, v_ord_qty, v_ord_uom
		FROM rms_t_pres_med
		WHERE code = p_code AND his_code = p_yp_code LIMIT 1;

		-- 获取药品最大发药量
		SELECT zdfyl INTO v_yp_yyzl
		FROM rms_t_drug_zdfyl
		WHERE drug_code = p_yp_code AND unit = v_ord_uom LIMIT 1;

		-- 获取处方类型信息
		SELECT hosp_flag, pres_type INTO v_hosp_flag, v_pres_type2
		FROM rms_t_pres
		WHERE Code = p_code LIMIT 1;

		-- 门诊用药总量检查
		IF v_hosp_flag = 'op' AND (v_yp_yyzl != '' AND v_yp_yyzl IS NOT NULL) THEN
				SET v_value = CAST(v_yp_yyzl AS DECIMAL(10,2)) - CAST(v_ord_qty AS DECIMAL(10,2));
				IF v_value < 0 THEN
						INSERT INTO rms_t_pres_fx
						SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT047', 'yyzlwt', '用药总量', '用药总量问题',
								CONCAT(v_yp_name, '超出最大发药量,医院要求门诊发药最大量为', v_yp_yyzl, v_ord_uom), '0', '用药总量';
				END IF;
		END IF;

		-- 外用软膏最大发药量检查（不能超过5支）
		SELECT COUNT(1) INTO v_n_count36
		FROM rms_itf_hos_drug b
		WHERE b.DRUG_CODE = p_yp_code
		AND b.DRUG_FOR_NAME IN ('软膏剂', '乳膏剂', '凝胶(胶浆)剂', '眼膏剂', '茶剂', '滴眼剂');

		IF v_hosp_flag = 'op' AND v_n_count36 > 0 AND CAST(v_ord_qty AS DECIMAL(10,2)) > 5 THEN
				INSERT INTO rms_t_pres_fx
				SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT047', 'yyzlwt', '用药总量', '用药总量问题',
						CONCAT(v_yp_name, '超出最大发药量,医院要求门诊软膏、滴眼剂发药最大量为5支'), '0', '用药总量';
		END IF;

		-- 药品自定义科室检查
		SELECT COUNT(1) INTO v_n_count
		FROM rms_t_med_zdy_dept
		WHERE yp_code = p_yp_code;

		IF v_n_count > 0 THEN
				SELECT dept_code INTO v_ksdm
				FROM rms_t_pres
				WHERE code = p_code LIMIT 1;

				SELECT COUNT(1) INTO v_n_count1
				FROM rms_t_med_zdy_dept
				WHERE yp_code = p_yp_code AND dept_code = v_ksdm;

				IF v_n_count1 = 0 THEN
						INSERT INTO rms_t_pres_fx
						SELECT p_code, v_yp_name, '', '1', '严重警示', 'RLT048', 'yyks', '用药科室', '用药科室问题',
								CONCAT('医院规定：', v_yp_name, '不能在该科室使用！'), '0', '用药科室';
				END IF;
		END IF;

		-- 药品自定义医生检查
		SELECT COUNT(1) INTO v_n_count5
		FROM rms_t_med_zdy_doct
		WHERE yp_code = p_yp_code;

		IF v_n_count5 > 0 THEN
				SELECT doct_code INTO v_ysdm
				FROM rms_t_pres
				WHERE code = p_code LIMIT 1;

				SELECT COUNT(1) INTO v_n_count3
				FROM rms_t_med_zdy_doct
				WHERE yp_code = p_yp_code AND doct_code = v_ysdm;

				IF v_n_count3 = 0 THEN
						INSERT INTO rms_t_pres_fx
						SELECT p_code, v_yp_name, '', '2', '一般提示', 'RLT049', 'yyys', '用药医生', '用药医生问题',
								CONCAT(v_yp_name, '不能该医生使用！'), '0', '用药科室';
				END IF;
		END IF;

		-- 给药途径检查（取药用、遵医嘱、自用拦截）
		SELECT COUNT(1) INTO v_n_count9
		FROM rms_t_pres_med
		WHERE code = p_code
		AND administer IN (
				SELECT ADM_CODE
				FROM rms_itf_hos_admin_route
				WHERE jzbs = '1'
		);

		IF v_n_count9 > 0 THEN
				INSERT INTO rms_t_pres_fx
				SELECT p_code, v_yp_name, '', '2', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', '给药途径错误',
						'所有药品不能使用【取药用、遵医嘱、自用】给药途径！', '0', '给药途径错误';
		END IF;

		-- 自定义给药途径分析
		SELECT COUNT(1) INTO v_n_count12
		FROM rms_t_med_zdy_gytj
		WHERE yp_code = p_yp_code;

		SELECT COUNT(1) INTO v_n_count13
		FROM rms_t_pres_med
		WHERE code = p_code AND his_code = p_yp_code
		AND administer NOT IN (
				SELECT gytj_code
				FROM rms_t_med_zdy_gytj
				WHERE yp_code = p_yp_code
		);

		IF v_n_count12 > 0 AND v_n_count13 > 0 THEN
				INSERT INTO rms_t_pres_fx
				SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT026', 'GYTJWT', '给药途径错误', '给药途径问题',
						CONCAT(v_yp_name, '不符合医院规定给药途径！'), '0', '给药途径';
		END IF;

		-- 住院临时医嘱频次分析
		SELECT COUNT(1) INTO v_n_count18
		FROM rms_t_pres_med a, rms_t_pres b
		WHERE a.code = p_code AND a.Code = b.code
		AND freq IN ('01','13','15','14') AND his_code = p_yp_code;

		SELECT DRUG_NAME INTO v_tymc
		FROM rms_itf_hos_drug
		WHERE DRUG_CODE = p_yp_code LIMIT 1;

		IF v_hosp_flag = 'ip' AND v_pres_type2 = 'T' AND v_n_count18 >= 1 THEN
				DELETE FROM rms_t_pres_fx
				WHERE Code = p_code AND wtcode = 'RLT030'
				AND title LIKE '%频次%' AND ywa = v_tymc;

				SELECT COUNT(1) INTO v_n_count19
				FROM rms_t_pres_fx a, rms_t_pres b
				WHERE a.Code = b.code AND a.code = p_code;

				IF v_n_count19 = 0 THEN
						UPDATE rms_t_pres SET flag = 0 WHERE code = p_code;
				END IF;
		END IF;

		-- 门诊注射剂"立即使用"频次分析
		SELECT COUNT(1) INTO v_n_count20
		FROM rms_t_pres_med a
		WHERE a.code = p_code AND freq IN ('01','13','15','14')
		AND his_code = p_yp_code
		AND (a.med_name LIKE '%针%' OR a.med_name LIKE '%注射%');

		IF v_hosp_flag = 'op' AND v_n_count20 >= 1 THEN
				DELETE FROM rms_t_pres_fx
				WHERE Code = p_code AND wtcode = 'RLT030'
				AND title LIKE '%频次%' AND ywa = v_tymc;
		END IF;

		-- 门诊抗菌药物注射剂允许低于正常频次
		SELECT COUNT(1) INTO v_n_count22
		FROM rms_itf_hos_drug b
		WHERE b.IS_ANTIBAC = '1' AND drug_code = p_yp_code
		AND b.DRUG_PRODUCT_NAME LIKE '%注射%';

		SELECT b.DAILY_TIMES INTO v_DAILY_TIMES
		FROM rms_t_pres_med a, rms_itf_hos_frequency b
		WHERE code = p_code AND a.his_code = p_yp_code
		AND a.freq = b.FREQ_CODE LIMIT 1;

		SELECT MAX(yl_min) INTO v_yl_min
		FROM rms_t_sda_cgl_result a, rms_t_byyydzb b
		WHERE a.sda_id = b.sda_id
		AND b.yp_code = p_yp_code
		AND a.reco_type = '2';

		IF v_hosp_flag = 'op' AND v_n_count22 >= 1 AND v_DAILY_TIMES < v_yl_min THEN
				DELETE FROM rms_t_pres_fx
				WHERE Code = p_code AND wtcode = 'RLT030'
				AND title LIKE '%频次%' AND ywa = v_tymc;

				SELECT COUNT(1) INTO v_n_count23
				FROM rms_t_pres_fx a, rms_t_pres b
				WHERE a.Code = b.code AND a.code = p_code;

				IF v_n_count21 = 0 THEN
						UPDATE rms_t_pres SET flag = 0 WHERE code = p_code;
				END IF;
		END IF;

		-- 历史处方相互作用检查
		IF v_hosp_flag = 'ip' OR v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;

		SELECT card_code, pres_time, pres_id INTO v_card_code, v_his_time, v_pres_id
		FROM rms_t_pres
		WHERE code = p_code LIMIT 1;

		SELECT COUNT(1) INTO v_n_count24
		FROM rms_t_drug_zdfyl
		WHERE DRUG_CODE = p_yp_code;

		-- 创建临时表进行历史相互作用分析
		DROP TEMPORARY TABLE IF EXISTS temp_xhzy_ls;
		CREATE TEMPORARY TABLE temp_xhzy_ls (
				drug_name VARCHAR(50),
				dept_name VARCHAR(50),
				doct_name VARCHAR(50),
				mid VARCHAR(50)
		);

		INSERT INTO temp_xhzy_ls
		SELECT b.med_name, d.SPEC_NAME, a.doct_name, c.CID
		FROM rms_t_pres a, rms_t_pres_med b, rms_t_byyydzb c, rms_itf_hos_spec d
		WHERE a.code = b.Code
		AND b.his_code = c.yp_code
		AND a.dept_code = d.SPEC_CODE
		AND pres_time > DATE_SUB(CURDATE(), INTERVAL 1 DAY)
		AND hosp_flag = 'op'
		AND flag > -1
		AND flag <> 9
		AND a.prescription_type = '1'
		AND a.card_code = v_card_code
		AND a.pres_id <> v_pres_id;

		-- 插入相互作用警告
		INSERT INTO rms_t_pres_fx (
				Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		SELECT p_code, v_yp_name, b.drug_name, '1', '一般提示', 'RLT014', 'XHZYTS', '相互作用',
				CONCAT('【', v_yp_name, '】和【', CURDATE(), '】在', b.dept_name, '的【', b.doct_name, '】开的【', b.drug_name, '】存在相互作用'),
				CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), '; 机制：', IFNULL(CAST(mechanism AS CHAR(4000)), ''), ';', IFNULL(CONCAT('参考文献:', CAST(reference AS CHAR(4000))), '')),
				0, '配伍问题'
		FROM rms_t_xhzy_edi a, temp_xhzy_ls b
		WHERE yaowuA = v_mid AND yaowuB = b.mid
		AND sugflag = 'xhzy'
		UNION
		SELECT p_code, v_yp_name, b.drug_name, '1', '一般提示', 'RLT014', 'XHZYTS', '相互作用',
				CONCAT('【', v_yp_name, '】和【', CURDATE(), '】在', b.dept_name, '的【', b.doct_name, '】开的【', b.drug_name, '】存在相互作用'),
				CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), '; 机制：', IFNULL(CAST(mechanism AS CHAR(4000)), ''), ';', IFNULL(CONCAT('参考文献:', CAST(reference AS CHAR(4000))), '')),
				0, '配伍问题'
		FROM rms_t_xhzy_edi a, temp_xhzy_ls b
		WHERE yaowuA = b.mid AND yaowuB = v_mid
		AND sugflag = 'xhzy';

		DROP TEMPORARY TABLE temp_xhzy_ls;

END
