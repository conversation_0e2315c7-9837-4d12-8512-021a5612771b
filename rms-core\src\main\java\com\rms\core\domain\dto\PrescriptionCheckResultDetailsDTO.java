package com.rms.core.domain.dto;

import javax.validation.constraints.NotBlank;

/**
 * 获取处方审核结果详细信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class PrescriptionCheckResultDetailsDTO {

    /**
     * 门诊/住院标识：op-门诊；ip-住院
     */
    @NotBlank(message = "门诊/住院标识不能为空")
    private String hospFlag;

    /**
     * 就诊号
     */
    @NotBlank(message = "就诊号不能为空")
    private String treatCode;

    /**
     * 处方号
     */
    @NotBlank(message = "处方号不能为空")
    private String prescriptionId;

    public String getHospFlag() {
        return hospFlag;
    }

    public void setHospFlag(String hospFlag) {
        this.hospFlag = hospFlag;
    }

    public String getTreatCode() {
        return treatCode;
    }

    public void setTreatCode(String treatCode) {
        this.treatCode = treatCode;
    }

    public String getPrescriptionId() {
        return prescriptionId;
    }

    public void setPrescriptionId(String prescriptionId) {
        this.prescriptionId = prescriptionId;
    }

    @Override
    public String toString() {
        return "PrescriptionCheckResultDetailsDTO{" +
                "hospFlag='" + hospFlag + '\'' +
                ", treatCode='" + treatCode + '\'' +
                ", prescriptionId='" + prescriptionId + '\'' +
                '}';
    }
}
