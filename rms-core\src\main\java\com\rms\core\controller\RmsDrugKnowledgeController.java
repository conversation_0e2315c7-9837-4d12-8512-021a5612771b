package com.rms.core.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.core.service.IRmsDrugKnowledgeService;
import com.rms.core.service.IRmsTSdaAgeService;
import com.rms.core.domain.DrugQueryResult;
import com.rms.core.domain.RmsTSdaChd;
import com.rms.core.domain.RmsTSdaAge;
import com.rms.core.domain.RmsTSdaElder;
import com.rms.core.domain.RmsTSdaGestation;
import com.rms.core.domain.RmsTSdaSex;
import com.rms.core.domain.RmsItfHosDrug;
import com.rms.core.domain.RmsTXhzyEdi;
import com.rms.core.domain.RmsTXhzyEdiZy;
import com.rms.core.domain.RmsTSdaIcd10Info;
import com.rms.core.domain.RmsTSdaGm;

/**
 * 药品知识库Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/rms/drugknowledge")
public class RmsDrugKnowledgeController extends BaseController
{
    @Autowired
    private IRmsDrugKnowledgeService drugKnowledgeService;

    @Autowired
    private IRmsTSdaAgeService rmsTSdaAgeService;

    /**
     * 根据关键字查询药品
     */
    @GetMapping("/search")
    public AjaxResult searchDrugs(@RequestParam("keyword") String keyword)
    {
        List<DrugQueryResult> list = drugKnowledgeService.searchDrugs(keyword);
        return success(list);
    }

    /**
     * 根据sdaId查询儿童用药规则库信息
     */
    @GetMapping("/childRules/{sdaId}")
    public AjaxResult getChildDrugRules(@PathVariable("sdaId") Long sdaId)
    {
        List<RmsTSdaChd> list = drugKnowledgeService.getChildDrugRulesBySdaId(sdaId);
        return success(list);
    }

    /**
     * 新增儿童用药规则库
     */
    @PostMapping("/childRules")
    public AjaxResult addChildDrugRule(@RequestBody RmsTSdaChd rmsTSdaChd)
    {
        return toAjax(drugKnowledgeService.insertChildDrugRule(rmsTSdaChd));
    }

    /**
     * 修改儿童用药规则库
     */
    @PutMapping("/childRules")
    public AjaxResult editChildDrugRule(@RequestBody RmsTSdaChd rmsTSdaChd)
    {
        return toAjax(drugKnowledgeService.updateChildDrugRule(rmsTSdaChd));
    }

    /**
     * 删除儿童用药规则库
     */
    @DeleteMapping("/childRules/{id}")
    public AjaxResult removeChildDrugRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteChildDrugRule(id));
    }

    /**
     * 批量删除儿童用药规则库
     */
    @DeleteMapping("/childRules")
    public AjaxResult removeChildDrugRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteChildDrugRules(ids));
    }

    /**
     * 查询年龄代码列表
     */
    @GetMapping("/ageList")
    public AjaxResult getAgeList()
    {
        List<RmsTSdaAge> list = rmsTSdaAgeService.selectRmsTSdaAgeList(new RmsTSdaAge());
        return success(list);
    }

    /**
     * 根据sdaId查询老年人用药规则库信息
     */
    @GetMapping("/elderRules/{sdaId}")
    public AjaxResult getElderDrugRules(@PathVariable("sdaId") Long sdaId)
    {
        List<RmsTSdaElder> list = drugKnowledgeService.getElderDrugRulesBySdaId(sdaId);
        return success(list);
    }

    /**
     * 新增老年人用药规则库
     */
    @PostMapping("/elderRules")
    public AjaxResult addElderDrugRule(@RequestBody RmsTSdaElder rmsTSdaElder)
    {
        return toAjax(drugKnowledgeService.insertElderDrugRule(rmsTSdaElder));
    }

    /**
     * 修改老年人用药规则库
     */
    @PutMapping("/elderRules")
    public AjaxResult editElderDrugRule(@RequestBody RmsTSdaElder rmsTSdaElder)
    {
        return toAjax(drugKnowledgeService.updateElderDrugRule(rmsTSdaElder));
    }

    /**
     * 删除老年人用药规则库
     */
    @DeleteMapping("/elderRules/{id}")
    public AjaxResult removeElderDrugRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteElderDrugRule(id));
    }

    /**
     * 批量删除老年人用药规则库
     */
    @DeleteMapping("/elderRules")
    public AjaxResult removeElderDrugRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteElderDrugRules(ids));
    }

    /**
     * 根据sdaId查询孕妇用药规则库信息
     */
    @GetMapping("/pregnancyRules/{sdaId}")
    public AjaxResult getPregnancyDrugRules(@PathVariable("sdaId") Long sdaId)
    {
        List<RmsTSdaGestation> list = drugKnowledgeService.getPregnancyDrugRulesBySdaId(sdaId);
        return success(list);
    }

    /**
     * 新增孕妇用药规则库
     */
    @PostMapping("/pregnancyRules")
    public AjaxResult addPregnancyDrugRule(@RequestBody RmsTSdaGestation rmsTSdaGestation)
    {
        return toAjax(drugKnowledgeService.insertPregnancyDrugRule(rmsTSdaGestation));
    }

    /**
     * 修改孕妇用药规则库
     */
    @PutMapping("/pregnancyRules")
    public AjaxResult editPregnancyDrugRule(@RequestBody RmsTSdaGestation rmsTSdaGestation)
    {
        return toAjax(drugKnowledgeService.updatePregnancyDrugRule(rmsTSdaGestation));
    }

    /**
     * 删除孕妇用药规则库
     */
    @DeleteMapping("/pregnancyRules/{id}")
    public AjaxResult removePregnancyDrugRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deletePregnancyDrugRule(id));
    }

    /**
     * 批量删除孕妇用药规则库
     */
    @DeleteMapping("/pregnancyRules")
    public AjaxResult removePregnancyDrugRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deletePregnancyDrugRules(ids));
    }

    /**
     * 根据sdaId查询性别用药规则库信息
     */
    @GetMapping("/sexRules/{sdaId}")
    public AjaxResult getSexDrugRules(@PathVariable("sdaId") Long sdaId)
    {
        List<RmsTSdaSex> list = drugKnowledgeService.getSexDrugRulesBySdaId(sdaId);
        return success(list);
    }

    /**
     * 新增性别用药规则库
     */
    @PostMapping("/sexRules")
    public AjaxResult addSexDrugRule(@RequestBody RmsTSdaSex rmsTSdaSex)
    {
        return toAjax(drugKnowledgeService.insertSexDrugRule(rmsTSdaSex));
    }

    /**
     * 修改性别用药规则库
     */
    @PutMapping("/sexRules")
    public AjaxResult editSexDrugRule(@RequestBody RmsTSdaSex rmsTSdaSex)
    {
        return toAjax(drugKnowledgeService.updateSexDrugRule(rmsTSdaSex));
    }

    /**
     * 删除性别用药规则库
     */
    @DeleteMapping("/sexRules/{id}")
    public AjaxResult removeSexDrugRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteSexDrugRule(id));
    }

    /**
     * 批量删除性别用药规则库
     */
    @DeleteMapping("/sexRules")
    public AjaxResult removeSexDrugRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteSexDrugRules(ids));
    }

    /**
     * 获取所有给药途径基础数据
     */
    @GetMapping("/routeTypes")
    public AjaxResult getAllRouteTypes()
    {
        return success(drugKnowledgeService.getAllRouteTypes());
    }

    /**
     * 根据sdaId查询药品的给药途径信息
     */
    @GetMapping("/routeRules/{sdaId}")
    public AjaxResult getRouteDrugRules(@PathVariable("sdaId") Long sdaId)
    {
        return success(drugKnowledgeService.getRouteDrugRulesBySdaId(sdaId));
    }

    /**
     * 为药品添加给药途径
     */
    @PostMapping("/routeRules")
    public AjaxResult addRouteDrugRules(@RequestParam("sdaId") Long sdaId, @RequestBody String[] routeCodes)
    {
        return toAjax(drugKnowledgeService.addRouteDrugRules(sdaId, routeCodes));
    }

    /**
     * 删除药品的给药途径
     */
    @DeleteMapping("/routeRules")
    public AjaxResult removeRouteDrugRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteRouteDrugRules(ids));
    }

    /**
     * 根据drugCode查询药品标识信息
     */
    @GetMapping("/identity/{drugCode}")
    public AjaxResult getDrugIdentity(@PathVariable("drugCode") String drugCode)
    {
        return success(drugKnowledgeService.getDrugIdentityByDrugCode(drugCode));
    }

    /**
     * 更新药品标识信息
     */
    @PutMapping("/identity")
    public AjaxResult updateDrugIdentity(@RequestBody RmsItfHosDrug rmsItfHosDrug)
    {
        return toAjax(drugKnowledgeService.updateDrugIdentity(rmsItfHosDrug));
    }

    /**
     * 获取给药途径基础数据
     */
    @GetMapping("/routeBaseList")
    public AjaxResult getRouteBaseList()
    {
        return success(drugKnowledgeService.getRouteBaseList());
    }

    /**
     * 根据sdaId查询常规用量规则库信息
     */
    @GetMapping("/doseRules/{sdaId}")
    public AjaxResult getDoseRules(@PathVariable("sdaId") Long sdaId)
    {
        return success(drugKnowledgeService.getDoseRulesBySdaId(sdaId));
    }

    /**
     * 新增常规用量规则库
     */
    @PostMapping("/doseRules")
    public AjaxResult addDoseRule(@RequestBody java.util.Map<String, Object> params)
    {
        return toAjax(drugKnowledgeService.insertDoseRule(params));
    }

    /**
     * 修改常规用量规则库
     */
    @PutMapping("/doseRules")
    public AjaxResult editDoseRule(@RequestBody java.util.Map<String, Object> params)
    {
        return toAjax(drugKnowledgeService.updateDoseRule(params));
    }

    /**
     * 删除常规用量规则库
     */
    @DeleteMapping("/doseRules/{id}")
    public AjaxResult removeDoseRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteDoseRule(id));
    }

    /**
     * 批量删除常规用量规则库
     */
    @DeleteMapping("/doseRules")
    public AjaxResult removeDoseRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteDoseRules(ids));
    }

    // ========== 配伍禁忌相关接口 ==========

    /**
     * 根据sdaId查询配伍禁忌信息（西药）
     */
    @GetMapping("/incompatibleRules/{sdaId}")
    public AjaxResult getIncompatibleRules(@PathVariable("sdaId") Long sdaId)
    {
        return success(drugKnowledgeService.getIncompatibleRulesBySdaId(sdaId));
    }

    /**
     * 新增配伍禁忌规则（西药）
     */
    @PostMapping("/incompatibleRules")
    public AjaxResult addIncompatibleRule(@RequestBody RmsTXhzyEdi rmsTXhzyEdi)
    {
        return toAjax(drugKnowledgeService.insertIncompatibleRule(rmsTXhzyEdi));
    }

    /**
     * 修改配伍禁忌规则（西药）
     */
    @PutMapping("/incompatibleRules")
    public AjaxResult editIncompatibleRule(@RequestBody RmsTXhzyEdi rmsTXhzyEdi)
    {
        return toAjax(drugKnowledgeService.updateIncompatibleRule(rmsTXhzyEdi));
    }

    /**
     * 删除配伍禁忌规则（西药）
     */
    @DeleteMapping("/incompatibleRules/{id}")
    public AjaxResult removeIncompatibleRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteIncompatibleRule(id));
    }

    /**
     * 批量删除配伍禁忌规则（西药）
     */
    @DeleteMapping("/incompatibleRules")
    public AjaxResult removeIncompatibleRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteIncompatibleRules(ids));
    }

    /**
     * 根据sdaId查询配伍禁忌信息（中药）
     */
    @GetMapping("/incompatibleRulesZy/{sdaId}")
    public AjaxResult getIncompatibleRulesZy(@PathVariable("sdaId") Long sdaId)
    {
        return success(drugKnowledgeService.getIncompatibleRulesZyBySdaId(sdaId));
    }

    /**
     * 新增配伍禁忌规则（中药）
     */
    @PostMapping("/incompatibleRulesZy")
    public AjaxResult addIncompatibleRuleZy(@RequestBody RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return toAjax(drugKnowledgeService.insertIncompatibleRuleZy(rmsTXhzyEdiZy));
    }

    /**
     * 修改配伍禁忌规则（中药）
     */
    @PutMapping("/incompatibleRulesZy")
    public AjaxResult editIncompatibleRuleZy(@RequestBody RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return toAjax(drugKnowledgeService.updateIncompatibleRuleZy(rmsTXhzyEdiZy));
    }

    /**
     * 删除配伍禁忌规则（中药）
     */
    @DeleteMapping("/incompatibleRulesZy/{id}")
    public AjaxResult removeIncompatibleRuleZy(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteIncompatibleRuleZy(id));
    }

    /**
     * 批量删除配伍禁忌规则（中药）
     */
    @DeleteMapping("/incompatibleRulesZy")
    public AjaxResult removeIncompatibleRulesZy(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteIncompatibleRulesZy(ids));
    }

    // ========== 药物与诊断相关接口 ==========

    /**
     * 根据sdaId查询药物与诊断信息
     */
    @GetMapping("/diagnosisRules/{sdaId}")
    public AjaxResult getDiagnosisRules(@PathVariable("sdaId") Long sdaId)
    {
        return success(drugKnowledgeService.getDiagnosisRulesBySdaId(sdaId));
    }

    /**
     * 新增药物与诊断信息
     */
    @PostMapping("/diagnosisRules")
    public AjaxResult addDiagnosisRule(@RequestBody RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return toAjax(drugKnowledgeService.insertDiagnosisRule(rmsTSdaIcd10Info));
    }

    /**
     * 删除药物与诊断信息
     */
    @DeleteMapping("/diagnosisRules/{id}")
    public AjaxResult removeDiagnosisRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteDiagnosisRule(id));
    }

    /**
     * 批量删除药物与诊断信息
     */
    @DeleteMapping("/diagnosisRules")
    public AjaxResult removeDiagnosisRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteDiagnosisRules(ids));
    }

    /**
     * 根据关键字搜索ICD诊断信息
     */
    @GetMapping("/searchDiagnosis")
    public AjaxResult searchDiagnosis(@RequestParam("keyword") String keyword)
    {
        return success(drugKnowledgeService.searchDiagnosis(keyword));
    }

    // ========== 药物禁忌症相关接口 ==========

    /**
     * 根据sdaId查询药物禁忌症信息
     */
    @GetMapping("/contraindicationRules/{sdaId}")
    public AjaxResult getContraindicationRules(@PathVariable("sdaId") Long sdaId)
    {
        return success(drugKnowledgeService.getContraindicationRulesBySdaId(sdaId));
    }

    /**
     * 新增药物禁忌症信息
     */
    @PostMapping("/contraindicationRules")
    public AjaxResult addContraindicationRule(@RequestBody RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return toAjax(drugKnowledgeService.insertContraindicationRule(rmsTSdaIcd10Info));
    }

    /**
     * 删除药物禁忌症信息
     */
    @DeleteMapping("/contraindicationRules/{id}")
    public AjaxResult removeContraindicationRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteContraindicationRule(id));
    }

    /**
     * 批量删除药物禁忌症信息
     */
    @DeleteMapping("/contraindicationRules")
    public AjaxResult removeContraindicationRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteContraindicationRules(ids));
    }

    // ========== 过敏知识库相关接口 ==========

    /**
     * 获取所有过敏基础数据
     */
    @GetMapping("/allergyBaseList")
    public AjaxResult getAllergyBaseList()
    {
        return success(drugKnowledgeService.getAllergyBaseList());
    }

    /**
     * 根据sdaId查询药品过敏信息
     */
    @GetMapping("/allergyRules/{sdaId}")
    public AjaxResult getAllergyRules(@PathVariable("sdaId") Long sdaId)
    {
        return success(drugKnowledgeService.getAllergyRulesBySdaId(sdaId));
    }

    /**
     * 新增药品过敏信息
     */
    @PostMapping("/allergyRules")
    public AjaxResult addAllergyRule(@RequestBody RmsTSdaGm rmsTSdaGm)
    {
        return toAjax(drugKnowledgeService.insertAllergyRule(rmsTSdaGm));
    }

    /**
     * 修改药品过敏信息
     */
    @PutMapping("/allergyRules")
    public AjaxResult editAllergyRule(@RequestBody RmsTSdaGm rmsTSdaGm)
    {
        return toAjax(drugKnowledgeService.updateAllergyRule(rmsTSdaGm));
    }

    /**
     * 删除药品过敏信息
     */
    @DeleteMapping("/allergyRules/{id}")
    public AjaxResult removeAllergyRule(@PathVariable("id") Long id)
    {
        return toAjax(drugKnowledgeService.deleteAllergyRule(id));
    }

    /**
     * 批量删除药品过敏信息
     */
    @DeleteMapping("/allergyRules")
    public AjaxResult removeAllergyRules(@RequestBody Long[] ids)
    {
        return toAjax(drugKnowledgeService.deleteAllergyRules(ids));
    }

    /**
     * 根据关键字搜索过敏信息
     */
    @GetMapping("/searchAllergy")
    public AjaxResult searchAllergy(@RequestParam("keyword") String keyword)
    {
        return success(drugKnowledgeService.searchAllergy(keyword));
    }

    /**
     * 根据关键字搜索过敏基础数据
     */
    @GetMapping("/searchAllergyBase")
    public AjaxResult searchAllergyBase(@RequestParam("keyword") String keyword)
    {
        return success(drugKnowledgeService.searchAllergyBase(keyword));
    }
}
