<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsDrugKnowledgeMapper">

    <resultMap type="DrugQueryResult" id="DrugQueryResult">
        <result property="drugName"    column="drug_name"    />
        <result property="ypCode"    column="yp_code"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="drugManuf"    column="drug_manuf"    />
        <result property="drugSpec"    column="drug_spec"    />
        <result property="unitRem"    column="UnitRem"    />
    </resultMap>

    <!-- 根据关键字查询药品 -->
    <select id="searchDrugs" parameterType="String" resultMap="DrugQueryResult">
        select h.drug_name, d.yp_code, d.sda_id, h.DRUG_MANUF, h.DRUG_SPEC, s.UnitRem
        from rms_itf_hos_drug h
        INNER JOIN rms_t_byyydzb d on d.yp_code = h.drug_code
        LEFT JOIN rms_t_sda s on s.ID = d.sda_id
        <where>
            <if test="keyword != null and keyword != ''">
                and (h.drug_name like concat('%', #{keyword}, '%')
                or h.drug_sp like concat('%', #{keyword}, '%')
                or h.DRUG_PRODUCT_NAME like concat('%', #{keyword}, '%'))
            </if>
        </where>
        order by h.drug_name
    </select>

    <!-- 根据drugCode查询药品标识信息 -->
    <select id="getDrugIdentityByDrugCode" parameterType="String" resultType="RmsItfHosDrug">
        select h.drug_id as drugId,
               h.drug_code as drugCode,
               h.drug_name as drugName,
               h.drug_manuf as drugManuf,
               h.drug_spec as drugSpec,
               h.is_antibac as isAntibac,
               h.glucocorticoid as glucocorticoid,
               h.proton_pump_drug as protonPumpDrug,
               h.key_drug as keyDrug,
               h.blood_drug as bloodDrug,
               h.is_basic_drug as isBasicDrug,
               h.cancer_drug as cancerDrug,
               h.is_djm as isDjm,
               h.is_injection as isInjection,
               h.zx_flag as zxFlag,
               h.is_purchase_internet as isPurchaseInternet
        from rms_itf_hos_drug h
        where h.drug_code = #{drugCode}
    </select>

    <!-- 更新药品标识信息 -->
    <update id="updateDrugIdentity" parameterType="RmsItfHosDrug">
        update rms_itf_hos_drug
        set is_antibac = #{isAntibac},
            glucocorticoid = #{glucocorticoid},
            proton_pump_drug = #{protonPumpDrug},
            key_drug = #{keyDrug},
            blood_drug = #{bloodDrug},
            is_basic_drug = #{isBasicDrug},
            cancer_drug = #{cancerDrug},
            is_djm = #{isDjm},
            is_injection = #{isInjection},
            zx_flag = #{zxFlag},
            is_purchase_internet = #{isPurchaseInternet}
        where drug_code = #{drugCode}
    </update>

    <!-- 根据sdaId查询常规用量规则库信息 -->
    <select id="getDoseRulesBySdaId" parameterType="Long" resultType="java.util.Map">
        select c.id, c.age_min, c.age_max, c.admin_routine, r.reco_type, r.yl_min, r.yl_max, r.yl_unit
        from rms_t_sda_cgl_condition c
        inner join rms_t_sda_cgl_result r on r.condition_id = c.id
        where c.sda_id = #{sdaId}
        order by c.age_min, c.age_max
    </select>

    <!-- 新增常规用量条件表 -->
    <insert id="insertDoseCondition" parameterType="java.util.Map">
        <selectKey keyProperty="conditionId" resultType="Long" order="AFTER">
            select LAST_INSERT_ID()
        </selectKey>
        insert into rms_t_sda_cgl_condition (sda_id, age_min, age_max, admin_routine)
        values (#{sdaId}, #{ageMin}, #{ageMax}, #{adminRoutine})
    </insert>

    <!-- 新增常规用量结果表 -->
    <insert id="insertDoseResult" parameterType="java.util.Map">
        insert into rms_t_sda_cgl_result (condition_id, sda_id, reco_type, yl_min, yl_max, yl_unit)
        values (#{conditionId}, #{sdaId}, #{recoType}, #{ylMin}, #{ylMax}, #{ylUnit})
    </insert>

    <!-- 更新常规用量条件表 -->
    <update id="updateDoseCondition" parameterType="java.util.Map">
        update rms_t_sda_cgl_condition
        set age_min = #{ageMin}, age_max = #{ageMax}, admin_routine = #{adminRoutine}
        where id = #{id}
    </update>

    <!-- 更新常规用量结果表 -->
    <update id="updateDoseResult" parameterType="java.util.Map">
        update rms_t_sda_cgl_result
        set reco_type = #{recoType}, yl_min = #{ylMin}, yl_max = #{ylMax}, yl_unit = #{ylUnit}
        where condition_id = #{id}
    </update>

    <!-- 修改常规用量规则库 -->
    <update id="updateDoseRule" parameterType="java.util.Map">
        <!-- 这个方法现在在Service层面进行事务管理 -->
        update rms_t_sda_cgl_condition
        set age_min = #{ageMin}, age_max = #{ageMax}, admin_routine = #{adminRoutine}
        where id = #{id}
    </update>

    <!-- 删除常规用量结果表 -->
    <delete id="deleteDoseResult" parameterType="Long">
        delete from rms_t_sda_cgl_result where condition_id = #{conditionId}
    </delete>

    <!-- 删除常规用量条件表 -->
    <delete id="deleteDoseCondition" parameterType="Long">
        delete from rms_t_sda_cgl_condition where id = #{id}
    </delete>

    <!-- 删除常规用量规则库 -->
    <delete id="deleteDoseRule" parameterType="Long">
        <!-- 这个方法现在在Service层面进行事务管理 -->
        delete from rms_t_sda_cgl_condition where id = #{id}
    </delete>

    <!-- 批量删除常规用量结果表 -->
    <delete id="deleteDoseResults" parameterType="Long">
        delete from rms_t_sda_cgl_result where condition_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量删除常规用量条件表 -->
    <delete id="deleteDoseConditions" parameterType="Long">
        delete from rms_t_sda_cgl_condition where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量删除常规用量规则库 -->
    <delete id="deleteDoseRules" parameterType="Long">
        <!-- 这个方法现在在Service层面进行事务管理 -->
        delete from rms_t_sda_cgl_condition where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
