import request from '@/utils/request'

// 查询药品说明书列表
export function listSda(query) {
  return request({
    url: '/rms/sda/list',
    method: 'get',
    params: query
  })
}

// 查询药品说明书详细
export function getSda(ID) {
  return request({
    url: '/rms/sda/' + ID,
    method: 'get'
  })
}

// 新增药品说明书
export function addSda(data) {
  return request({
    url: '/rms/sda',
    method: 'post',
    data: data
  })
}

// 修改药品说明书
export function updateSda(data) {
  return request({
    url: '/rms/sda',
    method: 'put',
    data: data
  })
}

// 删除药品说明书
export function delSda(ID) {
  return request({
    url: '/rms/sda/' + ID,
    method: 'delete'
  })
}
