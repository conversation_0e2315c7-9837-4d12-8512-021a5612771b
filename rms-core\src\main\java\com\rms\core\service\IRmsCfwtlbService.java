package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.RmsCfwtlb;

/**
 * 处方问题类别Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IRmsCfwtlbService 
{
    /**
     * 查询处方问题类别
     * 
     * @param cfwtbh 处方问题类别主键
     * @return 处方问题类别
     */
    public RmsCfwtlb selectRmsCfwtlbByCfwtbh(String cfwtbh);

    /**
     * 查询处方问题类别列表
     * 
     * @param rmsCfwtlb 处方问题类别
     * @return 处方问题类别集合
     */
    public List<RmsCfwtlb> selectRmsCfwtlbList(RmsCfwtlb rmsCfwtlb);

    /**
     * 新增处方问题类别
     * 
     * @param rmsCfwtlb 处方问题类别
     * @return 结果
     */
    public int insertRmsCfwtlb(RmsCfwtlb rmsCfwtlb);

    /**
     * 修改处方问题类别
     * 
     * @param rmsCfwtlb 处方问题类别
     * @return 结果
     */
    public int updateRmsCfwtlb(RmsCfwtlb rmsCfwtlb);

    /**
     * 批量删除处方问题类别
     * 
     * @param cfwtbhs 需要删除的处方问题类别主键集合
     * @return 结果
     */
    public int deleteRmsCfwtlbByCfwtbhs(String[] cfwtbhs);

    /**
     * 删除处方问题类别信息
     * 
     * @param cfwtbh 处方问题类别主键
     * @return 结果
     */
    public int deleteRmsCfwtlbByCfwtbh(String cfwtbh);
}
