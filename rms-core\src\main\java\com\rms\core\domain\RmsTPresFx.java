package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 处方分析结果对象 rms_t_pres_fx
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public class RmsTPresFx extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 处方编码 */
    @Excel(name = "处方编码")
    private String code;

    /** 药物A */
    @Excel(name = "药物A")
    private String ywa;

    /** 药物B */
    @Excel(name = "药物B")
    private String ywb;

    /** 问题等级代码 */
    @Excel(name = "问题等级代码")
    private String wtlvlcode;

    /** 问题等级 */
    @Excel(name = "问题等级")
    private String wtlvl;

    /** 问题代码 */
    @Excel(name = "问题代码")
    private String wtcode;

    /** 问题属性 */
    @Excel(name = "问题属性")
    private String wtsp;

    /** 问题名称 */
    @Excel(name = "问题名称")
    private String wtname;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 详情 */
    @Excel(name = "详情")
    private String detail;

    /** 标志 */
    @Excel(name = "标志")
    private Long flag;

    /** 文本 */
    @Excel(name = "文本")
    private String text;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public void setYwa(String ywa)
    {
        this.ywa = ywa;
    }

    public String getYwa()
    {
        return ywa;
    }

    public void setYwb(String ywb)
    {
        this.ywb = ywb;
    }

    public String getYwb()
    {
        return ywb;
    }

    public void setWtlvlcode(String wtlvlcode)
    {
        this.wtlvlcode = wtlvlcode;
    }

    public String getWtlvlcode()
    {
        return wtlvlcode;
    }

    public void setWtlvl(String wtlvl)
    {
        this.wtlvl = wtlvl;
    }

    public String getWtlvl()
    {
        return wtlvl;
    }

    public void setWtcode(String wtcode)
    {
        this.wtcode = wtcode;
    }

    public String getWtcode()
    {
        return wtcode;
    }

    public void setWtsp(String wtsp)
    {
        this.wtsp = wtsp;
    }

    public String getWtsp()
    {
        return wtsp;
    }

    public void setWtname(String wtname)
    {
        this.wtname = wtname;
    }

    public String getWtname()
    {
        return wtname;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }

    public void setDetail(String detail)
    {
        this.detail = detail;
    }

    public String getDetail()
    {
        return detail;
    }

    public void setFlag(Long flag)
    {
        this.flag = flag;
    }

    public Long getFlag()
    {
        return flag;
    }

    public void setText(String text)
    {
        this.text = text;
    }

    public String getText()
    {
        return text;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("code", getCode())
            .append("ywa", getYwa())
            .append("ywb", getYwb())
            .append("wtlvlcode", getWtlvlcode())
            .append("wtlvl", getWtlvl())
            .append("wtcode", getWtcode())
            .append("wtsp", getWtsp())
            .append("wtname", getWtname())
            .append("title", getTitle())
            .append("detail", getDetail())
            .append("flag", getFlag())
            .append("text", getText())
            .append("createTime", getCreateTime())
            .toString();
    }
}
