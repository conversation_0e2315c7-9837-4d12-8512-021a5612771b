import request from '@/utils/request'

// 查询过敏基础信息列表
export function listTgmbase(query) {
  return request({
    url: '/rms/tgmbase/list',
    method: 'get',
    params: query
  })
}

// 查询过敏基础信息详细
export function getTgmbase(code) {
  return request({
    url: '/rms/tgmbase/' + code,
    method: 'get'
  })
}

// 新增过敏基础信息
export function addTgmbase(data) {
  return request({
    url: '/rms/tgmbase',
    method: 'post',
    data: data
  })
}

// 修改过敏基础信息
export function updateTgmbase(data) {
  return request({
    url: '/rms/tgmbase',
    method: 'put',
    data: data
  })
}

// 删除过敏基础信息
export function delTgmbase(code) {
  return request({
    url: '/rms/tgmbase/' + code,
    method: 'delete'
  })
}
