#!/bin/bash

# RMS API Demo 运行脚本 (Linux/Mac)
# 
# 使用说明：
# 1. 确保Java已安装并配置环境变量
# 2. 确保RMS系统已启动在 http://localhost:8080
# 3. 给脚本执行权限：chmod +x run_demo.sh
# 4. 运行脚本：./run_demo.sh

echo "================================================================================"
echo "RMS API Demo 运行脚本"
echo "================================================================================"

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误：未找到Java环境，请确保Java已正确安装并配置环境变量"
    exit 1
fi

echo "✅ Java环境检查通过"
java -version

# 编译Java文件
echo ""
echo "📦 正在编译 RmsApiDemo.java..."
if ! javac RmsApiDemo.java; then
    echo "❌ 编译失败，请检查代码是否有语法错误"
    exit 1
fi

echo "✅ 编译成功"

# 运行程序
echo ""
echo "🚀 正在运行 RMS API Demo..."
echo ""
java RmsApiDemo

# 清理编译文件
echo ""
echo "🧹 清理编译文件..."
rm -f RmsApiDemo.class

echo ""
echo "================================================================================"
echo "Demo 运行完成"
echo "================================================================================"
