<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaGestationMapper">
    
    <resultMap type="RmsTSdaGestation" id="RmsTSdaGestationResult">
        <result property="id"    column="id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="dayup"    column="dayup"    />
        <result property="daydown"    column="daydown"    />
        <result property="dwcode"    column="dwcode"    />
        <result property="dw"    column="dw"    />
        <result property="bs"    column="bs"    />
        <result property="remark"    column="remark"    />
        <result property="ispb"    column="ispb"    />
    </resultMap>

    <sql id="selectRmsTSdaGestationVo">
        select id, sda_id, dayup, daydown, dwcode, dw, bs, remark, ispb from rms_t_sda_gestation
    </sql>

    <select id="selectRmsTSdaGestationList" parameterType="RmsTSdaGestation" resultMap="RmsTSdaGestationResult">
        <include refid="selectRmsTSdaGestationVo"/>
        <where>  
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="dayup != null "> and dayup = #{dayup}</if>
            <if test="daydown != null "> and daydown = #{daydown}</if>
            <if test="dwcode != null  and dwcode != ''"> and dwcode = #{dwcode}</if>
            <if test="dw != null  and dw != ''"> and dw = #{dw}</if>
            <if test="bs != null  and bs != ''"> and bs = #{bs}</if>
            <if test="ispb != null  and ispb != ''"> and ispb = #{ispb}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaGestationById" parameterType="Long" resultMap="RmsTSdaGestationResult">
        <include refid="selectRmsTSdaGestationVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaGestation" parameterType="RmsTSdaGestation" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_sda_gestation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">sda_id,</if>
            <if test="dayup != null">dayup,</if>
            <if test="daydown != null">daydown,</if>
            <if test="dwcode != null">dwcode,</if>
            <if test="dw != null">dw,</if>
            <if test="bs != null">bs,</if>
            <if test="remark != null">remark,</if>
            <if test="ispb != null">ispb,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">#{sdaId},</if>
            <if test="dayup != null">#{dayup},</if>
            <if test="daydown != null">#{daydown},</if>
            <if test="dwcode != null">#{dwcode},</if>
            <if test="dw != null">#{dw},</if>
            <if test="bs != null">#{bs},</if>
            <if test="remark != null">#{remark},</if>
            <if test="ispb != null">#{ispb},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaGestation" parameterType="RmsTSdaGestation">
        update rms_t_sda_gestation
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="dayup != null">dayup = #{dayup},</if>
            <if test="daydown != null">daydown = #{daydown},</if>
            <if test="dwcode != null">dwcode = #{dwcode},</if>
            <if test="dw != null">dw = #{dw},</if>
            <if test="bs != null">bs = #{bs},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="ispb != null">ispb = #{ispb},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaGestationById" parameterType="Long">
        delete from rms_t_sda_gestation where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaGestationByIds" parameterType="String">
        delete from rms_t_sda_gestation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>