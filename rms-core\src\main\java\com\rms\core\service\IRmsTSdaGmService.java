package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.RmsTSdaGm;

/**
 * 药品过敏信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface IRmsTSdaGmService 
{
    /**
     * 查询药品过敏信息
     * 
     * @param id 药品过敏信息主键
     * @return 药品过敏信息
     */
    public RmsTSdaGm selectRmsTSdaGmById(Long id);

    /**
     * 查询药品过敏信息列表
     * 
     * @param rmsTSdaGm 药品过敏信息
     * @return 药品过敏信息集合
     */
    public List<RmsTSdaGm> selectRmsTSdaGmList(RmsTSdaGm rmsTSdaGm);

    /**
     * 新增药品过敏信息
     * 
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    public int insertRmsTSdaGm(RmsTSdaGm rmsTSdaGm);

    /**
     * 修改药品过敏信息
     * 
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    public int updateRmsTSdaGm(RmsTSdaGm rmsTSdaGm);

    /**
     * 批量删除药品过敏信息
     * 
     * @param ids 需要删除的药品过敏信息主键集合
     * @return 结果
     */
    public int deleteRmsTSdaGmByIds(Long[] ids);

    /**
     * 删除药品过敏信息信息
     * 
     * @param id 药品过敏信息主键
     * @return 结果
     */
    public int deleteRmsTSdaGmById(Long id);
}
