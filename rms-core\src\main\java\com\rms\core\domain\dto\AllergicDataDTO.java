package com.rms.core.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 过敏源信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class AllergicDataDTO {

    /**
     * 过敏类型：0-其他；1-联科药品大类；2=联科药品成份；3=医联过敏源；4=HIS过敏源；5= HIS药品代码；6=过敏体质
     * （目前仅支持1/2/5/6）
     */
    @NotNull(message = "过敏类型不能为空")
    private Integer type;

    /**
     * 过敏源名称
     */
    @NotBlank(message = "过敏源名称不能为空")
    private String name;

    /**
     * 过敏源代码
     */
    @NotBlank(message = "过敏源代码不能为空")
    private String gmdm;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return gmdm;
    }

    public void setCode(String code) {
        this.gmdm = code;
    }

    @Override
    public String toString() {
        return "AllergicDataDTO{" +
                "type=" + type +
                ", name='" + name + '\'' +
                ", gmdm='" + gmdm + '\'' +
                '}';
    }
}
