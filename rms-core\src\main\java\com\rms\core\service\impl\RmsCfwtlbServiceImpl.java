package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsCfwtlbMapper;
import com.rms.core.domain.RmsCfwtlb;
import com.rms.core.service.IRmsCfwtlbService;

/**
 * 处方问题类别Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class RmsCfwtlbServiceImpl implements IRmsCfwtlbService
{
    @Autowired
    private RmsCfwtlbMapper rmsCfwtlbMapper;

    /**
     * 查询处方问题类别
     *
     * @param cfwtbh 处方问题类别主键
     * @return 处方问题类别
     */
    @Override
    public RmsCfwtlb selectRmsCfwtlbByCfwtbh(String cfwtbh)
    {
        return rmsCfwtlbMapper.selectRmsCfwtlbByCfwtbh(cfwtbh);
    }

    /**
     * 查询处方问题类别列表
     *
     * @param rmsCfwtlb 处方问题类别
     * @return 处方问题类别
     */
    @Override
    public List<RmsCfwtlb> selectRmsCfwtlbList(RmsCfwtlb rmsCfwtlb)
    {
        return rmsCfwtlbMapper.selectRmsCfwtlbList(rmsCfwtlb);
    }

    /**
     * 新增处方问题类别
     *
     * @param rmsCfwtlb 处方问题类别
     * @return 结果
     */
    @Override
    public int insertRmsCfwtlb(RmsCfwtlb rmsCfwtlb)
    {
        return rmsCfwtlbMapper.insertRmsCfwtlb(rmsCfwtlb);
    }

    /**
     * 修改处方问题类别
     *
     * @param rmsCfwtlb 处方问题类别
     * @return 结果
     */
    @Override
    public int updateRmsCfwtlb(RmsCfwtlb rmsCfwtlb)
    {
        return rmsCfwtlbMapper.updateRmsCfwtlb(rmsCfwtlb);
    }

    /**
     * 批量删除处方问题类别
     *
     * @param cfwtbhs 需要删除的处方问题类别主键
     * @return 结果
     */
    @Override
    public int deleteRmsCfwtlbByCfwtbhs(String[] cfwtbhs)
    {
        return rmsCfwtlbMapper.deleteRmsCfwtlbByCfwtbhs(cfwtbhs);
    }

    /**
     * 删除处方问题类别信息
     *
     * @param cfwtbh 处方问题类别主键
     * @return 结果
     */
    @Override
    public int deleteRmsCfwtlbByCfwtbh(String cfwtbh)
    {
        return rmsCfwtlbMapper.deleteRmsCfwtlbByCfwtbh(cfwtbh);
    }
}
