package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 处方过敏信息对象 rms_t_pres_gm
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public class RmsTPresGm extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 处方编码 */
    @Excel(name = "处方编码")
    private String code;

    /** 过敏类型 */
    @Excel(name = "过敏类型")
    private Integer type;

    /** 过敏源名称 */
    @Excel(name = "过敏源名称")
    private String name;

    /** 过敏源代码 */
    @Excel(name = "过敏源代码")
    private String gmdm;

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public Integer getType()
    {
        return type;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }

    public void setGmdm(String gmdm)
    {
        this.gmdm = gmdm;
    }

    public String getGmdm()
    {
        return gmdm;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("type", getType())
            .append("name", getName())
            .append("gmdm", getGmdm())
            .toString();
    }
}
