package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 医院药品信息对象 rms_itf_hos_drug
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public class RmsItfHosDrug extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 药品ID */
    @Excel(name = "药品ID")
    private Long drugId;

    /** 药品编码 */
    private String drugCode;

    /** 药品规格 */
    @Excel(name = "药品规格")
    private String drugSpec;

    /** 包装规格 */
    @Excel(name = "包装规格")
    private String packingSpec;

    /** 包装单位 */
    @Excel(name = "包装单位")
    private String packingUom;

    /** 最小包装规格 */
    @Excel(name = "最小包装规格")
    private String packingMinSpec;

    /** 最小包装数量 */
    @Excel(name = "最小包装数量")
    private String packingMinQty;

    /** 药品数量 */
    @Excel(name = "药品数量")
    private String drugQty;

    /** 药品单位 */
    @Excel(name = "药品单位")
    private String drugUom;

    /** 生产厂家 */
    @Excel(name = "生产厂家")
    private String drugManuf;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String drugProductName;

    /** 药品名称 */
    @Excel(name = "药品名称")
    private String drugName;

    /** 药品简拼 */
    @Excel(name = "药品简拼")
    private String drugSp;

    /** 通用名 */
    @Excel(name = "通用名")
    private String drugGeneralName;

    /** 剂型名称 */
    @Excel(name = "剂型名称")
    private String drugForName;

    /** 是否抗菌药物（1-是，0-否） */
    @Excel(name = "是否抗菌药物", readConverterExp = "1=-是，0-否")
    private String isAntibac;

    /** 抗肿瘤类型 */
    @Excel(name = "抗肿瘤类型")
    private String antitumorType;

    /** 中西药标志（1-西药；2-中成药；3-草药） */
    @Excel(name = "中西药标志", readConverterExp = "1=-西药；2-中成药；3-草药")
    private String zxFlag;

    /** 是否基本药物（1-是，0-否） */
    @Excel(name = "是否基本药物", readConverterExp = "1=-是，0-否")
    private String isBasicDrug;

    /** 是否注射剂（1-是，0-否） */
    @Excel(name = "是否注射剂", readConverterExp = "1=-是，0-否")
    private String isInjection;

    /** 法规类型（管制类别） */
    @Excel(name = "法规类型", readConverterExp = "管=制类别")
    private String lawType;

    /** 药品类型 */
    @Excel(name = "药品类型")
    private String drugType;

    /** 抗菌药物分类（限制级、特殊级等） */
    @Excel(name = "抗菌药物分类", readConverterExp = "限=制级、特殊级等")
    private String antibacType;

    /** 是否网络采购（1-是，0-否） */
    @Excel(name = "是否网络采购", readConverterExp = "1=-是，0-否")
    private String isPurchaseInternet;

    /** 停用标志（1-停用，0-正常，用于药品状态检查） */
    @Excel(name = "停用标志", readConverterExp = "1=-停用，0-正常，用于药品状态检查")
    private String stopFlag;

    /** 停用日期 */
    @Excel(name = "停用日期")
    private String stopDate;

    /** 医保编码 */
    @Excel(name = "医保编码")
    private String medicareCode;

    /** 医保类型（甲类、乙类、丙类） */
    @Excel(name = "医保类型", readConverterExp = "甲=类、乙类、丙类")
    private String medicareType;

    /** 医保备注 */
    @Excel(name = "医保备注")
    private String medicareRemark;

    /** 批准文号 */
    @Excel(name = "批准文号")
    private String approvalCertif;

    /** 医院编码 */
    @Excel(name = "医院编码")
    private String hospCode;

    /** 导入日期 */
    @Excel(name = "导入日期")
    private String impDate;

    /** 门诊住院标志（op-门诊，ip-住院） */
    @Excel(name = "门诊住院标志", readConverterExp = "o=p-门诊，ip-住院")
    private String isOpIp;

    /** 是否中药（1-是，0-否） */
    @Excel(name = "是否中药", readConverterExp = "1=-是，0-否")
    private String isRm;

    /** 是否毒麻药品（1-是，0-否） */
    @Excel(name = "是否毒麻药品", readConverterExp = "1=-是，0-否")
    private String isDjm;

    /** 单价 */
    @Excel(name = "单价")
    private Long dj;

    /** 金额 */
    @Excel(name = "金额")
    private Long je;

    /** 抗肿瘤药物标识（1-是，0-否） */
    @Excel(name = "抗肿瘤药物标识", readConverterExp = "1=-是，0-否")
    private String cancerDrug;

    /** 感冒药标识（1-是，0-否） */
    @Excel(name = "感冒药标识", readConverterExp = "1=-是，0-否")
    private String coldDrug;

    /** 质子泵抑制剂标识（1-是，0-否） */
    @Excel(name = "质子泵抑制剂标识", readConverterExp = "1=-是，0-否")
    private String protonPumpDrug;

    /** 糖皮质激素标识（1-是，0-否） */
    @Excel(name = "糖皮质激素标识", readConverterExp = "1=-是，0-否")
    private String glucocorticoid;

    /** 重点监控药品标识（1-是，0-否） */
    @Excel(name = "重点监控药品标识", readConverterExp = "1=-是，0-否")
    private String keyDrug;

    /** 血液制品标识（1-是，0-否） */
    @Excel(name = "血液制品标识", readConverterExp = "1=-是，0-否")
    private String bloodDrug;

    /** 精神麻醉药品标识（1-是，0-否） */
    @Excel(name = "精神麻醉药品标识", readConverterExp = "1=-是，0-否")
    private String spiritAnaesthesiaDrug;

    /** 是否重点监控药品（1-是，0-否） */
    @Excel(name = "是否重点监控药品", readConverterExp = "1=-是，0-否")
    private String isZdjk;

    /** 贵重药品标识（1-是，0-否） */
    @Excel(name = "贵重药品标识", readConverterExp = "1=-是，0-否")
    private String expensiveDrug;

    /** 是否特殊用药（1-是，0-否） */
    @Excel(name = "是否特殊用药", readConverterExp = "1=-是，0-否")
    private String isTsyp;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String gt20;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String gt21;

    /** 是否需要监测（1-需要，0-不需要，如血药浓度监测） */
    @Excel(name = "是否需要监测", readConverterExp = "1=-需要，0-不需要，如血药浓度监测")
    private String isJc;

    public void setDrugId(Long drugId)
    {
        this.drugId = drugId;
    }

    public Long getDrugId()
    {
        return drugId;
    }

    public void setDrugCode(String drugCode)
    {
        this.drugCode = drugCode;
    }

    public String getDrugCode()
    {
        return drugCode;
    }

    public void setDrugSpec(String drugSpec)
    {
        this.drugSpec = drugSpec;
    }

    public String getDrugSpec()
    {
        return drugSpec;
    }

    public void setPackingSpec(String packingSpec)
    {
        this.packingSpec = packingSpec;
    }

    public String getPackingSpec()
    {
        return packingSpec;
    }

    public void setPackingUom(String packingUom)
    {
        this.packingUom = packingUom;
    }

    public String getPackingUom()
    {
        return packingUom;
    }

    public void setPackingMinSpec(String packingMinSpec)
    {
        this.packingMinSpec = packingMinSpec;
    }

    public String getPackingMinSpec()
    {
        return packingMinSpec;
    }

    public void setPackingMinQty(String packingMinQty)
    {
        this.packingMinQty = packingMinQty;
    }

    public String getPackingMinQty()
    {
        return packingMinQty;
    }

    public void setDrugQty(String drugQty)
    {
        this.drugQty = drugQty;
    }

    public String getDrugQty()
    {
        return drugQty;
    }

    public void setDrugUom(String drugUom)
    {
        this.drugUom = drugUom;
    }

    public String getDrugUom()
    {
        return drugUom;
    }

    public void setDrugManuf(String drugManuf)
    {
        this.drugManuf = drugManuf;
    }

    public String getDrugManuf()
    {
        return drugManuf;
    }

    public void setDrugProductName(String drugProductName)
    {
        this.drugProductName = drugProductName;
    }

    public String getDrugProductName()
    {
        return drugProductName;
    }

    public void setDrugName(String drugName)
    {
        this.drugName = drugName;
    }

    public String getDrugName()
    {
        return drugName;
    }

    public void setDrugSp(String drugSp)
    {
        this.drugSp = drugSp;
    }

    public String getDrugSp()
    {
        return drugSp;
    }

    public void setDrugGeneralName(String drugGeneralName)
    {
        this.drugGeneralName = drugGeneralName;
    }

    public String getDrugGeneralName()
    {
        return drugGeneralName;
    }

    public void setDrugForName(String drugForName)
    {
        this.drugForName = drugForName;
    }

    public String getDrugForName()
    {
        return drugForName;
    }

    public void setIsAntibac(String isAntibac)
    {
        this.isAntibac = isAntibac;
    }

    public String getIsAntibac()
    {
        return isAntibac;
    }

    public void setAntitumorType(String antitumorType)
    {
        this.antitumorType = antitumorType;
    }

    public String getAntitumorType()
    {
        return antitumorType;
    }

    public void setZxFlag(String zxFlag)
    {
        this.zxFlag = zxFlag;
    }

    public String getZxFlag()
    {
        return zxFlag;
    }

    public void setIsBasicDrug(String isBasicDrug)
    {
        this.isBasicDrug = isBasicDrug;
    }

    public String getIsBasicDrug()
    {
        return isBasicDrug;
    }

    public void setIsInjection(String isInjection)
    {
        this.isInjection = isInjection;
    }

    public String getIsInjection()
    {
        return isInjection;
    }

    public void setLawType(String lawType)
    {
        this.lawType = lawType;
    }

    public String getLawType()
    {
        return lawType;
    }

    public void setDrugType(String drugType)
    {
        this.drugType = drugType;
    }

    public String getDrugType()
    {
        return drugType;
    }

    public void setAntibacType(String antibacType)
    {
        this.antibacType = antibacType;
    }

    public String getAntibacType()
    {
        return antibacType;
    }

    public void setIsPurchaseInternet(String isPurchaseInternet)
    {
        this.isPurchaseInternet = isPurchaseInternet;
    }

    public String getIsPurchaseInternet()
    {
        return isPurchaseInternet;
    }

    public void setStopFlag(String stopFlag)
    {
        this.stopFlag = stopFlag;
    }

    public String getStopFlag()
    {
        return stopFlag;
    }

    public void setStopDate(String stopDate)
    {
        this.stopDate = stopDate;
    }

    public String getStopDate()
    {
        return stopDate;
    }

    public void setMedicareCode(String medicareCode)
    {
        this.medicareCode = medicareCode;
    }

    public String getMedicareCode()
    {
        return medicareCode;
    }

    public void setMedicareType(String medicareType)
    {
        this.medicareType = medicareType;
    }

    public String getMedicareType()
    {
        return medicareType;
    }

    public void setMedicareRemark(String medicareRemark)
    {
        this.medicareRemark = medicareRemark;
    }

    public String getMedicareRemark()
    {
        return medicareRemark;
    }

    public void setApprovalCertif(String approvalCertif)
    {
        this.approvalCertif = approvalCertif;
    }

    public String getApprovalCertif()
    {
        return approvalCertif;
    }

    public void setHospCode(String hospCode)
    {
        this.hospCode = hospCode;
    }

    public String getHospCode()
    {
        return hospCode;
    }

    public void setImpDate(String impDate)
    {
        this.impDate = impDate;
    }

    public String getImpDate()
    {
        return impDate;
    }

    public void setIsOpIp(String isOpIp)
    {
        this.isOpIp = isOpIp;
    }

    public String getIsOpIp()
    {
        return isOpIp;
    }

    public void setIsRm(String isRm)
    {
        this.isRm = isRm;
    }

    public String getIsRm()
    {
        return isRm;
    }

    public void setIsDjm(String isDjm)
    {
        this.isDjm = isDjm;
    }

    public String getIsDjm()
    {
        return isDjm;
    }

    public void setDj(Long dj)
    {
        this.dj = dj;
    }

    public Long getDj()
    {
        return dj;
    }

    public void setJe(Long je)
    {
        this.je = je;
    }

    public Long getJe()
    {
        return je;
    }

    public void setCancerDrug(String cancerDrug)
    {
        this.cancerDrug = cancerDrug;
    }

    public String getCancerDrug()
    {
        return cancerDrug;
    }

    public void setColdDrug(String coldDrug)
    {
        this.coldDrug = coldDrug;
    }

    public String getColdDrug()
    {
        return coldDrug;
    }

    public void setProtonPumpDrug(String protonPumpDrug)
    {
        this.protonPumpDrug = protonPumpDrug;
    }

    public String getProtonPumpDrug()
    {
        return protonPumpDrug;
    }

    public void setGlucocorticoid(String glucocorticoid)
    {
        this.glucocorticoid = glucocorticoid;
    }

    public String getGlucocorticoid()
    {
        return glucocorticoid;
    }

    public void setKeyDrug(String keyDrug)
    {
        this.keyDrug = keyDrug;
    }

    public String getKeyDrug()
    {
        return keyDrug;
    }

    public void setBloodDrug(String bloodDrug)
    {
        this.bloodDrug = bloodDrug;
    }

    public String getBloodDrug()
    {
        return bloodDrug;
    }

    public void setSpiritAnaesthesiaDrug(String spiritAnaesthesiaDrug)
    {
        this.spiritAnaesthesiaDrug = spiritAnaesthesiaDrug;
    }

    public String getSpiritAnaesthesiaDrug()
    {
        return spiritAnaesthesiaDrug;
    }

    public void setIsZdjk(String isZdjk)
    {
        this.isZdjk = isZdjk;
    }

    public String getIsZdjk()
    {
        return isZdjk;
    }

    public void setExpensiveDrug(String expensiveDrug)
    {
        this.expensiveDrug = expensiveDrug;
    }

    public String getExpensiveDrug()
    {
        return expensiveDrug;
    }

    public void setIsTsyp(String isTsyp)
    {
        this.isTsyp = isTsyp;
    }

    public String getIsTsyp()
    {
        return isTsyp;
    }

    public void setGt20(String gt20)
    {
        this.gt20 = gt20;
    }

    public String getGt20()
    {
        return gt20;
    }

    public void setGt21(String gt21)
    {
        this.gt21 = gt21;
    }

    public String getGt21()
    {
        return gt21;
    }

    public void setIsJc(String isJc)
    {
        this.isJc = isJc;
    }

    public String getIsJc()
    {
        return isJc;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("drugId", getDrugId())
            .append("drugCode", getDrugCode())
            .append("drugSpec", getDrugSpec())
            .append("packingSpec", getPackingSpec())
            .append("packingUom", getPackingUom())
            .append("packingMinSpec", getPackingMinSpec())
            .append("packingMinQty", getPackingMinQty())
            .append("drugQty", getDrugQty())
            .append("drugUom", getDrugUom())
            .append("drugManuf", getDrugManuf())
            .append("drugProductName", getDrugProductName())
            .append("drugName", getDrugName())
            .append("drugSp", getDrugSp())
            .append("drugGeneralName", getDrugGeneralName())
            .append("drugForName", getDrugForName())
            .append("isAntibac", getIsAntibac())
            .append("antitumorType", getAntitumorType())
            .append("zxFlag", getZxFlag())
            .append("isBasicDrug", getIsBasicDrug())
            .append("isInjection", getIsInjection())
            .append("lawType", getLawType())
            .append("drugType", getDrugType())
            .append("antibacType", getAntibacType())
            .append("isPurchaseInternet", getIsPurchaseInternet())
            .append("stopFlag", getStopFlag())
            .append("stopDate", getStopDate())
            .append("medicareCode", getMedicareCode())
            .append("medicareType", getMedicareType())
            .append("medicareRemark", getMedicareRemark())
            .append("approvalCertif", getApprovalCertif())
            .append("hospCode", getHospCode())
            .append("impDate", getImpDate())
            .append("isOpIp", getIsOpIp())
            .append("isRm", getIsRm())
            .append("isDjm", getIsDjm())
            .append("dj", getDj())
            .append("je", getJe())
            .append("cancerDrug", getCancerDrug())
            .append("coldDrug", getColdDrug())
            .append("protonPumpDrug", getProtonPumpDrug())
            .append("glucocorticoid", getGlucocorticoid())
            .append("keyDrug", getKeyDrug())
            .append("bloodDrug", getBloodDrug())
            .append("spiritAnaesthesiaDrug", getSpiritAnaesthesiaDrug())
            .append("isZdjk", getIsZdjk())
            .append("expensiveDrug", getExpensiveDrug())
            .append("isTsyp", getIsTsyp())
            .append("gt20", getGt20())
            .append("gt21", getGt21())
            .append("isJc", getIsJc())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
