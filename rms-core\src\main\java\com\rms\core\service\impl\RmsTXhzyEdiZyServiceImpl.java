package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTXhzyEdiZyMapper;
import com.rms.core.domain.RmsTXhzyEdiZy;
import com.rms.core.service.IRmsTXhzyEdiZyService;

/**
 * 中药相互作用Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class RmsTXhzyEdiZyServiceImpl implements IRmsTXhzyEdiZyService
{
    @Autowired
    private RmsTXhzyEdiZyMapper rmsTXhzyEdiZyMapper;

    /**
     * 查询中药相互作用
     *
     * @param id 中药相互作用主键
     * @return 中药相互作用
     */
    @Override
    public RmsTXhzyEdiZy selectRmsTXhzyEdiZyById(Long id)
    {
        return rmsTXhzyEdiZyMapper.selectRmsTXhzyEdiZyById(id);
    }

    /**
     * 查询中药相互作用列表
     *
     * @param rmsTXhzyEdiZy 中药相互作用
     * @return 中药相互作用
     */
    @Override
    public List<RmsTXhzyEdiZy> selectRmsTXhzyEdiZyList(RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return rmsTXhzyEdiZyMapper.selectRmsTXhzyEdiZyList(rmsTXhzyEdiZy);
    }

    /**
     * 新增中药相互作用
     *
     * @param rmsTXhzyEdiZy 中药相互作用
     * @return 结果
     */
    @Override
    public int insertRmsTXhzyEdiZy(RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return rmsTXhzyEdiZyMapper.insertRmsTXhzyEdiZy(rmsTXhzyEdiZy);
    }

    /**
     * 修改中药相互作用
     *
     * @param rmsTXhzyEdiZy 中药相互作用
     * @return 结果
     */
    @Override
    public int updateRmsTXhzyEdiZy(RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return rmsTXhzyEdiZyMapper.updateRmsTXhzyEdiZy(rmsTXhzyEdiZy);
    }

    /**
     * 批量删除中药相互作用
     *
     * @param ids 需要删除的中药相互作用主键
     * @return 结果
     */
    @Override
    public int deleteRmsTXhzyEdiZyByIds(Long[] ids)
    {
        return rmsTXhzyEdiZyMapper.deleteRmsTXhzyEdiZyByIds(ids);
    }

    /**
     * 删除中药相互作用信息
     *
     * @param id 中药相互作用主键
     * @return 结果
     */
    @Override
    public int deleteRmsTXhzyEdiZyById(Long id)
    {
        return rmsTXhzyEdiZyMapper.deleteRmsTXhzyEdiZyById(id);
    }
}
