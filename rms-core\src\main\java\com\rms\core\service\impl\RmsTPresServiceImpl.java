package com.rms.core.service.impl;

import java.util.List;
import java.util.Map;
import com.rms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTPresMapper;
import com.rms.core.domain.RmsTPres;
import com.rms.core.service.IRmsTPresService;

/**
 * 处方信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
public class RmsTPresServiceImpl implements IRmsTPresService
{
    @Autowired
    private RmsTPresMapper rmsTPresMapper;

    /**
     * 查询处方信息
     *
     * @param code 处方信息主键
     * @return 处方信息
     */
    @Override
    public RmsTPres selectRmsTPresByCode(String code)
    {
        return rmsTPresMapper.selectRmsTPresByCode(code);
    }

    /**
     * 查询处方信息列表
     *
     * @param rmsTPres 处方信息
     * @return 处方信息
     */
    @Override
    public List<RmsTPres> selectRmsTPresList(RmsTPres rmsTPres)
    {
        return rmsTPresMapper.selectRmsTPresList(rmsTPres);
    }

    /**
     * 新增处方信息
     *
     * @param rmsTPres 处方信息
     * @return 结果
     */
    @Override
    public int insertRmsTPres(RmsTPres rmsTPres)
    {
        rmsTPres.setCreateTime(DateUtils.getNowDate());
        return rmsTPresMapper.insertRmsTPres(rmsTPres);
    }

    /**
     * 修改处方信息
     *
     * @param rmsTPres 处方信息
     * @return 结果
     */
    @Override
    public int updateRmsTPres(RmsTPres rmsTPres)
    {
        rmsTPres.setUpdateTime(DateUtils.getNowDate());
        return rmsTPresMapper.updateRmsTPres(rmsTPres);
    }

    /**
     * 批量删除处方信息
     *
     * @param codes 需要删除的处方信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresByCodes(String[] codes)
    {
        return rmsTPresMapper.deleteRmsTPresByCodes(codes);
    }

    /**
     * 删除处方信息信息
     *
     * @param code 处方信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresByCode(String code)
    {
        return rmsTPresMapper.deleteRmsTPresByCode(code);
    }

    /**
     * 批量更新处方状态
     *
     * @param codes 处方编码数组
     * @param flag 新的状态标志
     * @return 更新的记录数
     */
    @Override
    public int batchUpdatePrescriptionStatus(String[] codes, Long flag)
    {
        return rmsTPresMapper.batchUpdatePrescriptionStatus(codes, flag);
    }

    /**
     * 查询所有有处方的科室
     *
     * @return 科室列表
     */
    @Override
    public List<Map<String, Object>> selectDistinctDepartments()
    {
        return rmsTPresMapper.selectDistinctDepartments();
    }
}
