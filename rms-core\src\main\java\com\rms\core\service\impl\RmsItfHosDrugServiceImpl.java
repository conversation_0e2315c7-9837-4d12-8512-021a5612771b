package com.rms.core.service.impl;

import java.util.List;
import com.rms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsItfHosDrugMapper;
import com.rms.core.domain.RmsItfHosDrug;
import com.rms.core.service.IRmsItfHosDrugService;

/**
 * 医院药品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class RmsItfHosDrugServiceImpl implements IRmsItfHosDrugService
{
    @Autowired
    private RmsItfHosDrugMapper rmsItfHosDrugMapper;

    /**
     * 查询医院药品信息
     *
     * @param drugCode 医院药品信息主键
     * @return 医院药品信息
     */
    @Override
    public RmsItfHosDrug selectRmsItfHosDrugByDrugCode(String drugCode)
    {
        return rmsItfHosDrugMapper.selectRmsItfHosDrugByDrugCode(drugCode);
    }

    /**
     * 查询医院药品信息列表
     *
     * @param rmsItfHosDrug 医院药品信息
     * @return 医院药品信息
     */
    @Override
    public List<RmsItfHosDrug> selectRmsItfHosDrugList(RmsItfHosDrug rmsItfHosDrug)
    {
        return rmsItfHosDrugMapper.selectRmsItfHosDrugList(rmsItfHosDrug);
    }

    /**
     * 新增医院药品信息
     *
     * @param rmsItfHosDrug 医院药品信息
     * @return 结果
     */
    @Override
    public int insertRmsItfHosDrug(RmsItfHosDrug rmsItfHosDrug)
    {
        rmsItfHosDrug.setCreateTime(DateUtils.getNowDate());
        return rmsItfHosDrugMapper.insertRmsItfHosDrug(rmsItfHosDrug);
    }

    /**
     * 修改医院药品信息
     *
     * @param rmsItfHosDrug 医院药品信息
     * @return 结果
     */
    @Override
    public int updateRmsItfHosDrug(RmsItfHosDrug rmsItfHosDrug)
    {
        rmsItfHosDrug.setUpdateTime(DateUtils.getNowDate());
        return rmsItfHosDrugMapper.updateRmsItfHosDrug(rmsItfHosDrug);
    }

    /**
     * 批量删除医院药品信息
     *
     * @param drugCodes 需要删除的医院药品信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsItfHosDrugByDrugCodes(String[] drugCodes)
    {
        return rmsItfHosDrugMapper.deleteRmsItfHosDrugByDrugCodes(drugCodes);
    }

    /**
     * 删除医院药品信息信息
     *
     * @param drugCode 医院药品信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsItfHosDrugByDrugCode(String drugCode)
    {
        return rmsItfHosDrugMapper.deleteRmsItfHosDrugByDrugCode(drugCode);
    }
}
