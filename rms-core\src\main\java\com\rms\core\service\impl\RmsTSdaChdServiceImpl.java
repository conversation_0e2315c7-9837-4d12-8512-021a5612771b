package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaChdMapper;
import com.rms.core.domain.RmsTSdaChd;
import com.rms.core.service.IRmsTSdaChdService;

/**
 * 儿童用药规则库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class RmsTSdaChdServiceImpl implements IRmsTSdaChdService
{
    @Autowired
    private RmsTSdaChdMapper rmsTSdaChdMapper;

    /**
     * 查询儿童用药规则库
     *
     * @param id 儿童用药规则库主键
     * @return 儿童用药规则库
     */
    @Override
    public RmsTSdaChd selectRmsTSdaChdById(Long id)
    {
        return rmsTSdaChdMapper.selectRmsTSdaChdById(id);
    }

    /**
     * 查询儿童用药规则库列表
     *
     * @param rmsTSdaChd 儿童用药规则库
     * @return 儿童用药规则库
     */
    @Override
    public List<RmsTSdaChd> selectRmsTSdaChdList(RmsTSdaChd rmsTSdaChd)
    {
        return rmsTSdaChdMapper.selectRmsTSdaChdList(rmsTSdaChd);
    }

    /**
     * 新增儿童用药规则库
     *
     * @param rmsTSdaChd 儿童用药规则库
     * @return 结果
     */
    @Override
    public int insertRmsTSdaChd(RmsTSdaChd rmsTSdaChd)
    {
        return rmsTSdaChdMapper.insertRmsTSdaChd(rmsTSdaChd);
    }

    /**
     * 修改儿童用药规则库
     *
     * @param rmsTSdaChd 儿童用药规则库
     * @return 结果
     */
    @Override
    public int updateRmsTSdaChd(RmsTSdaChd rmsTSdaChd)
    {
        return rmsTSdaChdMapper.updateRmsTSdaChd(rmsTSdaChd);
    }

    /**
     * 批量删除儿童用药规则库
     *
     * @param ids 需要删除的儿童用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaChdByIds(Long[] ids)
    {
        return rmsTSdaChdMapper.deleteRmsTSdaChdByIds(ids);
    }

    /**
     * 删除儿童用药规则库信息
     *
     * @param id 儿童用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaChdById(Long id)
    {
        return rmsTSdaChdMapper.deleteRmsTSdaChdById(id);
    }
}
