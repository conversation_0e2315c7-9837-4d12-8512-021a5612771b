package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsItfHosDrug;

/**
 * 医院药品信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface RmsItfHosDrugMapper 
{
    /**
     * 查询医院药品信息
     * 
     * @param drugCode 医院药品信息主键
     * @return 医院药品信息
     */
    public RmsItfHosDrug selectRmsItfHosDrugByDrugCode(String drugCode);

    /**
     * 查询医院药品信息列表
     * 
     * @param rmsItfHosDrug 医院药品信息
     * @return 医院药品信息集合
     */
    public List<RmsItfHosDrug> selectRmsItfHosDrugList(RmsItfHosDrug rmsItfHosDrug);

    /**
     * 新增医院药品信息
     * 
     * @param rmsItfHosDrug 医院药品信息
     * @return 结果
     */
    public int insertRmsItfHosDrug(RmsItfHosDrug rmsItfHosDrug);

    /**
     * 修改医院药品信息
     * 
     * @param rmsItfHosDrug 医院药品信息
     * @return 结果
     */
    public int updateRmsItfHosDrug(RmsItfHosDrug rmsItfHosDrug);

    /**
     * 删除医院药品信息
     * 
     * @param drugCode 医院药品信息主键
     * @return 结果
     */
    public int deleteRmsItfHosDrugByDrugCode(String drugCode);

    /**
     * 批量删除医院药品信息
     * 
     * @param drugCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsItfHosDrugByDrugCodes(String[] drugCodes);
}
