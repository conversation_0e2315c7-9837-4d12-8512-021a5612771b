package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTPresMed;
import com.rms.core.service.IRmsTPresMedService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 处方明细信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@RestController
@RequestMapping("/rms/tpresmed")
public class RmsTPresMedController extends BaseController
{
    @Autowired
    private IRmsTPresMedService rmsTPresMedService;

    /**
     * 查询处方明细信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresmed:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTPresMed rmsTPresMed)
    {
        startPage();
        List<RmsTPresMed> list = rmsTPresMedService.selectRmsTPresMedList(rmsTPresMed);
        return getDataTable(list);
    }

    /**
     * 导出处方明细信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresmed:export')")
    @Log(title = "处方明细信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTPresMed rmsTPresMed)
    {
        List<RmsTPresMed> list = rmsTPresMedService.selectRmsTPresMedList(rmsTPresMed);
        ExcelUtil<RmsTPresMed> util = new ExcelUtil<RmsTPresMed>(RmsTPresMed.class);
        util.exportExcel(response, list, "处方明细信息数据");
    }

    /**
     * 获取处方明细信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresmed:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(rmsTPresMedService.selectRmsTPresMedByCode(code));
    }

    /**
     * 新增处方明细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresmed:add')")
    @Log(title = "处方明细信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTPresMed rmsTPresMed)
    {
        return toAjax(rmsTPresMedService.insertRmsTPresMed(rmsTPresMed));
    }

    /**
     * 修改处方明细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresmed:edit')")
    @Log(title = "处方明细信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTPresMed rmsTPresMed)
    {
        return toAjax(rmsTPresMedService.updateRmsTPresMed(rmsTPresMed));
    }

    /**
     * 删除处方明细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresmed:remove')")
    @Log(title = "处方明细信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(rmsTPresMedService.deleteRmsTPresMedByCodes(codes));
    }
}
