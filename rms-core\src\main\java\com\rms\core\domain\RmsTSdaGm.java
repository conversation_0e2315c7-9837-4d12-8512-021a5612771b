package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 药品过敏信息对象 rms_t_sda_gm
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public class RmsTSdaGm extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 药品说明书ID */
    @Excel(name = "药品说明书ID")
    private Long sdaId;

    /** 过敏代码 */
    @Excel(name = "过敏代码")
    private String gmdm;

    /** 代码类型（未用） */
    @Excel(name = "代码类型", readConverterExp = "未=用")
    private Long dmlx;

    /** 过敏标识：0-过敏禁用；1-过敏慎用 */
    @Excel(name = "过敏标识：0-过敏禁用；1-过敏慎用")
    private Long gmbs;

    /** 是否屏蔽 */
    @Excel(name = "是否屏蔽")
    private String ispb;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setGmdm(String gmdm)
    {
        this.gmdm = gmdm;
    }

    public String getGmdm()
    {
        return gmdm;
    }

    public void setDmlx(Long dmlx)
    {
        this.dmlx = dmlx;
    }

    public Long getDmlx()
    {
        return dmlx;
    }

    public void setGmbs(Long gmbs)
    {
        this.gmbs = gmbs;
    }

    public Long getGmbs()
    {
        return gmbs;
    }

    public void setIspb(String ispb)
    {
        this.ispb = ispb;
    }

    public String getIspb()
    {
        return ispb;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sdaId", getSdaId())
            .append("gmdm", getGmdm())
            .append("dmlx", getDmlx())
            .append("gmbs", getGmbs())
            .append("ispb", getIspb())
            .toString();
    }
}
