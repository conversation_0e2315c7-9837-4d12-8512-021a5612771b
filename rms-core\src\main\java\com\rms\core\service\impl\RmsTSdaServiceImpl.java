package com.rms.core.service.impl;

import java.util.List;
import com.rms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaMapper;
import com.rms.core.domain.RmsTSda;
import com.rms.core.service.IRmsTSdaService;

/**
 * 药品说明书Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class RmsTSdaServiceImpl implements IRmsTSdaService
{
    @Autowired
    private RmsTSdaMapper rmsTSdaMapper;

    /**
     * 查询药品说明书
     *
     * @param ID 药品说明书主键
     * @return 药品说明书
     */
    @Override
    public RmsTSda selectRmsTSdaByID(Long ID)
    {
        return rmsTSdaMapper.selectRmsTSdaByID(ID);
    }

    /**
     * 查询药品说明书列表
     *
     * @param rmsTSda 药品说明书
     * @return 药品说明书
     */
    @Override
    public List<RmsTSda> selectRmsTSdaList(RmsTSda rmsTSda)
    {
        return rmsTSdaMapper.selectRmsTSdaList(rmsTSda);
    }

    /**
     * 新增药品说明书
     *
     * @param rmsTSda 药品说明书
     * @return 结果
     */
    @Override
    public int insertRmsTSda(RmsTSda rmsTSda)
    {
        rmsTSda.setCreateTime(DateUtils.getNowDate());
        return rmsTSdaMapper.insertRmsTSda(rmsTSda);
    }

    /**
     * 修改药品说明书
     *
     * @param rmsTSda 药品说明书
     * @return 结果
     */
    @Override
    public int updateRmsTSda(RmsTSda rmsTSda)
    {
        rmsTSda.setUpdateTime(DateUtils.getNowDate());
        return rmsTSdaMapper.updateRmsTSda(rmsTSda);
    }

    /**
     * 批量删除药品说明书
     *
     * @param IDs 需要删除的药品说明书主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaByIDs(Long[] IDs)
    {
        return rmsTSdaMapper.deleteRmsTSdaByIDs(IDs);
    }

    /**
     * 删除药品说明书信息
     *
     * @param ID 药品说明书主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaByID(Long ID)
    {
        return rmsTSdaMapper.deleteRmsTSdaByID(ID);
    }
}
