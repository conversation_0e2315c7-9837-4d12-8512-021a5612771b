<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTPresShMapper">
    
    <resultMap type="RmsTPresSh" id="RmsTPresShResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="flag"    column="flag"    />
        <result property="text"    column="text"    />
        <result property="userId"    column="user_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRmsTPresShVo">
        select id, code, flag, text, user_id, nick_name, create_time from rms_t_pres_sh
    </sql>

    <select id="selectRmsTPresShList" parameterType="RmsTPresSh" resultMap="RmsTPresShResult">
        <include refid="selectRmsTPresShVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="text != null  and text != ''"> and text = #{text}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
        </where>
    </select>
    
    <select id="selectRmsTPresShById" parameterType="Long" resultMap="RmsTPresShResult">
        <include refid="selectRmsTPresShVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTPresSh" parameterType="RmsTPresSh" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_pres_sh
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="flag != null">flag,</if>
            <if test="text != null and text != ''">text,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="flag != null">#{flag},</if>
            <if test="text != null and text != ''">#{text},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRmsTPresSh" parameterType="RmsTPresSh">
        update rms_t_pres_sh
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="text != null and text != ''">text = #{text},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTPresShById" parameterType="Long">
        delete from rms_t_pres_sh where id = #{id}
    </delete>

    <delete id="deleteRmsTPresShByIds" parameterType="String">
        delete from rms_t_pres_sh where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>