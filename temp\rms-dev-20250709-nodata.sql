/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50740
 Source Host           : localhost:3306
 Source Schema         : rms

 Target Server Type    : MySQL
 Target Server Version : 50740
 File Encoding         : 65001

 Date: 25/08/2025 01:28:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint(20) NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 359 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_cfwtlb
-- ----------------------------
DROP TABLE IF EXISTS `rms_cfwtlb`;
CREATE TABLE `rms_cfwtlb`  (
  `cfwtbh` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '处方问题编号',
  `cfwtname` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方问题名称',
  `cfwtxq` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方问题详情',
  `cfwtlb` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方问题类别',
  `cfwtlbname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方问题类别名称',
  `wtcode` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题代码',
  `bz` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`cfwtbh`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '处方问题类别' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_itf_hos_admin_route
-- ----------------------------
DROP TABLE IF EXISTS `rms_itf_hos_admin_route`;
CREATE TABLE `rms_itf_hos_admin_route`  (
  `adm_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '给药途径编码',
  `adm_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '给药途径名称',
  `jmsybs` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '静脉使用标识',
  `jzbs` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '禁止标识',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`adm_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '给药途径表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_itf_hos_drug
-- ----------------------------
DROP TABLE IF EXISTS `rms_itf_hos_drug`;
CREATE TABLE `rms_itf_hos_drug`  (
  `drug_id` int(11) NULL DEFAULT NULL COMMENT '药品ID',
  `drug_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '药品编码',
  `drug_spec` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品规格',
  `packing_spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包装规格',
  `packing_uom` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包装单位',
  `packing_min_spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最小包装规格',
  `packing_min_qty` varchar(53) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最小包装数量',
  `drug_qty` varchar(53) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品数量',
  `drug_uom` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品单位',
  `drug_manuf` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生产厂家',
  `drug_product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `drug_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品名称',
  `drug_sp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品简拼',
  `drug_general_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通用名',
  `drug_for_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '剂型名称',
  `is_antibac` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否抗菌药物（1-是，0-否）',
  `antitumor_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抗肿瘤类型',
  `zx_flag` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中西药标志（1-西药；2-中成药；3-草药）',
  `is_basic_drug` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否基本药物（1-是，0-否）',
  `is_injection` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否注射剂（1-是，0-否）',
  `law_type` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '法规类型（管制类别）',
  `drug_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品类型',
  `antibac_type` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抗菌药物分类（限制级、特殊级等）',
  `is_purchase_internet` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否网络采购（1-是，0-否）',
  `stop_flag` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '停用标志（1-停用，0-正常，用于药品状态检查）',
  `stop_date` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '停用日期',
  `medicare_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保编码',
  `medicare_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保类型（甲类、乙类、丙类）',
  `medicare_remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保备注',
  `approval_certif` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批准文号',
  `hosp_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院编码',
  `imp_date` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '导入日期',
  `is_op_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门诊住院标志（op-门诊，ip-住院）',
  `is_rm` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否中药（1-是，0-否）',
  `is_djm` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否毒麻药品（1-是，0-否）',
  `dj` float NULL DEFAULT NULL COMMENT '单价',
  `je` float NULL DEFAULT NULL COMMENT '金额',
  `cancer_drug` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抗肿瘤药物标识（1-是，0-否）',
  `cold_drug` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '感冒药标识（1-是，0-否）',
  `proton_pump_drug` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '质子泵抑制剂标识（1-是，0-否）',
  `glucocorticoid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '糖皮质激素标识（1-是，0-否）',
  `key_drug` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重点监控药品标识（1-是，0-否）',
  `blood_drug` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '血液制品标识（1-是，0-否）',
  `spirit_anaesthesia_drug` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '精神麻醉药品标识（1-是，0-否）',
  `is_zdjk` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否重点监控药品（1-是，0-否）',
  `expensive_drug` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '贵重药品标识（1-是，0-否）',
  `is_tsyp` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否特殊用药（1-是，0-否）',
  `gt_20` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `gt_21` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_jc` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否需要监测（1-需要，0-不需要，如血药浓度监测）',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`drug_code`) USING BTREE,
  INDEX `idx_drug_name`(`drug_name`) USING BTREE,
  INDEX `idx_is_antibac`(`is_antibac`) USING BTREE,
  INDEX `idx_drug_type`(`drug_type`) USING BTREE,
  INDEX `idx_zx_flag`(`zx_flag`) USING BTREE,
  INDEX `idx_drug_composite`(`is_antibac`, `drug_type`, `zx_flag`) USING BTREE,
  INDEX `idx_drug_manuf`(`drug_manuf`) USING BTREE,
  INDEX `idx_hos_drug_code_zx_flag`(`drug_code`, `zx_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院药品信息表（完整版）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_itf_hos_frequency
-- ----------------------------
DROP TABLE IF EXISTS `rms_itf_hos_frequency`;
CREATE TABLE `rms_itf_hos_frequency`  (
  `freq_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '频次编码',
  `freq_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '频次名称',
  `freq_sp` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '频次说明',
  `daily_times` decimal(5, 2) NULL DEFAULT NULL COMMENT '每日次数',
  `weekly_times` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '每周次数',
  `hosp_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院编码',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`freq_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用药频次表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_itf_hos_spec
-- ----------------------------
DROP TABLE IF EXISTS `rms_itf_hos_spec`;
CREATE TABLE `rms_itf_hos_spec`  (
  `spec_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '科室编码',
  `spec_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`spec_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '科室信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_pc_zdy
-- ----------------------------
DROP TABLE IF EXISTS `rms_pc_zdy`;
CREATE TABLE `rms_pc_zdy`  (
  `yp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `freq_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `freq_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药品使用自定义频次信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_sys_param
-- ----------------------------
DROP TABLE IF EXISTS `rms_sys_param`;
CREATE TABLE `rms_sys_param`  (
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参数代码',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数名称',
  `value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数值',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数类型',
  `flag` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标志',
  PRIMARY KEY (`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统参数表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_byyydzb
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_byyydzb`;
CREATE TABLE `rms_t_byyydzb`  (
  `akb020` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保编码',
  `yp_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品编码',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '标准数据ID',
  `cid` int(11) NULL DEFAULT NULL COMMENT 'cid',
  INDEX `idx_yp_code`(`yp_code`) USING BTREE,
  INDEX `idx_sda_id`(`sda_id`) USING BTREE,
  INDEX `idx_akb020`(`akb020`) USING BTREE,
  INDEX `idx_cid`(`cid`) USING BTREE,
  INDEX `idx_byyydzb_akb020_yp_code`(`akb020`, `yp_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '本院药品与标准数据对照表（修正版）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_drug_zdfyl
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_drug_zdfyl`;
CREATE TABLE `rms_t_drug_zdfyl`  (
  `drug_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品编码',
  `drug_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品名称',
  `hosp_flag` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院标志',
  `zdfyl` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最大发药量',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
  `bz` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  INDEX `idx_drug_code`(`drug_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药品最大发药量表（修正版）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_gmbase
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_gmbase`;
CREATE TABLE `rms_t_gmbase`  (
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '过敏代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过敏名称',
  `sp` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拼音缩写',
  PRIMARY KEY (`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '过敏基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_icd10_base
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_icd10_base`;
CREATE TABLE `rms_t_icd10_base`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号ID',
  `code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诊断编码',
  `icd_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ICD-10编码',
  `fjm` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分级码或父级码',
  `icd_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ICD-10诊断名称',
  `sp` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拼音缩写',
  `remark` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_icd10_base_code`(`code`) USING BTREE,
  INDEX `idx_icd10_base_icd_code`(`icd_code`) USING BTREE,
  INDEX `idx_icd10_base_icd_name`(`icd_name`) USING BTREE,
  INDEX `idx_icd10_base_fjm`(`fjm`) USING BTREE,
  INDEX `idx_icd10_base_sp`(`sp`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 900007 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'ICD10诊断基础表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_med_zdy_dept
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_med_zdy_dept`;
CREATE TABLE `rms_t_med_zdy_dept`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `yp_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品编码',
  `dept_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室编码',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_yp_code`(`yp_code`) USING BTREE,
  INDEX `idx_dept_code`(`dept_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药品自定义科室表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_med_zdy_doct
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_med_zdy_doct`;
CREATE TABLE `rms_t_med_zdy_doct`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `yp_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品编码',
  `doct_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生编码',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_yp_code`(`yp_code`) USING BTREE,
  INDEX `idx_doct_code`(`doct_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药品自定义医生表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_med_zdy_gytj
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_med_zdy_gytj`;
CREATE TABLE `rms_t_med_zdy_gytj`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `yp_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品编码',
  `gytj_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '给药途径编码',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_yp_code`(`yp_code`) USING BTREE,
  INDEX `idx_gytj_code`(`gytj_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药品自定义给药途径表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_med_zdy_pc
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_med_zdy_pc`;
CREATE TABLE `rms_t_med_zdy_pc`  (
  `yp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `freq_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`freq_code`, `yp_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_pcdmb
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_pcdmb`;
CREATE TABLE `rms_t_pcdmb`  (
  `akb020` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `his_pc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `by_pc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `drcs` float(20, 2) NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_pres
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_pres`;
CREATE TABLE `rms_t_pres`  (
  `code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '处方编码',
  `hosp_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院编码',
  `dept_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室代码',
  `dept_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `doct_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生代码',
  `doct_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生名称',
  `doct_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生级别代码',
  `doct_type_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生级别名称',
  `his_time` datetime(0) NULL DEFAULT NULL COMMENT 'HIS时间',
  `hosp_flag` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门诊/住院标识：op-门诊；ip-住院',
  `treat_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就诊类型',
  `treat_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就诊号',
  `bed_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '床位号',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `birth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出生日期',
  `sex` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `weight` float(10, 2) NULL DEFAULT NULL COMMENT '体重（千克）',
  `height` float(10, 2) NULL DEFAULT NULL COMMENT '身高（厘米）',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `medical_record` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '病历号',
  `card_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡类型',
  `card_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡号',
  `pregnant_unit` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '孕期单位（天、周、月）',
  `pregnant` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '孕期',
  `all_info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过敏信息',
  `dia_info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诊断信息',
  `pres_id` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方号',
  `reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方理由',
  `is_urgent` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否紧急处方：0-否；1-是',
  `is_new` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否新开处方：0-否；1-是',
  `is_current` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否当前处方：0-否；1-是',
  `pres_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医嘱类型：L-长期医嘱；T-临时医嘱',
  `pres_time` datetime(0) NULL DEFAULT NULL COMMENT '处方时间',
  `discharge_drug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出院带药标识：0-否；1-是',
  `adm_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中药处方(医嘱)服用类型：0-未定；1-内服；2-外敷',
  `requir` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '要求',
  `cs1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '次数',
  `ts` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '天数',
  `solvent` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '煎煮溶剂',
  `jl` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '剂量',
  `cs2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '次数',
  `lb` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '煎药类别',
  `fs1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '方式1',
  `fs2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '方式2',
  `prescription_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方(医嘱)类型：0-未定；1-西药；2-中药(草药)',
  `level` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '级别（未用）',
  `flag` int(11) NULL DEFAULT NULL COMMENT '处方状态：-1-临时处方；0-无问题处方；8-问题处方；10-进入审方中心处方；11-审核不通过处方；12-审核通过处方；13-双签通过处方；14-超时通过处方',
  `is_read_doc` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生已读',
  `is_read_ys` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药师已读',
  `text` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文本',
  `zyzb` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中医主病',
  `zyzb_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中医主病代码',
  `zyzz` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中医主症',
  `zyzz_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中医主症代码',
  `reason1` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方理由1',
  `pres_sm` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方说明',
  `reason2` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方理由2',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`code`) USING BTREE,
  INDEX `idx_hosp_code`(`hosp_code`) USING BTREE,
  INDEX `idx_dept_code`(`dept_code`) USING BTREE,
  INDEX `idx_doct_code`(`doct_code`) USING BTREE,
  INDEX `idx_treat_code`(`treat_code`) USING BTREE,
  INDEX `idx_pres_time`(`pres_time`) USING BTREE,
  INDEX `idx_hosp_flag`(`hosp_flag`) USING BTREE,
  INDEX `idx_flag`(`flag`) USING BTREE,
  INDEX `idx_pres_composite`(`hosp_code`, `dept_code`, `pres_time`, `flag`) USING BTREE,
  INDEX `idx_pres_patient`(`name`, `id_card`, `birth`) USING BTREE,
  INDEX `idx_rms_t_pres_type`(`prescription_type`) USING BTREE,
  INDEX `idx_rms_t_pres_doctor`(`doct_name`) USING BTREE,
  INDEX `idx_rms_t_pres_level`(`level`) USING BTREE,
  INDEX `idx_rms_t_pres_review`(`flag`, `level`, `pres_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '处方表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_pres_fx
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_pres_fx`;
CREATE TABLE `rms_t_pres_fx`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '处方编码',
  `ywa` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药物A',
  `ywb` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药物B',
  `wtlvlcode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题等级代码',
  `wtlvl` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题等级',
  `wtcode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题代码',
  `wtsp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题属性',
  `wtname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题名称',
  `title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详情',
  `flag` int(11) NULL DEFAULT 0 COMMENT '标志',
  `text` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文本',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_wtcode`(`wtcode`) USING BTREE,
  INDEX `idx_pres_fx_code_wtlvlcode`(`code`, `wtlvlcode`) USING BTREE,
  INDEX `idx_rms_t_pres_fx_code`(`code`) USING BTREE,
  INDEX `idx_rms_t_pres_fx_level`(`wtlvl`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 65 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '处方分析结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_pres_gm
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_pres_gm`;
CREATE TABLE `rms_t_pres_gm`  (
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方编码',
  `type` int(5) NULL DEFAULT NULL COMMENT '过敏类型',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过敏源名称',
  `gmdm` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过敏源代码',
  INDEX `idx_pres_gm_code`(`code`) USING BTREE,
  INDEX `idx_pres_gm_gmdm`(`gmdm`) USING BTREE,
  INDEX `idx_pres_gm_composite`(`code`, `gmdm`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '处方过敏信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_pres_med
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_pres_med`;
CREATE TABLE `rms_t_pres_med`  (
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方编码',
  `med_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品名称（商品名）',
  `his_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院药品代码',
  `insur_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保代码',
  `approval` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批准文号',
  `spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格',
  `group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组号',
  `reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用药理由',
  `dose_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单次量单位',
  `dose` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单次量',
  `ord_qty` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开药数量',
  `ord_uom` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开药数量单位',
  `freq` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '频次代码',
  `administer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '给药途径代码',
  `begin_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用药开始时间（住院）',
  `end_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用药结束时间（住院）',
  `days` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服药天数（门诊）',
  `decoction_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '煎煮代码',
  `money` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '金额',
  `pres_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方ID',
  `med_reason1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用药原因1',
  `yysm` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用药说明',
  `bz` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_his_code`(`his_code`) USING BTREE,
  INDEX `idx_pres_id`(`pres_id`) USING BTREE,
  INDEX `idx_med_composite`(`code`, `his_code`, `med_name`) USING BTREE,
  INDEX `idx_pres_med_code_his_code`(`code`, `his_code`) USING BTREE,
  INDEX `idx_rms_t_pres_med_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '处方明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_pres_sh
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_pres_sh`;
CREATE TABLE `rms_t_pres_sh`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '处方编码',
  `flag` int(11) NOT NULL COMMENT '审核标志：11-审核不通过；12-审核通过；',
  `text` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核意见',
  `user_id` bigint(20) NOT NULL COMMENT '医师ID',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医师姓名',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '审核时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_rms_t_pres_sh_code`(`code`) USING BTREE,
  INDEX `idx_rms_t_pres_sh_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '处方审核意见表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda`;
CREATE TABLE `rms_t_sda`  (
  `id` int(11) NOT NULL COMMENT '主键ID',
  `ym` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品名称',
  `sp` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拼音缩写',
  `hypy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '汉语拼音',
  `unitrem` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
  `tymc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通用名称',
  `yymc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '英文名称',
  `hxmc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '化学名称',
  `zycf` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '主要成分',
  `fzs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分子式',
  `fzl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分子量',
  `xz` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '性状',
  `yldl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '药理毒理',
  `yddlx` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '药代动力学',
  `syz` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适应症',
  `yfyl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用法用量',
  `blfy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '不良反应',
  `jjz` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '禁忌症',
  `zysx` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '注意事项',
  `yfyy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '孕妇用药',
  `etyy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '儿童用药',
  `lnryy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '老年人用药',
  `xhzy` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相互作用',
  `ywgl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '药物过量',
  `ywgg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '药物规格',
  `cctj` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '储存条件',
  `ydts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '要点提示',
  `ylgzd` int(11) NULL DEFAULT NULL COMMENT '用量规则代码？',
  `fygzd` int(11) NULL DEFAULT NULL COMMENT '发药规则代码？',
  `ydgzd` int(11) NULL DEFAULT NULL COMMENT '用药规则代码？',
  `jxbs_2` int(11) NULL DEFAULT NULL COMMENT '禁忌标识2？',
  `zxybs` int(11) NULL DEFAULT NULL COMMENT '注意事项标识？',
  `ly` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源？',
  `ly_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源标识？',
  `scqy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '生产企业',
  `bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识？',
  `pzwh` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '批准文号',
  `cpym` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品英文名？',
  `cpsp` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品商品名？',
  `lnr_bs` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '老年人标识？',
  `yf_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '孕妇标识？',
  `yfqsy_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '孕妇期授乳标识？',
  `yfsyh_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '孕妇授乳哺乳标识？',
  `br_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '哺乳标识？',
  `gg_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格标识？',
  `sg_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '肾功能标识？',
  `hgg_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '肝功能标识？',
  `hsg_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '肝肾功能标识？',
  `agemin` float NULL DEFAULT NULL COMMENT '最小年龄',
  `agemax` float NULL DEFAULT NULL COMMENT '最大年龄',
  `gytj_bs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '给药途径标识？',
  `gytj_bs1` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '给药途径标识1？',
  `gmdm` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过敏代码？',
  `dc_1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据字段1？',
  `dr_1` int(11) NULL DEFAULT NULL COMMENT '数据关系1？',
  `mid` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型码',
  `dx` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '毒性？',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_tymc`(`tymc`) USING BTREE,
  INDEX `idx_mid`(`mid`) USING BTREE,
  INDEX `idx_bs`(`bs`) USING BTREE,
  INDEX `idx_ym`(`ym`) USING BTREE,
  INDEX `idx_sp`(`sp`) USING BTREE,
  INDEX `idx_sda_composite`(`tymc`, `bs`, `mid`) USING BTREE,
  INDEX `idx_sda_age`(`agemin`, `agemax`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药品说明书' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_age
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_age`;
CREATE TABLE `rms_t_sda_age`  (
  `id` int(11) NULL DEFAULT NULL COMMENT '主键ID',
  `agetype` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年龄代码',
  `age` decimal(10, 2) NULL DEFAULT NULL COMMENT '年龄',
  INDEX `idx_sda_age_age`(`age`) USING BTREE,
  INDEX `idx_sda_age_id`(`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_cf
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_cf`;
CREATE TABLE `rms_t_sda_cf`  (
  `id` int(11) NULL DEFAULT NULL COMMENT '主键ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '标准数据ID',
  `cf_id` int(11) NULL DEFAULT NULL COMMENT '成分ID',
  `nsource` int(11) NULL DEFAULT NULL COMMENT '来源',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `bs` int(11) NULL DEFAULT NULL COMMENT '标识',
  INDEX `idx_sda_cf_sda_id`(`sda_id`) USING BTREE,
  INDEX `idx_sda_cf_cf_id`(`cf_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标准药品成分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_cgl_condition
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_cgl_condition`;
CREATE TABLE `rms_t_sda_cgl_condition`  (
  `id` int(11) NULL DEFAULT NULL COMMENT '主键ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '标准数据ID',
  `age_min` int(11) NULL DEFAULT NULL COMMENT '最小年龄',
  `age_max` int(11) NULL DEFAULT NULL COMMENT '最大年龄',
  `gender` int(11) NULL DEFAULT NULL COMMENT '性别',
  `count_type` int(11) NULL DEFAULT NULL COMMENT '计算类型',
  `weight_min` float NULL DEFAULT NULL COMMENT '最小体重',
  `weight_max` float NULL DEFAULT NULL COMMENT '最大体重',
  `admin_routine` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '给药途径',
  INDEX `idx_sda_cgl_condition_sda_id`(`sda_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '常规量条件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_cgl_result
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_cgl_result`;
CREATE TABLE `rms_t_sda_cgl_result`  (
  `condition_id` int(11) NULL DEFAULT NULL COMMENT '条件ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '标准数据ID',
  `reco_type` int(11) NULL DEFAULT NULL COMMENT '推荐类型',
  `yl_min` float NULL DEFAULT NULL COMMENT '用量最小值',
  `yl_max` float NULL DEFAULT NULL COMMENT '用量最大值',
  `yl_unit` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用量单位',
  INDEX `idx_sda_cgl_result_sda_id`(`sda_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标准数据常规量结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_chd
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_chd`;
CREATE TABLE `rms_t_sda_chd`  (
  `id` int(11) NULL DEFAULT NULL COMMENT '主键ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '药品说明书ID',
  `chd_jd_min` int(11) NULL DEFAULT NULL COMMENT '最小使用年龄代码，从rms_t_sda_age获取',
  `chd_jd_max` int(11) NULL DEFAULT NULL COMMENT '最大使用年龄代码，从rms_t_sda_age获取',
  `result` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用药规则：0-禁用；1-慎用；2-提示；9-忽略/停用',
  `ch_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识（未用）',
  `age_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最小使用年龄（未用）',
  `age_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最大使用年龄（未用）',
  `ispb` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽',
  INDEX `idx_sda_chd_sda_id`(`sda_id`) USING BTREE,
  INDEX `idx_sda_chd_range`(`chd_jd_min`, `chd_jd_max`) USING BTREE,
  INDEX `idx_sda_chd_result`(`result`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_elder
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_elder`;
CREATE TABLE `rms_t_sda_elder`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '药品说明书ID',
  `age_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最小年龄',
  `age_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最大年龄',
  `jsbs` int(11) NULL DEFAULT NULL COMMENT '禁慎标识：0-禁用，1-慎用，2-提示，9-忽略/禁用',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注说明',
  `ispb` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sda_elder_sda_id`(`sda_id`) USING BTREE,
  INDEX `idx_sda_elder_jsbs`(`jsbs`) USING BTREE,
  INDEX `idx_sda_elder_age`(`age_min`, `age_max`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4086 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标准数据老年人用药表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_gestation
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_gestation`;
CREATE TABLE `rms_t_sda_gestation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '药品说明书ID',
  `dayup` int(11) NULL DEFAULT NULL COMMENT '开始孕周',
  `daydown` int(11) NULL DEFAULT NULL COMMENT '结束孕周',
  `dwcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位代码（未用）',
  `dw` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位（未用）',
  `bs` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识：0-禁用；1-慎用；2-提示；9-忽略/停用',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `ispb` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sda_gestation_sda_id`(`sda_id`) USING BTREE,
  INDEX `idx_sda_gestation_days`(`dayup`, `daydown`) USING BTREE,
  INDEX `idx_sda_gestation_bs`(`bs`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14252 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '孕期用药分析表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_gm
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_gm`;
CREATE TABLE `rms_t_sda_gm`  (
  `id` int(11) NULL DEFAULT NULL COMMENT '主键ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '药品说明书ID',
  `gmdm` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过敏代码',
  `dmlx` int(11) NULL DEFAULT NULL COMMENT '代码类型（未用）',
  `gmbs` int(11) NULL DEFAULT NULL COMMENT '过敏标识：0-过敏禁用；1-过敏慎用',
  `ispb` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药品过敏信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_gytj
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_gytj`;
CREATE TABLE `rms_t_sda_gytj`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '药品说明书ID',
  `gytj_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '给药途径编码',
  `bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识（未用）',
  `ispb` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sda_gytj_sda_id`(`sda_id`) USING BTREE,
  INDEX `idx_sda_gytj_code`(`gytj_code`) USING BTREE,
  INDEX `idx_sda_gytj_bs`(`bs`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47297 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标准药品给药途径表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_gytj_result
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_gytj_result`;
CREATE TABLE `rms_t_sda_gytj_result`  (
  `sda_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `admin_route` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_suitable` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1',
  `warning_level` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `warning_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  INDEX `idx_sda_gytj_result_sda_id`(`sda_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '给药途径结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_icd10_info
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_icd10_info`;
CREATE TABLE `rms_t_sda_icd10_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sda_id` int(11) NULL DEFAULT NULL COMMENT '药品说明书ID',
  `diagnoses` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诊断编码',
  `expand` int(11) NULL DEFAULT NULL COMMENT '扩展（未用）',
  `bs` int(11) NULL DEFAULT NULL COMMENT '标识',
  `gzd` int(11) NULL DEFAULT NULL COMMENT '（未用）',
  `pri` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '（未用）',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `illhis` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '（未用）',
  `sda_icd10_stencil_expand` int(11) NULL DEFAULT NULL COMMENT '（未用）',
  `ispb` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽',
  `edit_time` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '（未用）',
  `edit_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '（未用）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_rms_t_sda_icd10_info_sda_id`(`sda_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1373256 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药物与诊断信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_max
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_max`;
CREATE TABLE `rms_t_sda_max`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sda_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `gytj_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `maxcount` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `jlbs` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `yl_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5129 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_nosyz_lk
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_nosyz_lk`;
CREATE TABLE `rms_t_sda_nosyz_lk`  (
  `sda_id` int(11) NOT NULL,
  `Ym` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `fldm` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `flname` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_sda_sex
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_sda_sex`;
CREATE TABLE `rms_t_sda_sex`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sda_id` int(11) NOT NULL COMMENT '药品说明书ID',
  `sex` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别：1-男；2-女',
  `bs` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识（未用）',
  `ispb` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 826 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标准药品性别表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_tj_base
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_tj_base`;
CREATE TABLE `rms_t_tj_base`  (
  `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '给药途径名称',
  `ms` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `dm` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代码',
  INDEX `idx_tj_base_dm`(`dm`) USING BTREE,
  INDEX `idx_tj_base_name`(`name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '给药途径基础表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_tjdzb
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_tjdzb`;
CREATE TABLE `rms_t_tjdzb`  (
  `akb020` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保编码',
  `h_tj` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'HIS系统给药途径编码',
  `h_tname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'HIS系统给药途径名称',
  `by_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '给药途径编码',
  `by_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '给药途径名称',
  INDEX `idx_tjdzb_akb020`(`akb020`) USING BTREE,
  INDEX `idx_tjdzb_h_tj`(`h_tj`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '给药途径对照表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_xhzy_edi
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_xhzy_edi`;
CREATE TABLE `rms_t_xhzy_edi`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `yaowua` int(11) NULL DEFAULT NULL COMMENT 'A药物ID',
  `yaowub` int(11) NULL DEFAULT NULL COMMENT 'B药物ID',
  `effect` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相互作用效果',
  `mechanism` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '作用机制',
  `relatedrug` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相关药物',
  `reference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参考文献',
  `imp_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重要性标识：0-严重；1-一般；2-其它',
  `jx_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识（未用）',
  `recommandations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '建议',
  `main` varchar(48) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主要信息（未用）',
  `sjbs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识（未用）',
  `gx` int(11) NULL DEFAULT NULL COMMENT '关系（未用）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `sugflag` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '建议标识：pwjj-配伍禁忌；xhzy-相互作用',
  `type` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型（未用）',
  `significance` int(11) NULL DEFAULT NULL COMMENT '重要性（未用）',
  `onset` int(11) NULL DEFAULT NULL COMMENT '起效时间（未用）',
  `documentation` int(11) NULL DEFAULT NULL COMMENT '文档（未用）',
  `effecttype` int(11) NULL DEFAULT NULL COMMENT '效果类型（未用）',
  `result` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果等级：0-禁忌，1-问题，2-提示',
  `ispb` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽',
  `add_user` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '添加用户（未用）',
  `add_time` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '添加时间（未用）',
  `edit_user` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编辑用户（未用）',
  `edit_time` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编辑时间（未用）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 166238 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药品相互作用表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_xhzy_edi_zy
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_xhzy_edi_zy`;
CREATE TABLE `rms_t_xhzy_edi_zy`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `yaowua` int(11) NULL DEFAULT NULL COMMENT 'A药物ID',
  `yaowub` int(11) NULL DEFAULT NULL COMMENT 'B药物ID',
  `effect` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相互作用效果',
  `mechanism` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '作用机制',
  `relatedrug` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相关药物',
  `reference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参考文献',
  `imp_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重要性标识：0-严重；1-一般；2-其它',
  `jx_bs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识（未用）',
  `recommandations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '建议',
  `main` varchar(48) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主要信息（未用）',
  `sjbs` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识（未用）',
  `gx` int(11) NULL DEFAULT NULL COMMENT '关系（未用）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `sugflag` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '建议标识：pwjj-配伍禁忌；xhzy-相互作用',
  `type` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型（未用）',
  `significance` int(11) NULL DEFAULT NULL COMMENT '重要性（未用）',
  `onset` int(11) NULL DEFAULT NULL COMMENT '起效时间（未用）',
  `documentation` int(11) NULL DEFAULT NULL COMMENT '文档（未用）',
  `effecttype` int(11) NULL DEFAULT NULL COMMENT '效果类型（未用）',
  `result` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果等级：0-禁忌，1-问题，2-提示',
  `ispb` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否屏蔽',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_xhzy_edi_zy_yaowu`(`yaowua`, `yaowub`) USING BTREE,
  INDEX `idx_xhzy_edi_zy_sugflag`(`sugflag`) USING BTREE,
  INDEX `idx_xhzy_edi_zy_result`(`result`) USING BTREE,
  INDEX `idx_xhzy_edi_zy_ispb`(`ispb`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 152800 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '中药相互作用表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_t_yb_zcy
-- ----------------------------
DROP TABLE IF EXISTS `rms_t_yb_zcy`;
CREATE TABLE `rms_t_yb_zcy`  (
  `drug_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品编码',
  `drug_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品名称',
  `bs` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医保中草药表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_wtlb
-- ----------------------------
DROP TABLE IF EXISTS `rms_wtlb`;
CREATE TABLE `rms_wtlb`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wtsp` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题缩拼',
  `wtname` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题名称',
  `wtlvl` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题级别',
  `wtlvlcode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '级别代码',
  `wtcode` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题编码',
  `wtlx` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题类型',
  `is_sq` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否启用',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_wtcode`(`wtcode`) USING BTREE,
  INDEX `idx_wtlvlcode`(`wtlvlcode`) USING BTREE,
  INDEX `idx_wtsp`(`wtsp`) USING BTREE,
  INDEX `idx_wtlb_composite`(`wtlvlcode`, `wtcode`, `is_sq`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 900004 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问题列表表（用于合理用药分析问题分类）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(5) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 102 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(4) NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime(0) NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 64 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int(1) NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1073 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int(4) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int(2) NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int(1) NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int(1) NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(20) NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type`) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status`) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 95 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime(0) NULL DEFAULT NULL COMMENT '密码最后更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Function structure for rms_fn_get_yz
-- ----------------------------
DROP FUNCTION IF EXISTS `rms_fn_get_yz`;
delimiter ;;
CREATE FUNCTION `rms_fn_get_yz`(pregnant_unit VARCHAR(10), 
    pregnant VARCHAR(10))
 RETURNS int(11)
  READS SQL DATA 
  DETERMINISTIC
BEGIN
    DECLARE result_weeks INT DEFAULT 0;
    DECLARE pregnant_value DECIMAL(10,2);
    DECLARE week_part INT DEFAULT 0;
    DECLARE day_remainder INT DEFAULT 0;
    
    -- 如果输入为空则返回0
    IF pregnant_unit IS NULL OR pregnant = '' OR pregnant IS NULL THEN
        RETURN 0;
    END IF;
    
    SET pregnant_value = CAST(pregnant AS DECIMAL(10,2));
    
    -- 根据单位转换为孕周数，与MSSQL逻辑一致
    CASE pregnant_unit
        WHEN '天' THEN 
            SET week_part = FLOOR(pregnant_value / 7);
            SET day_remainder = pregnant_value % 7;
            SET result_weeks = week_part;
            IF day_remainder > 4 THEN
                SET result_weeks = week_part + 1;
            END IF;
        WHEN '月' THEN 
            SET week_part = FLOOR((pregnant_value * 30) / 7);
            SET day_remainder = (pregnant_value * 30) % 7;
            SET result_weeks = week_part;
            IF day_remainder > 4 THEN
                SET result_weeks = week_part + 1;
            END IF;
        WHEN '周' THEN SET result_weeks = pregnant_value;
        WHEN 'week' THEN SET result_weeks = pregnant_value;
        WHEN 'month' THEN 
            SET week_part = FLOOR((pregnant_value * 30) / 7);
            SET day_remainder = (pregnant_value * 30) % 7;
            SET result_weeks = week_part;
            IF day_remainder > 4 THEN
                SET result_weeks = week_part + 1;
            END IF;
        WHEN 'day' THEN 
            SET week_part = FLOOR(pregnant_value / 7);
            SET day_remainder = pregnant_value % 7;
            SET result_weeks = week_part;
            IF day_remainder > 4 THEN
                SET result_weeks = week_part + 1;
            END IF;
        ELSE SET result_weeks = pregnant_value;
    END CASE;
    
    RETURN result_weeks;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_cfyy
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_cfyy`;
delimiter ;;
CREATE PROCEDURE `rms_fx_cfyy`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code_a VARCHAR(20),
    IN p_yp_code_b VARCHAR(20))
BEGIN
    main_block: BEGIN
        DECLARE v_midA VARCHAR(10);
        DECLARE v_midB VARCHAR(10);
        DECLARE v_sda_idA VARCHAR(10);
        DECLARE v_sda_idB VARCHAR(10);
        DECLARE v_n_count INT DEFAULT 0;
        DECLARE v_zx_flag1 VARCHAR(10);
        DECLARE v_zx_flag2 VARCHAR(10);
        DECLARE v_ywa_name VARCHAR(50);
        DECLARE v_cf VARCHAR(50);
        DECLARE v_n_count6 INT DEFAULT 0;
        
        -- 检查中药数量
        SELECT COUNT(*) INTO v_n_count6
        FROM rms_t_pres_med a
        INNER JOIN rms_itf_hos_drug b ON b.DRUG_CODE = a.his_code
        WHERE a.Code = p_Code AND b.ZX_FLAG = '3';
        
        IF v_n_count6 > 1 THEN
            LEAVE main_block;
        END IF;
        
        -- 获取药品标准数据信息
        SELECT b.cid, a.ID INTO v_midA, v_sda_idA
        FROM rms_t_sda a
        JOIN rms_t_byyydzb b ON a.ID = b.sda_id
        WHERE b.akb020 = p_akb020 AND b.yp_code = p_yp_code_a
        LIMIT 1;
        
        SELECT b.cid, a.ID INTO v_midB, v_sda_idB
        FROM rms_t_sda a
        JOIN rms_t_byyydzb b ON a.ID = b.sda_id
        WHERE b.akb020 = p_akb020 AND b.yp_code = p_yp_code_b
        LIMIT 1;
        
        IF v_midA IS NULL OR v_midB IS NULL THEN
            LEAVE main_block;
        END IF;
        
        SELECT DRUG_NAME INTO v_ywa_name
        FROM rms_itf_hos_drug 
        WHERE DRUG_CODE = p_yp_code_a
        LIMIT 1;
        
        -- 检查是否为同种药品
        IF v_midA = v_midB THEN
            INSERT INTO rms_t_pres_fx (
                Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
            )
            VALUES (
                p_Code,
                v_ywa_name,
                (SELECT DRUG_NAME FROM rms_itf_hos_drug WHERE DRUG_CODE = p_yp_code_b LIMIT 1),
                '1',
                '一般提示',
                'RLT025',
                'CFYYTS',
                '重复用药',
                '重复用药',
                CONCAT(v_ywa_name, '与', (SELECT DRUG_NAME FROM rms_itf_hos_drug WHERE DRUG_CODE = p_yp_code_b LIMIT 1), '属于同种药品'),
                0,
                '重复用药'
            );
        END IF;
        
        -- 检查同成分药品
        SELECT ZX_FLAG INTO v_zx_flag1 FROM rms_itf_hos_drug WHERE DRUG_CODE = p_yp_code_a LIMIT 1;
        SELECT ZX_FLAG INTO v_zx_flag2 FROM rms_itf_hos_drug WHERE DRUG_CODE = p_yp_code_b LIMIT 1;
        
        IF v_zx_flag1 = '1' AND v_zx_flag2 = '1' THEN
            SELECT COUNT(*) INTO v_n_count
            FROM rms_t_sda_cf a
            JOIN rms_t_sda_cf b ON a.cf_id = b.cf_id
            WHERE a.sda_id = v_sda_idA
            AND b.sda_id = v_sda_idB
            AND a.bs = '0'
            AND b.bs = '0';

            SELECT c.cf INTO v_cf
            FROM rms_t_sda_cf a
            JOIN rms_t_sda_cf b ON a.cf_id = b.cf_id
            JOIN rms_t_cfbase c ON a.cf_id = c.id
            WHERE a.sda_id = v_sda_idA
            AND b.sda_id = v_sda_idB
            AND a.bs = '0'
            AND b.bs = '0'
            LIMIT 1;
            
            IF v_n_count > 0 THEN
                INSERT INTO rms_t_pres_fx (
                    Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
                )
                VALUES (
                    p_Code,
                    v_ywa_name,
                    (SELECT DRUG_NAME FROM rms_itf_hos_drug WHERE DRUG_CODE = p_yp_code_b LIMIT 1),
                    '1',
                    '一般提示',
                    'RLT025',
                    'CFYYTS',
                    '重复用药',
                    '同成分用药',
                    CONCAT(v_ywa_name, '与', (SELECT DRUG_NAME FROM rms_itf_hos_drug WHERE DRUG_CODE = p_yp_code_b LIMIT 1), '含有相同成分【', v_cf, '】，请注意用药过量！'),
                    0,
                    '重复用药'
                );
            END IF;
        END IF;
    END main_block;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_cgl
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_cgl`;
delimiter ;;
CREATE PROCEDURE `rms_fx_cgl`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(20),
    IN p_csrq DATE,
    IN p_tz VARCHAR(20))
  COMMENT '单次常规用量分析存储过程'
main_block: BEGIN
	DECLARE v_sda_id VARCHAR(20);
	DECLARE v_sda_tj VARCHAR(20);
	DECLARE v_sda_drcs VARCHAR(20);
	DECLARE v_sda_dcyl VARCHAR(20);
	DECLARE v_nl INT;
	DECLARE v_condition_id VARCHAR(20);
	DECLARE v_count_type VARCHAR(20);
	DECLARE v_yl_unit VARCHAR(20);
	DECLARE v_xs VARCHAR(20);
	DECLARE v_PACKING_SPEC VARCHAR(20);
	DECLARE v_packing_uom VARCHAR(20);
	DECLARE v_count1 INT DEFAULT 0;
	DECLARE v_count2 INT DEFAULT 0;
	DECLARE v_count3 INT DEFAULT 0;
	DECLARE v_count4 INT DEFAULT 0;
	DECLARE v_count5 INT DEFAULT 0;
	DECLARE v_count6 INT DEFAULT 0;
	DECLARE v_count7 INT DEFAULT 0;
	DECLARE v_packing_min_spec VARCHAR(20);
	DECLARE v_yl_max VARCHAR(20);
	DECLARE v_ywa_name VARCHAR(50);
	DECLARE v_zx_flag VARCHAR(50);
	DECLARE v_dcsl DECIMAL(14,3);
	DECLARE v_gydw VARCHAR(20);
	DECLARE v_yl1 DECIMAL(14,3);
	
	-- 检查打粉情况
	SELECT COUNT(1) INTO v_count7
	FROM rms_t_pres_med 
	WHERE Code = p_Code AND his_code = p_yp_code 
	AND yysm LIKE '%打粉%';
	
	IF v_count7 > 0 THEN
			-- 打粉不分析用量
			LEAVE main_block;
	END IF;
	
	-- 检查中药执行标志
	SELECT COUNT(1) INTO v_count1
	FROM rms_itf_hos_drug 
	WHERE drug_code = p_yp_code AND ZX_FLAG = '3';
	
	-- 获取标准数据ID
	SELECT sda_id INTO v_sda_id
	FROM rms_t_byyydzb  
	WHERE akb020 = p_akb020 AND yp_code = p_yp_code
	LIMIT 1;
	
	-- 普通草药用量暂时不提示
	SELECT COUNT(1) INTO v_count6
	FROM rms_t_sda 
	WHERE ID = v_sda_id AND IFNULL(dx, '0') NOT IN ('1','2','3','4');
	
	IF v_count1 > 0 AND v_count6 > 0 THEN
			LEAVE main_block;
	END IF;
	
	-- 获取药品名称和执行标志
	SELECT DRUG_NAME, ZX_FLAG INTO v_ywa_name, v_zx_flag
	FROM rms_itf_hos_drug 
	WHERE DRUG_CODE = p_yp_code
	LIMIT 1;
	
	-- 处理给药途径
	SELECT COUNT(1) INTO v_count4
	FROM rms_t_tjdzb 
	WHERE h_tj = p_yp_tj;
	
	IF v_count4 > 1 THEN
			SELECT SUBSTRING(by_code, 1, 2) INTO v_sda_tj
			FROM rms_t_tjdzb 
			WHERE h_tj = p_yp_tj
			LIMIT 1;
	END IF;
	
	IF v_count4 = 1 THEN
			SELECT by_code INTO v_sda_tj
			FROM rms_t_tjdzb 
			WHERE h_tj = p_yp_tj
			LIMIT 1;
	END IF;
	
	-- 获取频次信息
	SELECT daily_times INTO v_sda_drcs
	FROM rms_itf_hos_frequency 
	WHERE freq_code = p_gypc
	LIMIT 1;
	
	-- 计算年龄（天数）
	SET v_nl = DATEDIFF(CURDATE(), p_csrq);
	
	-- 检查外用等情况
	SELECT COUNT(1) INTO v_count3
	FROM rms_t_pres_med 
	WHERE Code = p_Code AND his_code = p_yp_code 
	AND (yysm LIKE '%外%' OR yysm LIKE '%洗%' OR yysm LIKE '%浴%');
	
	-- 开始单位转换
	SET v_gydw = p_gydw;
	SET v_dcsl = CAST(p_dcsl AS DECIMAL(14,3));
	
	-- 单位标准化
	IF v_gydw = 'g' THEN
			SET v_gydw = '克';
	END IF;
	IF v_gydw = 'mg' THEN
			SET v_gydw = '毫克';
	END IF;
	IF v_gydw = 'ml' THEN
			SET v_gydw = '毫升';
	END IF;
	
	-- 获取包装规格信息
	SELECT PACKING_SPEC, packing_uom INTO v_PACKING_SPEC, v_packing_uom
	FROM rms_itf_hos_drug 
	WHERE DRUG_CODE = p_yp_code
	LIMIT 1;
	
	-- 获取条件ID和计数类型
	SELECT id, count_type INTO v_condition_id, v_count_type
	FROM rms_t_sda_cgl_condition
	WHERE sda_id = v_sda_id
	AND admin_routine LIKE CONCAT('%', v_sda_tj, '%')
	AND age_min < v_nl
	AND age_max > v_nl
	LIMIT 1;
	
	-- 获取用量单位
	SELECT yl_unit INTO v_yl_unit
	FROM rms_t_sda_cgl_result
	WHERE condition_id = v_condition_id
	AND reco_type != '2'
	LIMIT 1;
	
	-- 获取最小包装信息
	SELECT PACKING_MIN_QTY, packing_min_spec INTO v_xs, v_packing_min_spec
	FROM rms_itf_hos_drug
	WHERE drug_code = p_yp_code
	LIMIT 1;
	
	-- 单位转换逻辑
	IF v_gydw = '毫克' AND v_yl_unit = '克' THEN
			SET v_dcsl = v_dcsl / 1000;
			SET v_gydw = v_yl_unit;
	END IF;
	
	IF v_gydw = '克' AND v_yl_unit = '毫克' THEN
			SET v_dcsl = v_dcsl * 1000;
			SET v_gydw = v_yl_unit;
	END IF;
	
	IF v_gydw = 'u' AND v_yl_unit = '国际单位' THEN
			SET v_gydw = v_yl_unit;
	END IF;
	
	-- 复杂包装单位转换
	IF v_yl_unit = v_packing_uom AND v_gydw != v_yl_unit THEN
			IF v_packing_min_spec != v_yl_unit THEN
					IF v_packing_min_spec = 'mg' AND v_yl_unit = '克' THEN
							SET v_dcsl = (v_dcsl / CAST(v_xs AS DECIMAL)) / 1000;
							SET v_gydw = v_yl_unit;
					END IF;
					
					IF v_gydw = 'g' AND v_yl_unit = '毫克' THEN
							SET v_dcsl = (v_dcsl / CAST(v_xs AS DECIMAL)) * 1000;
							SET v_gydw = v_yl_unit;
					END IF;
			END IF;
			
			IF (v_packing_min_spec = v_yl_unit) OR 
				 (v_packing_min_spec = 'mg' AND v_yl_unit = '毫克') OR 
				 (v_packing_min_spec = 'g' AND v_yl_unit = '克') THEN
					SET v_dcsl = v_dcsl / CAST(v_xs AS DECIMAL);
					SET v_gydw = v_yl_unit;
			END IF;
	END IF;
	
	IF v_gydw = v_packing_uom AND v_gydw != v_yl_unit THEN
			IF v_packing_min_spec != v_yl_unit THEN
					IF v_packing_min_spec = 'mg' AND v_yl_unit = '克' THEN
							SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL) / 1000;
							SET v_gydw = v_yl_unit;
					END IF;
					
					IF v_gydw = 'g' AND v_yl_unit = '毫克' THEN
							SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL) * 1000;
							SET v_gydw = v_yl_unit;
					END IF;
			END IF;
			
			IF (v_packing_min_spec = v_yl_unit) OR 
				 (v_packing_min_spec = 'mg' AND v_yl_unit = '毫克') OR 
				 (v_packing_min_spec = 'g' AND v_yl_unit = '克') THEN
					SET v_dcsl = v_dcsl * CAST(v_xs AS DECIMAL);
					SET v_gydw = v_yl_unit;
			END IF;
	END IF;
	
	-- 单位转换结束，如果单位不匹配则退出
	IF v_gydw != v_yl_unit THEN
			LEAVE main_block;
	END IF;
	
	-- 中药内服检查
	IF v_count1 > 0 AND v_count3 = 0 THEN
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('医院规定有毒草药内服最大量为药典推荐量：', c.tymc, '超过了最大用量', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.id = v_sda_id
			AND a.reco_type = '1'
			AND a.yl_max < v_dcsl
			LIMIT 1;
	END IF;
	
	-- 中药外用检查
	IF v_count1 > 0 AND v_count3 > 0 THEN
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('医院规定有毒草药外用最大量为药典推荐量：', c.tymc, '超过了最大用量', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.id = v_sda_id
			AND a.reco_type = '1'
			AND a.yl_max < v_dcsl
			LIMIT 1;
	END IF;
	
	-- 单次超极量分析
	IF v_count_type = '0' AND v_count3 = 0 THEN
			-- 单次超极量分析
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('说明书提示：', c.tymc, '常规用量为：', a.yl_min, '~', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min > v_dcsl OR a.yl_max < v_dcsl)
			LIMIT 1;
			
			-- 单日量分析
			SELECT COUNT(1) INTO v_count5
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min > v_dcsl OR a.yl_max < v_dcsl);
			
			IF v_count5 = 0 THEN
					SET v_yl1 = v_dcsl * CAST(v_sda_drcs AS DECIMAL);
					
					INSERT INTO rms_t_pres_fx (
							Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
					)
					SELECT 
							p_Code,
							v_ywa_name,
							'',
							'1',
							'一般提示',
							'RLT030',
							'YHGXHCGYFYLWT_PC',
							'药品用法用量',
							CONCAT(c.tymc, '单日用量不符合药品说明书推荐用量'),
							CONCAT('说明书提示：', c.tymc, '单日常规用量为：', a.yl_min, '~', a.yl_max, a.yl_unit),
							0,
							'单日常规用量分析'
					FROM rms_t_sda c 
					LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
					WHERE c.ID = v_sda_id 
					AND a.condition_id = v_condition_id
					AND a.reco_type = '1'
					AND (a.yl_min > v_yl1 OR a.yl_max < v_yl1)
					LIMIT 1;
			END IF;
	END IF;
	
	-- 按体重检查
	IF v_count_type = '1' AND v_count3 = 0 THEN
			IF p_tz = '' OR IFNULL(p_tz, '0') = '0' THEN
					LEAVE main_block;
			END IF;
			
			INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
			)
			SELECT 
					p_Code,
					v_ywa_name,
					'',
					'1',
					'一般提示',
					'RLT030',
					'YHGXHCGYFYLWT_PC',
					'药品用法用量',
					CONCAT(c.tymc, '用量不符合药品说明书推荐用量'),
					CONCAT('说明书提示：', c.tymc, '常规用量为每公斤：', a.yl_min, '~', a.yl_max, a.yl_unit),
					0,
					'单次常规用量分析'
			FROM rms_t_sda c 
			LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
			WHERE c.ID = v_sda_id 
			AND a.condition_id = v_condition_id
			AND a.reco_type = '0'
			AND (a.yl_min * CAST(p_tz AS DECIMAL) > v_dcsl OR a.yl_max * CAST(p_tz AS DECIMAL) < v_dcsl)
			LIMIT 1;
	END IF;

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_cgl_drl
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_cgl_drl`;
delimiter ;;
CREATE PROCEDURE `rms_fx_cgl_drl`(IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(20),
    IN p_csrq VARCHAR(20),
    IN p_tz VARCHAR(20))
  COMMENT '单次常规用量分析存储过程'
main_block: BEGIN
		-- 声明变量
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_sda_tj VARCHAR(20);
		DECLARE v_sda_drcs INT DEFAULT 0;
		DECLARE v_sda_dcyl VARCHAR(20);
		DECLARE v_nl VARCHAR(20);
		DECLARE v_condition_id VARCHAR(20);
		DECLARE v_count_type VARCHAR(20);
		DECLARE v_n_count INT DEFAULT 0;
		DECLARE v_n_count1 INT DEFAULT 0;
		DECLARE v_yysm VARCHAR(50);
		DECLARE v_zx_flag VARCHAR(50);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_freq_times VARCHAR(10);
		DECLARE v_zongzhuanheng VARCHAR(200);
		
		-- 声明异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;

		-- 获取药品基本信息
		SELECT DRUG_NAME, ZX_FLAG INTO v_ywa_name, v_zx_flag
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code LIMIT 1;

		-- 如果是中药则返回
		IF v_zx_flag = '3' THEN
				LEAVE main_block;
		END IF;

		-- 获取用药说明
		SELECT yysm INTO v_yysm
		FROM rms_t_pres_med 
		WHERE Code = p_code LIMIT 1;

		-- 如果用药说明包含"预防"且给药频次是"01"则返回
		IF v_yysm LIKE '%预防%' AND p_gypc = '01' THEN
				LEAVE main_block;
		END IF;

		-- 检查是否有医院自定义频次
		SELECT COUNT(1) INTO v_n_count
		FROM rms_t_med_zdy_pc  
		WHERE yp_code = p_yp_code;
		
		IF v_n_count > 0 THEN
				-- 检查频次是否符合自定义要求
				SELECT COUNT(1) INTO v_n_count1
				FROM rms_t_med_zdy_pc  
				WHERE yp_code = p_yp_code AND freq_code = p_gypc;
				
				IF v_n_count1 <= 0 THEN
						-- 获取自定义频次信息（这里简化处理，实际应该实现get_zongzhuanheng函数）
						SET v_zongzhuanheng = IFNULL(rms_get_zongzhuanheng(p_yp_code), '');
					
						-- 插入频次不符合提示
						INSERT INTO rms_t_pres_fx (
								Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
						)
						SELECT p_code, v_ywa_name, '', '1', '一般提示', 'RLT030', 'YHGXHCGYFYLWT_PC', 
								'药品用法用量', CONCAT(v_ywa_name, '++++', '频次不符合医院自定义频次'),
								CONCAT('医院自定义要求该药频次为：', v_zongzhuanheng), 
								0, '单次常规用量分析';
				ELSE
						-- 符合自定义频次要求，直接返回
						LEAVE main_block;
				END IF;
		END IF;

		-- 如果没有自定义频次，进行常规分析
		IF v_n_count = 0 THEN
				-- 获取SDA ID
				SELECT sda_id INTO v_sda_id
				FROM rms_t_byyydzb  
				WHERE akb020 = p_akb020 AND yp_code = p_yp_code LIMIT 1;

				-- 获取给药途径编码
				SELECT DISTINCT SUBSTRING(by_code, 1, 2) INTO v_sda_tj
				FROM rms_t_tjdzb 
				WHERE h_tj = p_yp_tj LIMIT 1;

				-- 获取每日次数
				SELECT daily_times INTO v_sda_drcs
				FROM rms_itf_hos_frequency 
				WHERE freq_code = p_gypc LIMIT 1;

				-- 计算年龄（天数）
				SET v_nl = DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y%m%d'));

				-- 获取条件ID和计算类型
				SELECT id, count_type INTO v_condition_id, v_count_type
				FROM rms_t_sda_cgl_condition
				WHERE sda_id = v_sda_id
				AND admin_routine LIKE CONCAT('%', v_sda_tj, '%')
				AND age_min < v_nl
				AND age_max > v_nl
				LIMIT 1;

				-- 进行单次超极量分析
				IF v_condition_id IS NOT NULL THEN
						INSERT INTO rms_t_pres_fx (
								Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
						)
						SELECT p_code, v_ywa_name, '', '1', '一般提示', 'RLT030', 'YHGXHCGYFYLWT_PC', 
								'药品用法用量', 
								CONCAT(c.tymc, '++++', '频次不符合药品说明书推荐频次'),
								CONCAT('说明书提示：', c.tymc, '++++', '频次为：', 
												CAST(a.yl_min AS CHAR), '~', CAST(a.yl_max AS CHAR)),
								0, '单次常规用量分析' 
						FROM rms_t_sda c 
						LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
						WHERE c.ID = v_sda_id 
						AND a.condition_id = v_condition_id
						AND reco_type = '2'
						AND (a.yl_min > CAST(v_sda_drcs AS SIGNED) OR a.yl_max < CAST(v_sda_drcs AS SIGNED));
				END IF;
		END IF;

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_cjl
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_cjl`;
delimiter ;;
CREATE PROCEDURE `rms_fx_cjl`(IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(50))
  COMMENT '单次超极量分析存储过程'
main_block: BEGIN
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_sda_tj VARCHAR(20);
		DECLARE v_sda_drcs VARCHAR(20);
		DECLARE v_sda_dcyl VARCHAR(20);
		DECLARE v_yl_unit1 VARCHAR(20);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_n_count INT;
		DECLARE v_gydw_converted VARCHAR(20);
		
		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
				
				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;
		
		-- 检查药品状态
		SELECT COUNT(1) INTO v_n_count 
		FROM rms_ITF_HOS_DRUG 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		IF v_n_count > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name 
		FROM rms_ITF_HOS_DRUG 
		WHERE DRUG_CODE = p_yp_code LIMIT 1;
		
		-- 获取标准药品ID
		SELECT sda_id INTO v_sda_id 
		FROM rms_t_byyydzb 
		WHERE akb020 = p_akb020 AND yp_code = p_yp_code LIMIT 1;
		
		-- 获取剂型编码
		SELECT by_code INTO v_sda_tj 
		FROM rms_t_tjdzb 
		WHERE h_tj = p_yp_tj AND akb020 = p_akb020 LIMIT 1;
		
		-- 获取频次信息
		SELECT drcs INTO v_sda_drcs 
		FROM rms_t_pcdmb 
		WHERE his_pc = p_gypc AND akb020 = p_akb020 LIMIT 1;
		
		-- 获取用量单位
		SELECT DISTINCT UnitRem INTO v_yl_unit1 
		FROM rms_t_sda 
		WHERE id = v_sda_id LIMIT 1;
		
		-- 单位转换
		SET v_gydw_converted = p_gydw;
		IF p_gydw = 'g' THEN
				SET v_gydw_converted = '克';
		ELSEIF p_gydw = 'mg' THEN
				SET v_gydw_converted = '毫克';
		ELSEIF p_gydw = 'ml' THEN
				SET v_gydw_converted = '毫升';
		END IF;
		
		-- 单位不匹配则退出
		IF v_gydw_converted != v_yl_unit1 THEN
				LEAVE main_block;
		END IF;
		
		-- 单次超极量分析结果
		INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		SELECT DISTINCT 
				p_code,
				v_ywa_name AS ywa,
				'' AS ywb,
				'1' AS wtlvlcode,
				'重要警示' AS wtlvl,
				'RLT010' AS wtcode,
				'CJLJJ' AS wtsp,
				'超极量' AS wtname,
				CONCAT('【', CAST(c.tymc AS CHAR), '】单次用量超最大次极量') AS title,
				CONCAT('说明书提示：', CAST(c.tymc AS CHAR), '++++', '单次最大极量为：', CAST(a.maxcount AS CHAR)) AS detail,
				0,
				'超极量'
		FROM rms_t_sda c 
		LEFT JOIN rms_t_sda_max a ON a.sda_id = c.ID AND a.jlbs = '0' 
		WHERE c.ID = v_sda_id 
				AND SUBSTRING(a.gytj_code, 1, 2) = SUBSTRING(v_sda_tj, 1, 2)
				AND CAST(a.maxcount AS DECIMAL(14,4)) < CAST(p_dcsl AS DECIMAL(14,4));

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_cjl_drl
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_cjl_drl`;
delimiter ;;
CREATE PROCEDURE `rms_fx_cjl_drl`(IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(20))
  COMMENT '单日超极量分析存储过程'
main_block: BEGIN

		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_sda_tj VARCHAR(20);
		DECLARE v_sda_drcs VARCHAR(20);
		DECLARE v_sda_dcyl VARCHAR(20);
		DECLARE v_sda_dryl DECIMAL(14,3);
		DECLARE v_yl_unit VARCHAR(20);
		DECLARE v_yl_unit1 VARCHAR(20);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_gydw_converted VARCHAR(20);
		
		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
				
				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;
		
		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name 
		FROM rms_ITF_HOS_DRUG 
		WHERE DRUG_CODE = p_yp_code LIMIT 1;
		
		-- 获取标准药品ID
		SELECT sda_id INTO v_sda_id 
		FROM rms_t_byyydzb 
		WHERE akb020 = p_akb020 AND yp_code = p_yp_code LIMIT 1;
		
		-- 获取剂型编码
		SELECT by_code INTO v_sda_tj 
		FROM rms_t_tjdzb 
		WHERE h_tj = p_yp_tj AND akb020 = p_akb020 LIMIT 1;
		
		-- 获取每日次数
		SELECT IFNULL(DAILY_TIMES, '0') INTO v_sda_drcs 
		FROM rms_ITF_HOS_FREQUENCY 
		WHERE freq_code = p_gypc LIMIT 1;
		
		-- 计算单日用量
		SET v_sda_dryl = CAST(p_dcsl AS DECIMAL(14,3)) * CAST(v_sda_drcs AS DECIMAL(14,3));
		
		-- 获取用量单位
		SELECT DISTINCT yl_unit INTO v_yl_unit 
		FROM rms_t_sda_max 
		WHERE sda_id = v_sda_id LIMIT 1;
		
		SELECT DISTINCT UnitRem INTO v_yl_unit1 
		FROM rms_t_sda 
		WHERE id = v_sda_id LIMIT 1;
		
		-- 单位转换
		SET v_gydw_converted = p_gydw;
		IF p_gydw = 'g' THEN
				SET v_gydw_converted = '克';
		ELSEIF p_gydw = 'mg' THEN
				SET v_gydw_converted = '毫克';
		ELSEIF p_gydw = 'ml' THEN  
				SET v_gydw_converted = '毫升';
		END IF;
		
		-- 单位不匹配则退出
		IF v_gydw_converted != v_yl_unit1 THEN
				LEAVE main_block;
		END IF;
		
		-- 如果没有特定单位限制
		IF v_yl_unit IS NULL OR v_yl_unit = '' THEN
				INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT 
						p_code,
						v_ywa_name AS ywa,
						'' AS ywb,
						'1' AS wtlvlcode,
						'重要警示' AS wtlvl,
						'RLT010' AS wtcode,
						'CJLJJ_DRL' AS wtsp,
						'超极量' AS wtname,
						CONCAT('【', CAST(c.tymc AS CHAR), '】', '单日用量超最大日极量') AS title,
						CONCAT('说明书提示：【', CAST(c.tymc AS CHAR), '】 单日最大极量为：', CAST(a.maxcount AS CHAR), v_yl_unit1) AS detail,
						0,
						'超极量单日'
				FROM rms_t_sda c 
				LEFT JOIN rms_t_sda_max a ON a.sda_id = c.ID AND a.jlbs = '1' 
				WHERE c.ID = v_sda_id 
						AND a.gytj_code = v_sda_tj
						AND CAST(a.maxcount AS DECIMAL(14,4)) < v_sda_dryl;
		ELSE
				-- 有特定单位限制
				INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT 
						p_code,
						v_ywa_name AS ywa,
						'' AS ywb,
						'1' AS wtlvlcode,
						'重要警示' AS wtlvl,
						'RLT010' AS wtcode,
						'CJLJJ_DRL' AS wtsp,
						'超极量' AS wtname,
						CONCAT('【', CAST(c.tymc AS CHAR), '】', '单日用量超最大日极量') AS title,
						CONCAT('说明书提示：【', CAST(c.tymc AS CHAR), '】 单日最大极量为：', CAST(a.maxcount AS CHAR), v_yl_unit1) AS detail,
						0,
						'超极量单日'
				FROM rms_t_sda c 
				LEFT JOIN rms_t_sda_max a ON a.sda_id = c.ID AND a.jlbs = '1' 
				WHERE c.ID = v_sda_id 
						AND a.gytj_code = v_sda_tj
						AND CAST(a.maxcount AS DECIMAL(14,4)) < v_sda_dryl 
						AND a.yl_unit = p_gydw;
		END IF;

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_etyy
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_etyy`;
delimiter ;;
CREATE PROCEDURE `rms_fx_etyy`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_csrq VARCHAR(20))
  COMMENT '儿童用药分析存储过程'
main_block: BEGIN
		DECLARE v_nl DECIMAL(18,2);
		DECLARE v_id VARCHAR(20);
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_n_count INT DEFAULT 0;
		DECLARE v_age INT;
		
		-- 如果没有p_Code则返回
		IF p_Code IS NULL OR p_Code = '' THEN
				LEAVE main_block;
		END IF;
		
		-- 获取标准数据ID
		SELECT sda_id INTO v_sda_id 
		FROM rms_t_byyydzb 
		WHERE yp_code = p_yp_code
		LIMIT 1;
		
		-- 如果没有SDA ID则返回
		IF v_sda_id IS NULL OR v_sda_id = '' THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否为中药
		SELECT COUNT(1) INTO v_n_count 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		-- 如果是中药则返回
		IF v_n_count > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code
		LIMIT 1;
		
		-- 计算年龄（将天数转换为年数）
		-- 支持多种日期格式：YYYY-MM-DD 或 YYYY-M-D 或 YYYYMMDD
		SET v_nl = CASE 
				WHEN p_csrq LIKE '%-%' THEN 
						CAST(DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y-%m-%d')) / 365.0 AS DECIMAL(18,2))
				WHEN LENGTH(p_csrq) = 8 THEN
						CAST(DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y%m%d')) / 365.0 AS DECIMAL(18,2))
				ELSE
						CAST(DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y-%m-%d')) / 365.0 AS DECIMAL(18,2))
		END;
		
		-- 如果年龄小于1岁
		IF CAST(v_nl AS DECIMAL(18,2)) < 1 THEN
				-- 找到最接近的年龄ID
				SELECT id INTO v_id 
				FROM rms_t_sda_age 
				ORDER BY ABS(v_nl - age)
				LIMIT 1;
				
				-- 插入儿童用药分析结果
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT 
						p_Code,
						v_ywa_name as ywa,
						'' as ywb,
						CASE WHEN a.result = '0' THEN '1' ELSE '1' END as wtlvlcode,
						(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.result) as wtlvl,
						CASE 
								WHEN a.result = '0' THEN 'RLT031' 
								WHEN a.result = '1' THEN 'RLT032' 
								WHEN a.result = '2' THEN 'RLT034' 
						END as wtcode,
						CASE 
								WHEN a.result = '0' THEN 'etjj' 
								WHEN a.result = '1' THEN 'etwt' 
								WHEN a.result = '2' THEN 'etjjts' 
						END as wtsp,
						CASE 
								WHEN a.result = '0' THEN '特殊人群禁用' 
								WHEN a.result = '1' THEN '特殊人群慎用' 
								WHEN a.result = '2' THEN '特殊人群提示' 
						END as wtname,
						'儿童用药' as title,
						CASE 
								WHEN a.result = '0' THEN CONCAT('说明书提示：', v_ywa_name, '儿童禁用！') 
								ELSE CONCAT('说明书提示：', v_ywa_name, '儿童慎用！') 
						END as detail,
						0 as flag,
						'儿童用药' as text
				FROM rms_t_sda_chd a
				WHERE a.sda_id = v_sda_id
				AND chd_jd_min <= CAST(v_id AS DECIMAL)
				AND chd_jd_max >= CAST(v_id AS DECIMAL)
				AND a.result <> '9';
				
		ELSE
				-- 年龄大于等于1岁
				SET v_age = CEILING(v_nl);
				
				-- 获取对应年龄的ID
				SELECT id INTO v_id 
				FROM rms_t_sda_age 
				WHERE age = v_age
				LIMIT 1;
				
				-- 插入儿童用药分析结果
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT 
						p_Code,
						v_ywa_name as ywa,
						'' as ywb,
						CASE WHEN a.result = '0' THEN '0' ELSE '1' END as wtlvlcode,
						(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.result) as wtlvl,
						CASE 
								WHEN a.result = '0' THEN 'RLT031' 
								WHEN a.result = '1' THEN 'RLT032' 
								WHEN a.result = '2' THEN 'RLT034' 
						END as wtcode,
						CASE 
								WHEN a.result = '0' THEN 'etjj' 
								WHEN a.result = '1' THEN 'etwt' 
								WHEN a.result = '2' THEN 'etjjts' 
						END as wtsp,
						CASE 
								WHEN a.result = '0' THEN '特殊人群禁用' 
								WHEN a.result = '1' THEN '特殊人群慎用' 
								WHEN a.result = '2' THEN '特殊人群提示' 
						END as wtname,
						'儿童用药' as title,
						CASE 
								WHEN a.result = '0' THEN CONCAT('说明书提示：', v_ywa_name, '儿童禁用！') 
								ELSE CONCAT('说明书提示：', v_ywa_name, '儿童慎用！') 
						END as detail,
						0 as flag,
						'儿童用药' as text
				FROM rms_t_sda_chd a
				WHERE a.sda_id = v_sda_id
				AND a.result <> '9'
				AND chd_jd_min <= CAST(v_id AS DECIMAL)
				AND chd_jd_max >= CAST(v_id AS DECIMAL);
		END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_gm
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_gm`;
delimiter ;;
CREATE PROCEDURE `rms_fx_gm`(IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20))
  COMMENT '过敏分析存储过程'
BEGIN
    DECLARE v_sda_id VARCHAR(10);
    DECLARE v_ywa_name VARCHAR(50);
    
    -- 异常处理
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 如果出现异常，继续执行
        BEGIN END;
    END;
    
    -- 获取药品名称
    SELECT DRUG_NAME INTO v_ywa_name 
    FROM rms_itf_hos_drug 
    WHERE DRUG_CODE = p_yp_code
    LIMIT 1;
    
    -- 获取标准数据ID
    SELECT sda_id INTO v_sda_id 
    FROM rms_t_byyydzb 
    WHERE yp_code = p_yp_code
    LIMIT 1;
    
    -- 过敏分析：检查药品过敏信息与处方过敏史的匹配
    INSERT INTO rms_t_pres_fx (
        Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
    )
    SELECT 
        p_code,
        v_ywa_name AS ywa,
        '' AS ywb,
        '1' AS wtlvlcode,
        (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.gmbs LIMIT 1) AS wtlvl,
        CASE 
            WHEN a.gmbs = '0' THEN 'RLT011'  
            WHEN a.gmbs = '1' THEN 'RLT021'
        END AS wtcode,
        CASE 
            WHEN a.gmbs = '0' THEN 'GMJJ'  
            WHEN a.gmbs = '1' THEN 'GMWT'
        END AS wtsp,
        CASE 
            WHEN a.gmbs = '0' THEN '过敏禁用'  
            WHEN a.gmbs = '1' THEN '过敏慎用' 
        END AS wtname,
        CONCAT('过敏分析', v_ywa_name, '过敏分析', 
               CASE 
                   WHEN a.gmbs = '0' THEN '过敏禁用'  
                   WHEN a.gmbs = '1' THEN '过敏慎用' 
               END) AS title,
        CONCAT('说明书提示:', v_ywa_name, 
               CASE 
                   WHEN a.gmbs = '0' THEN '过敏禁用'  
                   WHEN a.gmbs = '1' THEN '过敏慎用' 
               END) AS detail,
        0,
        '过敏问题'
    FROM rms_t_sda_gm a
    WHERE a.sda_id = v_sda_id 
    AND EXISTS (
        SELECT 1 FROM rms_t_pres_gm b 
        WHERE a.gmdm = b.gmdm 
        AND b.code = p_code
    );
    
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_gytj
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_gytj`;
delimiter ;;
CREATE PROCEDURE `rms_fx_gytj`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20))
  COMMENT '给药途径分析存储过程'
main_block: BEGIN
		DECLARE v_sda_id VARCHAR(10);
		DECLARE v_by_code VARCHAR(10);
		DECLARE v_n_count INT;
		DECLARE v_n_count1 INT;
		DECLARE v_n_count2 INT;
		DECLARE v_n_count3 INT;
		DECLARE v_n_count4 INT;
		DECLARE v_n_count5 INT;
		DECLARE v_n_count6 INT;
		DECLARE v_n_count7 INT;
		DECLARE v_n_count8 INT;
		DECLARE v_n_count9 INT;
		DECLARE v_n_count10 INT;
		DECLARE v_n_count11 INT;
		DECLARE v_n_count12 INT;
		DECLARE v_adm_name VARCHAR(50);
		DECLARE v_count7 INT;
		DECLARE v_by_code_2 VARCHAR(10);
		DECLARE v_zx_flag VARCHAR(10);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_gytj_bs VARCHAR(10);
		
		-- 异常处理
		-- 声明异常处理
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
    END;
		
		-- 获取药品基本信息
		SELECT DRUG_NAME, zx_flag INTO v_ywa_name, v_zx_flag 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code
		LIMIT 1;
		
		-- 获取标准数据信息
		SELECT b.sda_id, a.gytj_bs INTO v_sda_id, v_gytj_bs 
		FROM rms_t_sda a, rms_t_byyydzb b 
		WHERE a.ID = b.sda_id AND yp_code = p_yp_code
		LIMIT 1;
		
		-- 获取给药途径编码
		SELECT by_code INTO v_by_code 
		FROM rms_t_tjdzb 
		WHERE h_tj = p_yp_tj
		LIMIT 1;
		
		-- 如果没有标准数据ID则返回
		IF v_sda_id = '' OR v_sda_id IS NULL THEN
				LEAVE main_block;
		END IF;
		
		-- 中药且给药途径标识为0则返回
		IF v_zx_flag = '3' AND v_gytj_bs = '0' THEN
				LEAVE main_block;
		END IF;
		
		-- 检查中药颗粒/免煎/粉/M/Z的特殊情况
		SELECT COUNT(1) INTO v_n_count4 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND zx_flag = '3' 
		AND (drug_name LIKE '%颗粒%' OR drug_name LIKE '%免煎%' 
				OR drug_name LIKE '%粉%' OR drug_name LIKE '%M%' 
				OR drug_name LIKE '%Z%');
		
		IF v_n_count4 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查中药足浴/外用/打粉的特殊情况
		SELECT COUNT(1) INTO v_count7 
		FROM rms_t_pres_med 
		WHERE Code = p_Code AND his_code = p_yp_code 
		AND (yysm IN ('足浴','外用','打粉')
			OR yysm LIKE '%外%'
			OR yysm LIKE '%打粉%');
		
		IF v_count7 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否为溶媒
		SELECT COUNT(1) INTO v_n_count6 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND is_rm = '1';
		
		IF v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否是中药
		SELECT COUNT(1) INTO v_n_count3 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		-- 检查是否有给药途径设置
		SELECT COUNT(1) INTO v_n_count11 
		FROM rms_t_sda_gytj a
		WHERE a.sda_id = v_sda_id;

		-- 如果没有给药途径设置且是中药且给药途径不为空，提示内服不能无煎药方式
		IF v_n_count11 = 0 AND v_n_count3 = 1 AND p_yp_tj != '' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'草药煎药方法错误', 
						CONCAT('【', v_ywa_name, '】无需特殊煎药方式'), '0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;

		-- 如果没有给药途径设置则返回
		IF v_n_count11 = 0 THEN
				LEAVE main_block;
		END IF;

		-- 如果给药途径为空且不是中药则返回
		IF (v_by_code = '' OR v_by_code IS NULL) AND v_n_count3 = 0 THEN
				LEAVE main_block;
		END IF;

		-- 检查是否有自定义给药途径
		IF EXISTS(SELECT 1 FROM rms_t_med_zdy_gytj WHERE yp_code = p_yp_code AND gytj_code = p_yp_tj) THEN
				LEAVE main_block;
		END IF;
		
		-- 中药给药途径检查
		IF v_n_count3 = 1 THEN
				SELECT COUNT(1) INTO v_n_count
				FROM rms_t_sda b, rms_t_sda_gytj a 
				WHERE a.sda_id = b.ID 
				AND b.id = v_sda_id
				AND a.gytj_code = p_yp_tj
				AND a.bs = '0';
				
				IF v_n_count > 0 THEN
						LEAVE main_block;
				END IF;
		END IF;

		-- 西药给药途径检查
		IF v_n_count3 = 0 THEN
				SELECT COUNT(1) INTO v_n_count
				FROM rms_t_sda b, rms_t_sda_gytj a 
				WHERE a.sda_id = b.ID 
				AND b.id = v_sda_id
				AND a.gytj_code IN (
						SELECT by_code FROM rms_t_tjdzb WHERE h_tj = p_yp_tj
				)
				AND a.bs = '0';
				
				IF v_n_count > 0 THEN
						LEAVE main_block;
				END IF;
		END IF;
		
		-- 中药煎药方式为空的处理
		IF v_zx_flag = '3' AND v_gytj_bs = '1' AND p_yp_tj = '' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'草药煎药方法错误',
						CONCAT('【', v_ywa_name, '】的煎药方式不能为空，建议使用【', rms_get_gytj(v_sda_id), '】煎药方法'), 
						'0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;
		
		-- 中药给药途径不匹配的处理
		IF v_n_count3 > 0 AND v_n_count = 0 AND v_gytj_bs = '1' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'草药煎药方法错误',
						CONCAT('【', v_ywa_name, '】【中国药典2020版】未提及该煎药方法！，建议使用【', rms_get_gytj(v_sda_id), '】煎药方法'), 
						'0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;

		-- 获取其他检查参数
		SELECT COUNT(1) INTO v_n_count10 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code 
		AND DRUG_NAME LIKE '%醋酸戈舍%';
		
		SELECT COUNT(1) INTO v_n_count7 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code 
		AND DRUG_FOR_NAME NOT LIKE '%注射%' 
		AND DRUG_FOR_NAME NOT LIKE '%针%';
		
		SELECT SUBSTRING(by_code, 1, 2) INTO v_by_code_2 
		FROM rms_t_tjdzb 
		WHERE akb020 = p_akb020 AND h_tj = p_yp_tj
		LIMIT 1;
		
		SELECT COUNT(1) INTO v_n_count8 
		FROM rms_t_sda a, rms_t_byyydzb b 
		WHERE b.yp_code = p_yp_code AND a.ID = b.sda_id 
		AND (a.ym LIKE '%胰岛%' OR a.ym LIKE '%膏%');
		
		-- 西药特殊给药途径检查
		IF v_n_count3 = 0 THEN
				IF (v_n_count10 = 0 AND v_n_count7 > 0 AND v_by_code_2 = '02') 
				OR (v_n_count8 > 0 AND v_by_code_2 = '01') THEN
						INSERT INTO rms_t_pres_fx (
								Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
						)
						VALUES (
								p_Code, v_ywa_name, '', '0', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
								CONCAT('【', v_ywa_name, '】给药途径错误'),
								CONCAT('【', v_ywa_name, '】说明书未提及该给药途径'), '0', '给药途径错误'
						);
						LEAVE main_block;
				END IF;
		END IF;
		
		-- 默认给药途径错误处理
		INSERT INTO rms_t_pres_fx (
				Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		VALUES (
				p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
				CONCAT('【', v_ywa_name, '】给药途径错误'),
				CONCAT('【', v_ywa_name, '】说明书未提及该给药途径'), '0', '给药途径错误'
		);
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_lnr
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_lnr`;
delimiter ;;
CREATE PROCEDURE `rms_fx_lnr`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_csrq VARCHAR(20))
  COMMENT '老年人用药分析存储过程'
main_block: BEGIN
		DECLARE v_nl INT;
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_n_count_zy INT;
		DECLARE v_ywa_name VARCHAR(50);
		
		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				-- 如果出现异常，继续执行
				BEGIN END;
		END;
		
		-- 获取标准数据ID
		SELECT sda_id INTO v_sda_id 
		FROM rms_t_byyydzb 
		WHERE yp_code = p_yp_code
		LIMIT 1;
		
		-- 如果没有标准数据ID则返回
		IF v_sda_id = '' OR v_sda_id IS NULL THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否为中药
		SELECT COUNT(1) INTO v_n_count_zy 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		-- 如果是中药则返回
		IF v_n_count_zy > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code
		LIMIT 1;
		
		-- 计算年龄（年数）
		-- 支持多种日期格式：YYYY-MM-DD 或 YYYY-M-D 或 YYYYMMDD
		SET v_nl = CASE 
				WHEN p_csrq LIKE '%-%' THEN 
						YEAR(CURDATE()) - YEAR(STR_TO_DATE(p_csrq, '%Y-%m-%d'))
				WHEN LENGTH(p_csrq) = 8 THEN
						YEAR(CURDATE()) - YEAR(STR_TO_DATE(p_csrq, '%Y%m%d'))
				ELSE
						YEAR(CURDATE()) - YEAR(STR_TO_DATE(p_csrq, '%Y-%m-%d'))
		END;
		
		-- 老年人用药分析：插入分析结果
		INSERT INTO rms_t_pres_fx (
				Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		SELECT 
				p_Code,
				v_ywa_name as ywa,
				'' as ywb,
				'1' as wtlvlcode,
				(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.jsbs LIMIT 1) as wtlvl,
				CASE 
						WHEN a.jsbs = 0 THEN 'RLT031' 
						WHEN a.jsbs = 1 THEN 'RLT032' 
						WHEN a.jsbs = 2 THEN 'RLT034' 
				END as wtcode,
				CASE 
						WHEN a.jsbs = 0 THEN 'LNRJJ' 
						WHEN a.jsbs = 1 THEN 'LNRWT' 
						WHEN a.jsbs = 2 THEN 'LNRJJTS' 
				END as wtsp,
				CASE 
						WHEN a.jsbs = 0 THEN '特殊人群禁用' 
						WHEN a.jsbs = 1 THEN '特殊人群慎用' 
						WHEN a.jsbs = 2 THEN '特殊人群提示' 
				END as wtname,
				CASE 
						WHEN a.jsbs = 0 THEN '老年人禁用药品' 
						WHEN a.jsbs = 1 THEN '老年人慎用药品' 
						WHEN a.jsbs = 2 THEN '老年人提示药品' 
				END as title,
				CASE 
						WHEN a.jsbs = 0 THEN CONCAT('说明书提示：', v_ywa_name, '老年人禁用！') 
						WHEN a.jsbs = 1 THEN CONCAT('说明书提示：', v_ywa_name, '老年人慎用！') 
						WHEN a.jsbs = 2 THEN CONCAT('说明书提示：', v_ywa_name, '老年人用药注意事项！') 
				END as detail,
				0 as flag,
				'老年人用药' as text
		FROM rms_t_sda_elder a
		WHERE a.sda_id = v_sda_id
		AND a.jsbs <> 9
		AND a.age_min <= v_nl;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_main
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_main`;
delimiter ;;
CREATE PROCEDURE `rms_fx_main`(IN p_code VARCHAR(50),              -- 处方编码（必填）
    IN p_akb020 VARCHAR(20),            -- 医保编码（可选）
    OUT p_result_code INT,              -- 返回结果代码：0-成功，1-警告，2-错误
    OUT p_result_message VARCHAR(1000))
  COMMENT '合理用药分析主存储过程'
main_proc: BEGIN
    -- 声明变量
    DECLARE v_error_count INT DEFAULT 0;
    DECLARE v_warning_count INT DEFAULT 0;
    DECLARE v_total_issues INT DEFAULT 0;
    DECLARE v_step_name VARCHAR(100);
    DECLARE v_med_count INT DEFAULT 0;
    DECLARE v_current_med_code VARCHAR(50);
    DECLARE v_current_med_name VARCHAR(200);
    DECLARE v_current_gytj VARCHAR(20);
    DECLARE v_current_dose VARCHAR(50);
    DECLARE v_current_dose_unit VARCHAR(50);
    DECLARE v_current_freq VARCHAR(50);
    DECLARE v_current_days VARCHAR(50);
    DECLARE v_done INT DEFAULT FALSE;
    
    -- 重复用药分析变量
    DECLARE v_med_code_a VARCHAR(50);
    DECLARE v_med_code_b VARCHAR(50);
    DECLARE v_done_duplicate INT DEFAULT FALSE;
    
    -- 从数据库自动获取的患者信息变量
    DECLARE v_patient_birth VARCHAR(20) DEFAULT NULL;
    DECLARE v_patient_weight VARCHAR(20) DEFAULT NULL;
    DECLARE v_patient_sex VARCHAR(2) DEFAULT NULL;
    DECLARE v_pregnant_unit VARCHAR(10) DEFAULT NULL;
    DECLARE v_pregnant VARCHAR(10) DEFAULT NULL;
    DECLARE v_allergy_codes VARCHAR(500) DEFAULT NULL;
    DECLARE v_diagnosis_codes VARCHAR(500) DEFAULT NULL;
    DECLARE v_allergy_from_table VARCHAR(500) DEFAULT NULL;
    DECLARE v_patient_name VARCHAR(30) DEFAULT NULL;
    
    -- 声明游标
    DECLARE main_cursor CURSOR FOR 
        SELECT his_code, med_name, administer, dose, dose_unit, freq, days
        FROM rms_t_pres_med 
        WHERE code = p_code;
    
    DECLARE duplicate_cursor CURSOR FOR 
        SELECT med_code_a, med_code_b FROM temp_med_pairs;
    
    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
        SET p_result_code = 2;
        SET p_result_message = CONCAT('执行异常: ', v_step_name, ' - ', @text);
        -- 清理临时表
        DROP TEMPORARY TABLE IF EXISTS temp_med_info;
        DROP TEMPORARY TABLE IF EXISTS temp_med_pairs;
        ROLLBACK;
				select p_result_message;
    END;
    
    -- 统一的 NOT FOUND 处理器
    DECLARE CONTINUE HANDLER FOR NOT FOUND 
    BEGIN
        SET v_done = TRUE;
        SET v_done_duplicate = TRUE;
    END;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 初始化返回值
    SET p_result_code = 0;
    SET p_result_message = '合理用药分析完成';
    
    -- ====================================================================
    -- 第一步：参数验证和患者信息获取
    -- ====================================================================
    SET v_step_name = '参数验证和数据获取';
    
    -- 检查必要参数
    IF p_code IS NULL OR p_code = '' THEN
        SET p_result_code = 2;
        SET p_result_message = '处方编码不能为空';
        ROLLBACK;
        LEAVE main_proc;
    END IF;
    
    -- 从处方表获取患者基本信息
    SELECT 
        birth, CAST(weight AS CHAR), sex, pregnant_unit, pregnant, 
        all_info, dia_info, name
    INTO 
        v_patient_birth, v_patient_weight, v_patient_sex, v_pregnant_unit, 
        v_pregnant, v_allergy_codes, v_diagnosis_codes, v_patient_name
    FROM rms_t_pres 
    WHERE code = p_code;

    -- 检查处方是否存在
    IF v_patient_name IS NULL THEN
        SET p_result_code = 2;
        SET p_result_message = '处方不存在';
        ROLLBACK;
        LEAVE main_proc;
    END IF;
    
    -- 检查处方明细是否存在
    SELECT COUNT(*) INTO v_med_count FROM rms_t_pres_med WHERE code = p_code;
    IF v_med_count = 0 THEN
        SET p_result_code = 2;
        SET p_result_message = '处方明细不存在';
        ROLLBACK;
        LEAVE main_proc;
    END IF;
    
    -- 从过敏信息表获取过敏代码（如果主表中没有）
    IF v_allergy_codes IS NULL OR v_allergy_codes = '' THEN
        SELECT GROUP_CONCAT(gmdm SEPARATOR ',') INTO v_allergy_from_table
        FROM rms_t_pres_gm WHERE code = p_code;
        
        IF v_allergy_from_table IS NOT NULL AND v_allergy_from_table != '' THEN
            SET v_allergy_codes = v_allergy_from_table;
        END IF;
    END IF;
    
    -- 数据清理和验证
    SET v_allergy_codes = TRIM(IFNULL(v_allergy_codes, ''));
    SET v_diagnosis_codes = TRIM(IFNULL(v_diagnosis_codes, ''));
    SET v_patient_birth = TRIM(IFNULL(v_patient_birth, ''));
    SET v_patient_weight = TRIM(IFNULL(v_patient_weight, ''));
    SET v_patient_sex = TRIM(IFNULL(v_patient_sex, ''));
    SET v_pregnant_unit = TRIM(IFNULL(v_pregnant_unit, ''));
    SET v_pregnant = TRIM(IFNULL(v_pregnant, ''));
    
    -- 数据格式验证和修正
    IF v_patient_birth != '' AND v_patient_birth NOT REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' THEN
        IF LENGTH(v_patient_birth) = 8 AND v_patient_birth REGEXP '^[0-9]{8}$' THEN
            SET v_patient_birth = CONCAT(SUBSTRING(v_patient_birth, 1, 4), '-', 
                                       SUBSTRING(v_patient_birth, 5, 2), '-', 
                                       SUBSTRING(v_patient_birth, 7, 2));
        ELSE
            SET v_patient_birth = '';
        END IF;
    END IF;

    -- 验证性别值
    IF v_patient_sex NOT IN ('1', '2', '男', '女') THEN
        SET v_patient_sex = '';
    ELSEIF v_patient_sex = '男' THEN
        SET v_patient_sex = '1';
    ELSEIF v_patient_sex = '女' THEN
        SET v_patient_sex = '2';
    END IF;
    
    -- 验证体重值
    IF v_patient_weight != '' AND v_patient_weight NOT REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN
        SET v_patient_weight = '';
    END IF;
    
    -- ====================================================================
    -- 第二步：创建临时表用于性能优化
    -- ====================================================================
    SET v_step_name = '创建临时表';
    
    -- 创建药品信息临时表
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_med_info (
        seq_no INT AUTO_INCREMENT PRIMARY KEY,
        his_code VARCHAR(50),
        med_name VARCHAR(200),
        administer VARCHAR(50),
        dose VARCHAR(50),
        dose_unit VARCHAR(50),
        freq VARCHAR(50),
        days VARCHAR(50),
        INDEX idx_his_code (his_code)
    ) ENGINE=MEMORY;
    
    -- 创建药品组合临时表（用于重复用药分析）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_med_pairs (
        med_code_a VARCHAR(50),
        med_code_b VARCHAR(50),
        PRIMARY KEY (med_code_a, med_code_b)
    ) ENGINE=MEMORY;
    
    -- 清空临时表
    DELETE FROM temp_med_info;
    DELETE FROM temp_med_pairs;

    -- ====================================================================
    -- 第三步：统一主循环 - 一次遍历完成所有单药品分析
    -- ====================================================================
    SET v_step_name = '统一药品分析循环';
    SET v_done = FALSE;

    OPEN main_cursor;
    main_loop: LOOP
        FETCH main_cursor INTO v_current_med_code, v_current_med_name,
                              v_current_gytj, v_current_dose, v_current_dose_unit,
                              v_current_freq, v_current_days;
        IF v_done THEN
            LEAVE main_loop;
        END IF;

        -- 存储药品信息到临时表（用于后续重复用药分析）
        INSERT INTO temp_med_info (his_code, med_name, administer, dose, dose_unit, freq, days)
        VALUES (v_current_med_code, v_current_med_name, v_current_gytj,
                v_current_dose, v_current_dose_unit, v_current_freq, v_current_days);

        -- 在同一循环中完成所有单药品分析（性能优化核心）

        -- 3.1 过敏分析
        IF v_allergy_codes IS NOT NULL AND v_allergy_codes != '' THEN
            SET v_step_name = '过敏分析';
            CALL rms_fx_gm(p_code, p_akb020, v_current_med_code);
        END IF;

        -- 3.2 给药途径分析
        SET v_step_name = '给药途径分析';
        CALL rms_fx_gytj(p_code, p_akb020, v_current_med_code, v_current_gytj);

        -- 3.3 儿童用药分析
        IF v_patient_birth IS NOT NULL AND v_patient_birth != '' THEN
            SET v_step_name = '儿童用药分析';
            CALL rms_fx_etyy(p_code, p_akb020, v_current_med_code, v_patient_birth);
        END IF;

        -- 3.4 老年人用药分析
        IF v_patient_birth IS NOT NULL AND v_patient_birth != '' THEN
            SET v_step_name = '老年人用药分析';
--             CALL rms_fx_lnr(p_code, p_akb020, v_current_med_code, v_patient_birth);
        END IF;

        -- 3.5 孕妇用药分析
        IF v_patient_sex = '2' AND v_pregnant_unit IS NOT NULL AND v_pregnant IS NOT NULL THEN
            SET v_step_name = '孕妇用药分析';
--             CALL rms_fx_yfyy(p_code, p_akb020, v_current_med_code, v_pregnant_unit, v_pregnant);
        END IF;

        -- 3.6 常规用量分析
        SET v_step_name = '常规用量分析';
--         CALL rms_fx_cgl(p_code, p_akb020, v_current_med_code, v_current_gytj,
--                        v_current_dose, v_current_dose_unit, v_current_freq,
--                        v_patient_birth, v_patient_weight);

        -- 3.7 常规用量分析（日用量）
--         CALL rms_fx_cgl_drl(p_code, p_akb020, v_current_med_code, v_current_gytj,
--                            v_current_dose, v_current_dose_unit, v_current_freq,
--                            v_patient_birth, v_patient_weight);

        -- 3.8 单次超极量分析
        SET v_step_name = '单次超极量分析';
--         CALL rms_fx_cjl(p_code, p_akb020, v_current_med_code, v_current_gytj,
--                        v_current_dose, v_current_dose_unit, v_current_freq);

        -- 3.9 单日超极量分析
        SET v_step_name = '单日超极量分析';
--         CALL rms_fx_cjl_drl(p_code, p_akb020, v_current_med_code, v_current_gytj,
--                            v_current_dose, v_current_dose_unit, v_current_freq);

        -- 3.10 适应症分析
        IF v_diagnosis_codes IS NOT NULL AND v_diagnosis_codes != '' THEN
            SET v_step_name = '适应症分析';
--             CALL rms_fx_syz(p_code, p_akb020, v_current_med_code, v_diagnosis_codes);
        END IF;

        -- 3.11 药品用药总量分析
        SET v_step_name = '药品用药总量分析';
--         CALL rms_fx_yuliu_med(p_code, v_current_med_code);

    END LOOP;
    CLOSE main_cursor;

    -- ====================================================================
    -- 第四步：重复用药和配伍禁忌分析优化
    -- ====================================================================

    -- 4.1 生成药品组合（避免重复比较，性能优化关键）
--     INSERT INTO temp_med_pairs (med_code_a, med_code_b)
--     SELECT a.his_code, b.his_code
--     FROM temp_med_info a
--     CROSS JOIN temp_med_info b
--     WHERE a.seq_no < b.seq_no;  -- 只生成上三角组合，避免A-B和B-A重复

    -- 4.2 配伍禁忌分析（不需要遍历，直接调用）
    SET v_step_name = '配伍禁忌分析';
--     CALL rms_fx_pwjj(p_code);

    -- 4.3 重复用药分析（rms_fx_cfyy 的实际功能）
    -- 功能说明：检查同种药品和同成分药品的重复使用
--     SET v_step_name = '重复用药分析';
--     SET v_done_duplicate = FALSE;
-- 
--     OPEN duplicate_cursor;
--     duplicate_loop: LOOP
--         FETCH duplicate_cursor INTO v_med_code_a, v_med_code_b;
--         IF v_done_duplicate THEN
--             LEAVE duplicate_loop;
--         END IF;
-- 
--         -- 调用重复用药分析存储过程
--         -- 该存储过程检查：1) 是否为同种药品 2) 是否含有相同成分
--         CALL rms_fx_cfyy(p_code, p_akb020, v_med_code_a, v_med_code_b);
-- 
--     END LOOP;
--     CLOSE duplicate_cursor;

    -- ====================================================================
    -- 第五步：综合检查和结果汇总
    -- ====================================================================

    -- 5.1 处方余留综合检查
    SET v_step_name = '处方余留综合检查';
--     CALL rms_fx_yuliu(p_code, v_allergy_codes);

    -- ====================================================================
    -- 第六步：结果统计和返回
    -- ====================================================================
    SET v_step_name = '结果统计';

    -- 统计分析结果
    SELECT
        COUNT(CASE WHEN wtlvlcode IN ('1', '2') THEN 1 END),
        COUNT(CASE WHEN wtlvlcode IN ('3', '4') THEN 1 END),
        COUNT(*)
    INTO v_error_count, v_warning_count, v_total_issues
    FROM rms_t_pres_fx
    WHERE code = p_code;

    -- 设置返回结果
    IF v_error_count > 0 THEN
        SET p_result_code = 2;
        SET p_result_message = CONCAT('发现严重问题 ', v_error_count, ' 个，警告 ', v_warning_count, ' 个');
    ELSEIF v_warning_count > 0 THEN
        SET p_result_code = 1;
        SET p_result_message = CONCAT('发现警告 ', v_warning_count, ' 个');
    ELSE
        SET p_result_code = 0;
        SET p_result_message = '合理用药分析完成，未发现问题';
    END IF;

    -- ====================================================================
    -- 第七步：资源清理
    -- ====================================================================
    SET v_step_name = '资源清理';

    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_med_info;
    DROP TEMPORARY TABLE IF EXISTS temp_med_pairs;

    -- 提交事务
    COMMIT;

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_pwjj
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_pwjj`;
delimiter ;;
CREATE PROCEDURE `rms_fx_pwjj`(IN p_code VARCHAR(50))
  READS SQL DATA 
  DETERMINISTIC
  COMMENT '配伍禁忌分析存储过程'
BEGIN
    main_block: BEGIN
        DECLARE v_zxybs VARCHAR(10);
        DECLARE v_n_count INT;
        DECLARE v_CID_a VARCHAR(10);
        DECLARE v_ywa VARCHAR(50);
        DECLARE v_htja VARCHAR(50);
        DECLARE v_zuhaoa VARCHAR(50);
        DECLARE v_CID_b VARCHAR(10);
        DECLARE v_ywb VARCHAR(50);
        DECLARE v_htjb VARCHAR(50);
        DECLARE v_zuhaob VARCHAR(50);
        DECLARE v_tja VARCHAR(50);
        DECLARE v_tjb VARCHAR(50);
        DECLARE done INT DEFAULT FALSE;
        
        -- 游标变量声明
        DECLARE cursor_drug_pwjj CURSOR FOR 
            SELECT CID_a, ywa, tja, zuhaoa, CID_b, ywb, tjb, zuhaob 
            FROM temp_xy_pwjj;
        
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
        
        -- 异常处理
        DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
        BEGIN
            -- 如果出现异常，继续执行
            BEGIN END;
        END;
        
        -- 获取处方类型
        SELECT prescription_type INTO v_zxybs 
        FROM rms_t_pres 
        WHERE code = p_code
        LIMIT 1;
        
        -- 中药处方分析
        IF v_zxybs = '2' THEN
            -- 创建中药临时表
            DROP TEMPORARY TABLE IF EXISTS temp_drug_info_zy;
            CREATE TEMPORARY TABLE temp_drug_info_zy (
                sda_id VARCHAR(20),
                cid VARCHAR(10),
                med_name VARCHAR(100)
            );
            
            -- 插入中药信息（排除重复药品码和需要相互作用标识的药品）
            INSERT INTO temp_drug_info_zy
            SELECT b.sda_id, b.cid, a.med_name 
            FROM rms_t_pres_med a, rms_t_byyydzb b
            WHERE a.his_code = b.yp_code
            AND a.code = p_code
            AND b.yp_code NOT IN (
                SELECT yp_code FROM rms_t_byyydzb  
                GROUP BY yp_code
                HAVING COUNT(1) > 1
            )
            AND EXISTS (
                SELECT 1 FROM rms_t_sda 
                WHERE ID = b.sda_id AND xhzy = '1'
            );
            
            SELECT COUNT(1) INTO v_n_count FROM temp_drug_info_zy;
            
            IF v_n_count <= 1 THEN
                DROP TEMPORARY TABLE temp_drug_info_zy;
                LEAVE main_block;
            END IF;
            
            -- 创建中药配伍禁忌组合表
            DROP TEMPORARY TABLE IF EXISTS temp_zy_pwjj;
            CREATE TEMPORARY TABLE temp_zy_pwjj (
                CID_a VARCHAR(10),
                ywa VARCHAR(50),
                CID_b VARCHAR(10),
                ywb VARCHAR(50)
            );
            
            INSERT INTO temp_zy_pwjj
            SELECT a.CID, a.med_name, b.CID, b.med_name
            FROM temp_drug_info_zy a, temp_drug_info_zy b
            WHERE a.cid <> b.cid
            AND CAST(a.cid AS SIGNED) < CAST(b.cid AS SIGNED)
            ORDER BY CAST(a.cid AS SIGNED);
            
            -- 插入中药相互作用分析结果
            INSERT INTO rms_t_pres_fx
            SELECT 
                p_code,
                b.ywa AS ywa,
                b.ywb AS ywb,
                '1' AS wtlvlcode,
                (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.result) AS wtlvl,
                CASE 
                    WHEN a.result = '0' THEN 'RLT002' 
                    WHEN a.result = '1' THEN 'RLT014' 
                    WHEN a.result = '2' THEN 'RLT024'  
                END AS wtcode,
                CASE 
                    WHEN a.result = '0' THEN 'PWJJ' 
                    WHEN a.result = '1' THEN 'PWWT' 
                    WHEN a.result = '2' THEN 'XHZYTS' 
                END AS wtsp,
                CASE 
                    WHEN a.result = '0' THEN '相互作用禁忌' 
                    WHEN a.result = '1' THEN '相互作用问题' 
                    WHEN a.result = '2' THEN '相互作用'  
                END AS wtname,
                CONCAT('【', b.ywa, '】【', b.ywb, '】存在', 
                    CASE 
                        WHEN a.result = '0' THEN '相互作用禁忌' 
                        WHEN a.result = '1' THEN '相互作用问题' 
                        WHEN a.result = '2' THEN '相互作用'  
                    END) AS title,
                CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), 
                    '; 机制：', IFNULL(CAST(a.mechanism AS CHAR(4000)), ''), 
                    '; ', IFNULL(CONCAT('参考文献:', CAST(a.reference AS CHAR(4000))), '')) AS detail,
                0,
                '配伍问题'
            FROM rms_t_xhzy_edi_zy a, temp_zy_pwjj b
            WHERE (a.yaowuA = b.CID_a AND a.yaowuB = b.CID_b)
            AND a.sugflag = 'xhzy'
            AND a.ispb = '0';
            
            -- 清理临时表
            DROP TEMPORARY TABLE temp_drug_info_zy;
            DROP TEMPORARY TABLE temp_zy_pwjj;
        END IF;
        
        -- 西药处方分析
        IF v_zxybs = '1' THEN
            -- 创建西药临时表
            DROP TEMPORARY TABLE IF EXISTS temp_drug_info_xy;
            CREATE TEMPORARY TABLE temp_drug_info_xy (
                sda_id VARCHAR(20),
                cid VARCHAR(10),
                med_name VARCHAR(100),
                administer VARCHAR(50),
                group_no VARCHAR(50)
            );
            
            -- 插入西药信息（排除重复药品码）
            INSERT INTO temp_drug_info_xy
            SELECT b.sda_id, b.cid, a.med_name, a.administer, a.`group`
            FROM rms_t_pres_med a, rms_t_byyydzb b
            WHERE a.his_code = b.yp_code
            AND a.code = p_code
            AND b.yp_code NOT IN (
                SELECT yp_code FROM rms_t_byyydzb  
                GROUP BY yp_code
                HAVING COUNT(1) > 1
            );
            
            SELECT COUNT(1) INTO v_n_count FROM temp_drug_info_xy;
            
            IF v_n_count <= 1 THEN
                DROP TEMPORARY TABLE temp_drug_info_xy;
                LEAVE main_block;
            END IF;
            
            -- 创建西药配伍禁忌组合表
            DROP TEMPORARY TABLE IF EXISTS temp_xy_pwjj;
            CREATE TEMPORARY TABLE temp_xy_pwjj (
                CID_a VARCHAR(10),
                ywa VARCHAR(50),
                tja VARCHAR(50),
                zuhaoa VARCHAR(50),
                CID_b VARCHAR(10),
                ywb VARCHAR(50),
                tjb VARCHAR(50),
                zuhaob VARCHAR(50)
            );
            
            INSERT INTO temp_xy_pwjj
            SELECT a.CID, a.med_name, a.administer, a.group_no, 
                b.CID, b.med_name, b.administer, b.group_no
            FROM temp_drug_info_xy a, temp_drug_info_xy b
            WHERE a.cid <> b.cid
            AND CAST(a.cid AS SIGNED) < CAST(b.cid AS SIGNED)
            ORDER BY CAST(a.cid AS SIGNED);
            
            -- 使用游标处理每个药品组合
            OPEN cursor_drug_pwjj;
            
            cursor_loop: LOOP
                FETCH cursor_drug_pwjj INTO v_CID_a, v_ywa, v_htja, v_zuhaoa, 
                                        v_CID_b, v_ywb, v_htjb, v_zuhaob;
                IF done THEN
                    LEAVE cursor_loop;
                END IF;
                
                -- 获取给药途径编码
                SELECT by_code INTO v_tja 
                FROM rms_t_tjdzb 
                WHERE h_tj = v_htja 
                LIMIT 1;
                
                SELECT by_code INTO v_tjb 
                FROM rms_t_tjdzb 
                WHERE h_tj = v_htjb 
                LIMIT 1;
                
                -- 静脉注射且同组的配伍禁忌
                IF v_tja LIKE '02%' AND v_tjb LIKE '02%' AND v_zuhaoa = v_zuhaob THEN
                    INSERT INTO rms_t_pres_fx
                    SELECT 
                        p_code,
                        v_ywa AS ywa,
                        v_ywb AS ywb,
                        '1' AS wtlvlcode,
                        (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.result) AS wtlvl,
                        CASE 
                            WHEN a.result = '0' THEN 'RLT001' 
                            WHEN a.result = '1' THEN 'RLT013'  
                        END AS wtcode,
                        CASE 
                            WHEN a.result = '0' THEN 'PWJJ' 
                            WHEN a.result = '1' THEN 'PWWT' 
                        END AS wtsp,
                        CASE 
                            WHEN a.result = '0' THEN '配伍禁忌' 
                            WHEN a.result = '1' THEN '配伍问题'  
                        END AS wtname,
                        CONCAT('【', v_ywa, '】【', v_ywb, '】存在', 
                            CASE 
                                WHEN a.result = '0' THEN '配伍禁忌' 
                                WHEN a.result = '1' THEN '配伍问题' 
                            END) AS title,
                        CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), 
                            '; 机制：', IFNULL(CAST(a.mechanism AS CHAR(4000)), ''), 
                            '; ', IFNULL(CONCAT('参考文献:', CAST(a.reference AS CHAR(4000))), '')) AS detail,
                        0,
                        '配伍问题'
                    FROM rms_t_xhzy_edi a
                    WHERE (a.yaowuA = v_CID_a AND a.yaowuB = v_CID_b)
                    AND a.sugflag = 'pwjj' 
                    AND a.ispb = '0';
                ELSE
                    -- 其他情况的相互作用分析
                    INSERT INTO rms_t_pres_fx
                    SELECT 
                        p_code,
                        v_ywa AS ywa,
                        v_ywb AS ywb,
                        '1' AS wtlvlcode,
                        (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.result) AS wtlvl,
                        CASE 
                            WHEN a.result = '0' THEN 'RLT002' 
                            WHEN a.result = '1' THEN 'RLT014' 
                            WHEN a.result = '2' THEN 'RLT024'  
                        END AS wtcode,
                        CASE 
                            WHEN a.result = '0' THEN 'PWJJ' 
                            WHEN a.result = '1' THEN 'PWWT' 
                            WHEN a.result = '2' THEN 'XHZYTS' 
                        END AS wtsp,
                        CASE 
                            WHEN a.result = '0' THEN '相互作用禁忌' 
                            WHEN a.result = '1' THEN '相互作用问题' 
                            WHEN a.result = '2' THEN '相互作用'  
                        END AS wtname,
                        CONCAT('【', v_ywa, '】【', v_ywb, '】存在', 
                            CASE 
                                WHEN a.result = '0' THEN '相互作用禁忌' 
                                WHEN a.result = '1' THEN '相互作用问题' 
                                WHEN a.result = '2' THEN '相互作用'  
                            END) AS title,
                        CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), 
                            '; 机制：', IFNULL(CAST(a.mechanism AS CHAR(4000)), ''), 
                            '; ', IFNULL(CONCAT('参考文献:', CAST(a.reference AS CHAR(4000))), '')) AS detail,
                        0,
                        '配伍问题'
                    FROM rms_t_xhzy_edi a
                    WHERE (a.yaowuA = v_CID_a AND a.yaowuB = v_CID_b) 
                    AND a.sugflag = 'xhzy'
                    AND a.ispb = '0';
                END IF;
                
            END LOOP;
            
            CLOSE cursor_drug_pwjj;
            
            -- 清理临时表
            DROP TEMPORARY TABLE temp_drug_info_xy;
            DROP TEMPORARY TABLE temp_xy_pwjj;
        END IF;
    END main_block;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_syz
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_syz`;
delimiter ;;
CREATE PROCEDURE `rms_fx_syz`(IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),  -- 该参数无用，仅用来补位
    IN p_icd10_codes VARCHAR(500))
  COMMENT '适应症分析存储过程'
main_block: BEGIN
		DECLARE v_sda_id VARCHAR(10) DEFAULT NULL;
		DECLARE v_diagnoses VARCHAR(10);
		DECLARE v_n_count INT;
		DECLARE v_n_count1 INT DEFAULT 0;
		DECLARE v_n_count2 INT;
		DECLARE v_n_count4 INT;
		DECLARE v_n_count6 INT;
		DECLARE v_n_count7 INT;
		DECLARE v_n_count8 INT;
		DECLARE v_n_count9 INT;
		DECLARE v_n_count12 INT;
		DECLARE v_n_count13 INT;
		DECLARE v_n_count133 INT;
		DECLARE v_n_count144 INT;
		DECLARE v_n_count18 INT;
		DECLARE v_icd10_code VARCHAR(20);
		DECLARE v_hosp_flag VARCHAR(20);
		DECLARE v_zdxx VARCHAR(500);
		DECLARE v_icd10_name VARCHAR(500);
		DECLARE v_remark VARCHAR(500);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_his_code VARCHAR(100);
		DECLARE v_med_name VARCHAR(100);
		DECLARE v_icd10_codes_work VARCHAR(500);
		
		-- 游标变量
		DECLARE done INT DEFAULT FALSE;
		DECLARE his_code_cursor CURSOR FOR 
				SELECT DISTINCT his_code FROM rms_t_pres_med WHERE code = p_code;
		DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
		
		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
				
				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;
		
		-- 清理临时表
		DROP TEMPORARY TABLE IF EXISTS temp_pres_fx_ls;
		DROP TEMPORARY TABLE IF EXISTS temp_icd10_codes;
		
		-- 检查是否有中药
		SELECT COUNT(1) INTO v_n_count6 
		FROM rms_t_pres_med a
		INNER JOIN rms_ITF_HOS_DRUG b ON b.DRUG_CODE = a.his_code
		WHERE a.Code = p_code AND b.ZX_FLAG = '3';
		
		IF v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 获取住院标志和诊断信息
		SELECT hosp_flag, rms_get_zdxx_cj(dia_info) INTO v_hosp_flag, v_zdxx 
		FROM rms_t_pres 
		WHERE code = p_code LIMIT 1;
		
		-- 住院患者跳过
		IF v_hosp_flag = 'ip' THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否所有药品都在适应症白名单中
		SELECT COUNT(DISTINCT his_code) INTO v_n_count13 
		FROM rms_t_pres_med 
		WHERE code = p_code;

		SELECT COUNT(DISTINCT a.his_code) INTO v_n_count12 
		FROM rms_t_pres_med a, rms_t_byyydzb b, rms_t_sda_nosyz_lk c  
		WHERE a.code = p_code 
				AND a.his_code = b.yp_code 
				AND b.sda_id = c.sda_id;
		
		-- 如果所有药品都在白名单中，则跳过
		IF v_n_count13 = v_n_count12 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查感染诊断与抗菌药物的匹配
		SELECT COUNT(1) INTO v_n_count133 
		FROM rms_t_pres 
		WHERE code = p_code 
				AND (dia_info LIKE '%炎%' OR dia_info LIKE '%感染%');
		
		SELECT COUNT(1) INTO v_n_count144 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE IN (
				SELECT his_code FROM rms_t_pres_med WHERE code = p_code
		) AND IS_ANTIBAC = '1';
		
		-- 如果有感染诊断且有抗菌药物，则跳过
		IF v_n_count133 > 0 AND v_n_count144 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 清理之前的适应症分析结果
		DELETE FROM rms_t_pres_fx WHERE code = p_code AND wtcode = 'RLT037';
		
		-- 检查是否有诊断信息
		IF p_icd10_codes IS NULL OR p_icd10_codes = '' THEN
				INSERT INTO rms_t_pres_fx (
							Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT 
						p_code,
						'' AS ywa,
						'' AS ywb,
						'1' AS wtlvlcode,
						(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = '2') AS wtlvl,
						'RLT037' AS wtcode,
						'ZDXGYWSY' AS wtsp,
						'诊断与药物适应症' AS wtname, 
						'无适应症用药' AS title, 
						'无诊断或无适应症用药' AS detail,
						0,
						'适应症';
				LEAVE main_block;
		END IF;
		
		-- 如果有诊断信息，进行适应症分析
		IF p_icd10_codes IS NOT NULL AND p_icd10_codes != '' THEN
				-- 计算诊断代码的数量
				SET v_n_count = CHAR_LENGTH(p_icd10_codes) - CHAR_LENGTH(REPLACE(p_icd10_codes, ';', '')) + 1;
				
				-- 创建临时表存储解析的诊断代码
				DROP TEMPORARY TABLE IF EXISTS temp_icd10_codes;
				CREATE TEMPORARY TABLE temp_icd10_codes (
						icd10_code VARCHAR(50),   
						diagnoses VARCHAR(50)
				);
				
				-- 解析诊断代码字符串
				SET v_icd10_codes_work = p_icd10_codes;
				SET v_n_count1 = 0;
				
				WHILE v_n_count1 < v_n_count DO
						IF v_icd10_codes_work NOT LIKE '%;%' THEN
								SET v_icd10_code = v_icd10_codes_work;
								
								SELECT COUNT(1) INTO v_n_count9 
								FROM rms_t_icd10_base 
								WHERE icd_code = v_icd10_code;
								
								IF v_n_count9 > 0 THEN
										INSERT INTO temp_icd10_codes 
										SELECT icd_code, code 
										FROM rms_t_icd10_base 
										WHERE icd_code = v_icd10_code;
								ELSE
										INSERT INTO temp_icd10_codes 
										SELECT icd_code, code 
										FROM rms_t_icd10_base 
										WHERE icd_code = SUBSTRING(v_icd10_code, 1, 3);
								END IF;
								
								SET v_n_count1 = v_n_count1 + 1;
						ELSE
								SET v_icd10_code = SUBSTRING_INDEX(v_icd10_codes_work, ';', 1);
								SET v_icd10_codes_work = SUBSTRING(v_icd10_codes_work, LOCATE(';', v_icd10_codes_work) + 1);
								
								SET v_icd10_code = SUBSTRING_INDEX(v_icd10_codes_work, ';', 1);
								SET v_icd10_codes_work = SUBSTRING(v_icd10_codes_work, LOCATE(';', v_icd10_codes_work) + 1, CHAR_LENGTH(v_icd10_codes_work) - LOCATE(';', v_icd10_codes_work));
								
								SELECT COUNT(1) INTO v_n_count9 
								FROM rms_t_icd10_base 
								WHERE icd_code = v_icd10_code;
								
								IF v_n_count9 > 0 THEN
										INSERT INTO temp_icd10_codes 
										SELECT icd_code, code 
										FROM rms_t_icd10_base 
										WHERE icd_code = v_icd10_code;
								ELSE
										INSERT INTO temp_icd10_codes 
										SELECT icd_code, code 
										FROM rms_t_icd10_base 
										WHERE icd_code = SUBSTRING(v_icd10_code, 1, 3);
								END IF;
								
								SET v_n_count1 = v_n_count1 + 1;
						END IF;
				END WHILE;
				
				SELECT COUNT(1) INTO v_n_count4 FROM temp_icd10_codes;
				
				IF v_n_count4 > 0 THEN
						-- 创建临时表存储分析结果
						DROP TEMPORARY TABLE IF EXISTS temp_pres_fx_ls;
						CREATE TEMPORARY TABLE temp_pres_fx_ls (
								Code VARCHAR(50),
								ywa VARCHAR(50),
								ywb VARCHAR(50),
								wtlvlcode INT,
								wtlvl VARCHAR(5),
								wtcode VARCHAR(8),
								wtsp VARCHAR(30),
								wtname VARCHAR(30),
								title VARCHAR(500),
								detail VARCHAR(1000),
								flag VARCHAR(2),
								text VARCHAR(20)
						);
						
						-- 打开游标遍历每个药品
						SET done = FALSE;
						OPEN his_code_cursor;
						
						read_loop: LOOP
								FETCH his_code_cursor INTO v_his_code;
								IF done THEN
										LEAVE read_loop;
								END IF;
								
								SELECT COUNT(1) INTO v_n_count8 
								FROM rms_t_byyydzb a
								WHERE yp_code = v_his_code;
								
								SELECT med_name INTO v_med_name 
								FROM rms_t_pres_med 
								WHERE code = p_code AND his_code = v_his_code 
								LIMIT 1;
								
								IF v_n_count8 > 0 THEN
										-- 检查禁忌症
										SELECT DISTINCT 
												a.sda_id, e.med_name, c.icd_name, b.remark 
										INTO v_sda_id, v_ywa_name, v_icd10_name, v_remark
										FROM rms_t_pres_med e, rms_t_byyydzb a, 
												rms_t_sda_icd10_info b, rms_t_icd10_base c, temp_icd10_codes d
										WHERE e.Code = p_code
												AND a.yp_code = e.his_code
												AND a.sda_id = b.sda_id
												AND b.diagnoses = c.code
												AND d.icd10_code = c.icd_code
												AND b.bs = '1'
										LIMIT 1;
										
										IF v_sda_id IS NOT NULL AND v_sda_id != '' THEN
												INSERT INTO temp_pres_fx_ls
												SELECT 
														p_code,
														v_ywa_name AS ywa,
														'' AS ywb,
														'1' AS wtlvlcode,
														(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = '0') AS wtlvl,
														'RLT012' AS wtcode,
														'ZDXGYWJJ' AS wtsp,
														'药物禁忌症' AS wtname, 
														'药物禁忌症' AS title, 
														CONCAT('【', CAST(a.tymc AS CHAR), '】的禁忌症为：', v_icd10_name, ';', 
																IFNULL(v_remark, ''), '； 当诊断为(', v_icd10_name, ')时禁用【', 
																CAST(a.tymc AS CHAR), '】！') AS detail,
														0,
														'适应症' 
												FROM rms_t_sda a
												WHERE a.ID = v_sda_id;
										END IF;
										
										IF v_sda_id IS NULL OR v_sda_id = '' THEN
												-- 检查适应症匹配
												SELECT COUNT(1) INTO v_n_count2 
												FROM rms_t_byyydzb a, rms_t_sda_icd10_info b, rms_t_icd10_base c, temp_icd10_codes d
												WHERE a.yp_code = v_his_code
														AND a.sda_id = b.sda_id
														AND b.diagnoses = c.code
														AND SUBSTRING(d.icd10_code, 1, 3) = SUBSTRING(c.icd_code, 1, 3)
														AND b.bs = '5';
												
												SELECT COUNT(1) INTO v_n_count7 
												FROM rms_t_byyydzb a, rms_t_sda_icd10_info b, rms_t_icd10_base c 
												WHERE a.yp_code = v_his_code
														AND a.sda_id = b.sda_id
														AND b.diagnoses = c.code
														AND b.bs = '5';
												
												SELECT COUNT(1) INTO v_n_count18 
												FROM rms_t_byyydzb a, rms_t_sda_nosyz_lk b
												WHERE a.sda_id = b.SDA_ID
														AND a.yp_code = v_his_code;
												
												IF (v_n_count2 = 0 AND v_n_count7 > 0 AND v_n_count18 = 0) THEN
														INSERT INTO temp_pres_fx_ls
														SELECT 
																p_code,
																v_med_name AS ywa,
																'' AS ywb,
																'1' AS wtlvlcode,
																(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = '1') AS wtlvl,
																'RLT037' AS wtcode,
																'ZDXGYWSY' AS wtsp,
																'诊断与药物适应症' AS wtname, 
																'诊断与用药不适宜' AS title, 
																CONCAT(v_med_name, '的适应症与患者诊断(', v_zdxx, ')不相关，可能存在超说明书用药或诊断不全。') AS detail,
																0,
																'适应症';
												END IF;
										END IF;
										
										-- 重置变量
										SET v_sda_id = NULL;
								END IF;
								
						END LOOP;
						
						CLOSE his_code_cursor;
						
						-- 插入分析结果
						INSERT INTO rms_t_pres_fx (
								Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
						)
						SELECT * FROM temp_pres_fx_ls;
						
						-- 清理临时表
						DROP TEMPORARY TABLE temp_pres_fx_ls;
				END IF;
				
				-- 清理临时表
				DROP TEMPORARY TABLE temp_icd10_codes;
		END IF;

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_yfyy
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_yfyy`;
delimiter ;;
CREATE PROCEDURE `rms_fx_yfyy`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_pregnant_unit VARCHAR(10),
    IN p_pregnant VARCHAR(10))
  COMMENT '孕妇用药分析存储过程'
main_block: BEGIN
    DECLARE v_sda_id VARCHAR(20);
    DECLARE v_ywa_name VARCHAR(50);
    DECLARE v_yz INT DEFAULT 0;
    
    -- 异常处理
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 如果出现异常，继续执行
        GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
    END;
    
    -- 获取药品名称
    SELECT DRUG_NAME INTO v_ywa_name
    FROM rms_itf_hos_drug 
    WHERE DRUG_CODE = p_yp_code
    LIMIT 1;
    
    -- 获取标准数据ID
    SELECT sda_id INTO v_sda_id
    FROM rms_t_byyydzb  
    WHERE akb020 = p_akb020 AND yp_code = p_yp_code
    LIMIT 1;
		
		IF p_pregnant_unit = '' or p_pregnant_unit is null or p_pregnant = '' or p_pregnant is null THEN
				LEAVE main_block;
		END IF;
    
    -- 计算孕周天数
    SET v_yz = rms_fn_get_yz(p_pregnant_unit, p_pregnant);
		
    -- 如果有孕周信息，按孕周范围查询
    IF v_yz > 0 THEN
        INSERT INTO rms_t_pres_fx (
            Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
        )
        SELECT 
            p_Code,
            v_ywa_name AS ywa,
            '' AS ywb,
            '1' AS wtlvlcode,
            (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.bs) AS wtlvl,
            CASE 
                WHEN a.bs = '0' THEN 'RLT006'
                WHEN a.bs = '1' THEN 'RLT017'
            END AS wtcode,
            CASE 
                WHEN a.bs = '0' THEN 'RSQFNJJ'
                WHEN a.bs = '1' THEN 'RSQFNWT'
            END AS wtsp,
            CASE 
                WHEN a.bs = '0' THEN '特殊人群禁用'
                WHEN a.bs = '1' THEN '特殊人群慎用'
            END AS wtname,
            '妊娠用药' AS title,
            CONCAT('说明书提示：【', CAST(c.ym AS CHAR), '】', CAST(c.yfyy AS CHAR(500))) AS detail,
            0,
            '孕妇用药'
        FROM rms_t_sda_gestation a, rms_t_byyydzb b, rms_t_sda c
        WHERE a.sda_id = b.sda_id
        AND b.sda_id = c.ID
        AND b.akb020 = p_akb020
        AND b.yp_code = p_yp_code
        AND a.dayup <= v_yz
        AND a.daydown >= v_yz;
    ELSE
        -- 如果没有孕周信息，查询默认范围
        INSERT INTO rms_t_pres_fx (
            Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
        )
        SELECT 
            p_Code,
            v_ywa_name AS ywa,
            '' AS ywb,
            '1' AS wtlvlcode,
            (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.bs) AS wtlvl,
            CASE 
                WHEN a.bs = '0' THEN 'RLT006'
                WHEN a.bs = '1' THEN 'RLT017'
            END AS wtcode,
            CASE 
                WHEN a.bs = '0' THEN 'RSQFNJJ'
                WHEN a.bs = '1' THEN 'RSQFNWT'
            END AS wtsp,
            CASE 
                WHEN a.bs = '0' THEN '特殊人群禁用'
                WHEN a.bs = '1' THEN '特殊人群慎用'
            END AS wtname,
            '妊娠用药' AS title,
            CONCAT('说明书提示：', CAST(c.ym AS CHAR), CAST(c.yfyy AS CHAR(500))) AS detail,
            0,
            '孕妇用药'
        FROM rms_t_sda_gestation a, rms_t_byyydzb b, rms_t_sda c
        WHERE a.sda_id = b.sda_id
        AND b.sda_id = c.ID
        AND b.akb020 = p_akb020
        AND b.yp_code = p_yp_code
        AND a.dayup <= -100
        AND a.daydown >= 1000;
    END IF;
    
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_yuliu
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_yuliu`;
delimiter ;;
CREATE PROCEDURE `rms_fx_yuliu`(IN p_code VARCHAR(50),
    IN p_gm_code_str VARCHAR(200))
  READS SQL DATA 
  DETERMINISTIC
  COMMENT '处方余留综合检查存储过程'
BEGIN
    main_block: BEGIN
        -- 声明变量
        DECLARE v_hosp_flag VARCHAR(20);
        DECLARE v_prescription_type VARCHAR(20);
        DECLARE v_n_count4 INT;
        DECLARE v_xb VARCHAR(4);
        DECLARE v_n_count6 INT;
        DECLARE v_nl VARCHAR(20);
        DECLARE v_csrq VARCHAR(20);
        DECLARE v_n_count7 INT;
        DECLARE v_n_count8 INT;
        DECLARE v_n_count9 INT;
        DECLARE v_n_count10 INT;
        DECLARE v_cid VARCHAR(20);
        DECLARE v_med_name1 VARCHAR(50);
        DECLARE v_med_name2 VARCHAR(50);
        DECLARE v_n_count_gm INT;
        DECLARE v_n_count_gm1 INT DEFAULT 0;
        DECLARE v_gm_code VARCHAR(20);
        DECLARE v_gm_code_str_work VARCHAR(200);
        
        -- 游标变量
        DECLARE v_sda_id VARCHAR(10);
        DECLARE v_code_rc VARCHAR(50);
        DECLARE v_his_code_rc VARCHAR(50);
        DECLARE v_csrq_rc VARCHAR(50);
        DECLARE v_yp_tj_rc VARCHAR(50);
        DECLARE v_pregnant VARCHAR(10);
        DECLARE v_pregnant_unit VARCHAR(10);
        DECLARE done INT DEFAULT FALSE;
        
        DECLARE cursor_drug_info CURSOR FOR
            SELECT IFNULL(b.sda_id, ''), a.code, a.his_code, d.birth, a.administer, 
                IFNULL(d.pregnant, ''), IFNULL(d.pregnant_unit, '')
            FROM rms_t_pres d 
            INNER JOIN rms_t_pres_med a ON d.code = a.code
            INNER JOIN rms_itf_hos_drug c ON a.his_code = c.DRUG_CODE
            LEFT JOIN rms_t_byyydzb b ON a.his_code = b.yp_code
            WHERE d.Code = p_code
            AND IFNULL(c.is_rm, '') <> '1';
        
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
        
        -- 声明异常处理
        DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
        BEGIN
            -- 如果出现异常，继续执行
            BEGIN END;
        END;

        -- 获取处方基本信息
        SELECT hosp_flag, prescription_type INTO v_hosp_flag, v_prescription_type
        FROM rms_t_pres 
        WHERE code = p_code LIMIT 1;
        
        -- 如果是中药处方则返回
        IF v_prescription_type = '2' THEN
            LEAVE main_block;
        END IF;

        -- 妇科男性患者检查
        SELECT COUNT(1) INTO v_n_count4 
        FROM rms_itf_hos_spec a, rms_t_pres b 
        WHERE a.SPEC_CODE = b.dept_code AND b.code = p_code  
        AND a.SPEC_NAME LIKE '%妇科%';
        
        SELECT sex INTO v_xb 
        FROM rms_t_pres  
        WHERE code = p_code LIMIT 1;
        
        IF v_n_count4 > 0 AND v_xb = '男' THEN
            INSERT INTO rms_t_pres_fx 
            VALUES (p_code, '', '', '2', '严重警示', 'RLT050', 'yyxb', '用药性别', '用药性别问题', 
                    '妇科不能接诊男性患者！', '0', '用药性别');
        END IF;

        -- 儿科患者年龄检查（不能超过16周岁）
        SELECT COUNT(1) INTO v_n_count6 
        FROM rms_itf_hos_spec a, rms_t_pres b 
        WHERE a.SPEC_CODE = b.dept_code AND b.code = p_code  
        AND a.SPEC_NAME LIKE '%儿科%';
        
        SELECT birth INTO v_csrq 
        FROM rms_t_pres  
        WHERE code = p_code LIMIT 1;
        
        SET v_nl = CAST(DATEDIFF(CURDATE(), STR_TO_DATE(v_csrq, '%Y%m%d')) / 365.0 AS DECIMAL(18,3));
        
        IF v_n_count6 > 0 AND v_nl > 16 THEN
            INSERT INTO rms_t_pres_fx 
            VALUES (p_code, '', '', '2', '严重警示', 'RLT051', 'nlks', '年龄科室问题', '年龄科室问题', 
                    '儿科不能接诊成人患者！', '0', '用药科室');
        END IF;

        -- 男患者使用妇科诊断检查
        IF v_xb = '男' THEN
            SELECT COUNT(1) INTO v_n_count7 
            FROM rms_t_pres a, rms_t_icd10_base b
            WHERE a.code = p_code
            AND b.remark = '2'
            AND LOCATE(b.icd_name, a.dia_info) > 0;
            
            IF v_n_count7 > 0 THEN
                INSERT INTO rms_t_pres_fx 
                VALUES (p_code, '', '', '1', '严重警示', 'RLT050', 'yyxb', '用药性别', '用药性别问题', 
                        '妇科诊断不能用于男性患者！', '0', '用药性别');
            END IF;
        END IF;

        -- 女患者使用男性诊断检查
        IF v_xb = '女' THEN
            SELECT COUNT(1) INTO v_n_count8 
            FROM rms_t_pres a, rms_t_icd10_base b
            WHERE a.code = p_code
            AND b.remark = '1'
            AND LOCATE(b.icd_name, a.dia_info) > 0;
            
            IF v_n_count8 > 0 THEN
                INSERT INTO rms_t_pres_fx 
                VALUES (p_code, '', '', '1', '严重警示', 'RLT050', 'yyxb', '用药性别', '用药性别问题', 
                        '男性诊断不能用于女性患者！', '0', '用药性别');
            END IF;
        END IF;

        -- 男患者使用妇科用药检查
        IF v_xb = '男' THEN
            SELECT COUNT(1) INTO v_n_count9 
            FROM rms_t_pres a
            INNER JOIN rms_t_pres_med b ON a.code = b.code
            INNER JOIN rms_t_byyydzb d ON b.his_code = d.yp_code
            INNER JOIN rms_t_sda_sex c ON d.sda_id = c.sda_id
            WHERE a.code = p_code
            AND c.sex = '2'
            AND IFNULL(c.ispb, '0') <> '1';
            
            IF v_n_count9 > 0 THEN
                INSERT INTO rms_t_pres_fx 
                VALUES (p_code, '', '', '1', '严重警示', 'RLT050', 'yyxb', '用药性别', '用药性别问题', 
                        '妇科用药不能用于男性患者！', '0', '用药性别');
            END IF;
        END IF;

        -- 女患者使用男性用药检查
        IF v_xb = '女' THEN
            SELECT COUNT(1) INTO v_n_count10 
            FROM rms_t_pres a
            INNER JOIN rms_t_pres_med b ON a.code = b.code
            INNER JOIN rms_t_byyydzb d ON b.his_code = d.yp_code
            INNER JOIN rms_t_sda_sex c ON d.sda_id = c.sda_id
            WHERE a.code = p_code
            AND c.sex = '1'
            AND IFNULL(c.ispb, '0') <> '1';
            
            IF v_n_count10 > 0 THEN
                INSERT INTO rms_t_pres_fx 
                VALUES (p_code, '', '', '1', '严重警示', 'RLT050', 'yyxb', '用药性别', '用药性别问题', 
                        '男性用药不能用于女性患者！', '0', '用药性别');
            END IF;
        END IF;

        -- 重复用药检查
        SELECT b.CID INTO v_cid
        FROM rms_t_pres_med a
        INNER JOIN rms_t_byyydzb b ON a.his_code = b.yp_code
        INNER JOIN rms_itf_hos_drug c ON a.his_code = c.DRUG_CODE
        WHERE a.Code = p_code
        AND IFNULL(b.CID, '') <> ''
        AND IFNULL(c.is_rm, '') <> '1'
        GROUP BY b.CID
        HAVING COUNT(1) > 1
        LIMIT 1;

        IF v_cid IS NOT NULL AND v_cid <> '' THEN
            SELECT med_name INTO v_med_name1 
            FROM rms_t_pres_med a
            INNER JOIN rms_t_byyydzb b ON a.his_code = b.yp_code
            WHERE a.Code = p_code AND b.CID = v_cid
            ORDER BY a.his_code
            LIMIT 1;

            SELECT med_name INTO v_med_name2 
            FROM rms_t_pres_med a
            INNER JOIN rms_t_byyydzb b ON a.his_code = b.yp_code
            WHERE a.Code = p_code AND b.CID = v_cid
            ORDER BY a.his_code DESC
            LIMIT 1;

            IF v_med_name1 <> v_med_name2 THEN
                INSERT INTO rms_t_pres_fx 
                VALUES (p_code, v_med_name1, v_med_name2, '1', '一般提示', 'RLT025', 'CFYYTS',
                        '重复用药', '重复用药', CONCAT(v_med_name1, '和 ', v_med_name2, '属于同种药品'), '0', '重复用药');
            END IF;
        END IF;

        -- 分解过敏代码
--         IF p_gm_code_str IS NOT NULL AND p_gm_code_str <> '' THEN
--             -- 清理之前的过敏记录
--             DELETE FROM rms_t_pres_gm WHERE code = p_code;
--             
--             SET v_n_count_gm = CHAR_LENGTH(p_gm_code_str) - CHAR_LENGTH(REPLACE(p_gm_code_str, ';', '')) + 1;
--             SET v_gm_code_str_work = p_gm_code_str;
--             SET v_n_count_gm1 = 0;
--             
--             WHILE v_n_count_gm1 < v_n_count_gm DO
--                 IF v_gm_code_str_work NOT LIKE '%;%' THEN
--                     SET v_gm_code = v_gm_code_str_work;
--                     IF v_gm_code LIKE 'A%' THEN
--                         SET v_gm_code = SUBSTRING(v_gm_code, 2, LENGTH(v_gm_code));
--                     END IF;
--                     INSERT INTO rms_t_pres_gm VALUES (p_code, v_gm_code);
--                     SET v_n_count_gm1 = v_n_count_gm1 + 1;
--                 ELSE
--                     SET v_gm_code = SUBSTRING_INDEX(v_gm_code_str_work, ';', 1);
--                     SET v_gm_code_str_work = SUBSTRING(v_gm_code_str_work, LOCATE(';', v_gm_code_str_work) + 1);
--                     IF v_gm_code LIKE 'A%' THEN
--                         SET v_gm_code = SUBSTRING(v_gm_code, 2, LENGTH(v_gm_code));
--                     END IF;
--                     INSERT INTO rms_t_pres_gm VALUES (p_code, v_gm_code);
--                     SET v_n_count_gm1 = v_n_count_gm1 + 1;
--                 END IF;
--             END WHILE;
--         END IF;

        -- 配伍禁忌调用
        CALL rms_fx_pwjj(p_code);

        -- 循环调用各种分析  
        OPEN cursor_drug_info;
        
        read_loop: LOOP
            FETCH cursor_drug_info INTO v_sda_id, v_code_rc, v_his_code_rc, v_csrq_rc, 
                                    v_yp_tj_rc, v_pregnant, v_pregnant_unit;
            IF done THEN
                LEAVE read_loop;
            END IF;
            
            IF v_sda_id = '' OR v_sda_id IS NULL THEN
                ITERATE read_loop;
            END IF;
            
            IF v_prescription_type = '2' THEN
                CALL rms_fx_gytj(v_code_rc, '', v_his_code_rc, v_yp_tj_rc);
            END IF;
            
            IF v_prescription_type = '1' THEN
                CALL rms_fx_lnr(v_code_rc, '', v_his_code_rc, v_csrq_rc);
                CALL rms_fx_gytj(v_code_rc, '', v_his_code_rc, v_yp_tj_rc);
                CALL rms_fx_etyy(v_code_rc, '', v_his_code_rc, v_csrq_rc);
                CALL rms_fx_yfyy(v_code_rc, '', v_his_code_rc, v_pregnant_unit, v_pregnant);
                CALL rms_fx_gm(v_code_rc, '', v_his_code_rc);
            END IF;
            
        END LOOP;
        
        CLOSE cursor_drug_info;

    END main_block;

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for rms_fx_yuliu_med
-- ----------------------------
DROP PROCEDURE IF EXISTS `rms_fx_yuliu_med`;
delimiter ;;
CREATE PROCEDURE `rms_fx_yuliu_med`(IN p_code VARCHAR(50),
    IN p_yp_code VARCHAR(60))
  READS SQL DATA 
  DETERMINISTIC
  COMMENT '药品余留/用药总量分析存储过程'
BEGIN
    main_block: BEGIN
        -- 声明变量
        DECLARE v_sda_id VARCHAR(20);
        DECLARE v_yp_name VARCHAR(200);
        DECLARE v_yp_name2 VARCHAR(200);
        DECLARE v_yp_yyzl VARCHAR(50);    -- 用户自定义的用药总量最大值
        DECLARE v_ord_qty VARCHAR(50);    -- his传来的用药总量值
        DECLARE v_ord_uom VARCHAR(50);
        DECLARE v_n_count2 INT;
        DECLARE v_n_count11 INT;
        DECLARE v_ks VARCHAR(50);
        DECLARE v_hosp_flag VARCHAR(50);
        DECLARE v_value DECIMAL(10,2);
        DECLARE v_len_value INT;
        DECLARE v_pres_type2 VARCHAR(50);
        DECLARE v_n_count6 INT;
        DECLARE v_mid VARCHAR(50);
        DECLARE v_ksdm VARCHAR(20);
        DECLARE v_n_count INT;
        DECLARE v_n_count1 INT;
        DECLARE v_ysdm VARCHAR(20);
        DECLARE v_n_count5 INT;
        DECLARE v_n_count3 INT;
        DECLARE v_n_count36 INT;
        DECLARE v_n_count9 INT;
        DECLARE v_n_count10 INT;
        DECLARE v_n_count12 INT;
        DECLARE v_n_count13 INT;
        DECLARE v_n_count18 INT;
        DECLARE v_n_count19 INT;
        DECLARE v_tymc VARCHAR(50);
        DECLARE v_n_count20 INT;
        DECLARE v_n_count21 INT;
        DECLARE v_n_count22 INT;
        DECLARE v_n_count23 INT;
        DECLARE v_DAILY_TIMES INT;
        DECLARE v_yl_min INT;
        DECLARE v_ts_int INT;
        DECLARE v_zdts DECIMAL(14,2);
        DECLARE v_sj_zdts DECIMAL(14,2);
        DECLARE v_sdate VARCHAR(50);
        DECLARE v_his_time VARCHAR(50);
        DECLARE v_his_time2 VARCHAR(50);
        DECLARE v_last_his_time VARCHAR(50);
        DECLARE v_last_code VARCHAR(50);
        DECLARE v_dept_name VARCHAR(50);
        DECLARE v_doct_name VARCHAR(50);
        DECLARE v_n_count24 INT;
        DECLARE v_card_code VARCHAR(50);
        DECLARE v_pres_id VARCHAR(50);
        
        -- 声明异常处理
        DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
        BEGIN
            -- 如果出现异常，直接返回
            ROLLBACK;
        END;

        -- 检查药品是否为停用状态
        SELECT COUNT(1) INTO v_n_count6 
        FROM rms_itf_hos_drug b 
        WHERE b.DRUG_CODE = p_yp_code AND b.ZX_FLAG = '3';
        
        IF v_n_count6 > 0 THEN
            LEAVE main_block;
        END IF;

        -- 获取药品基本信息
        SELECT sda_id, cid INTO v_sda_id, v_mid 
        FROM rms_t_byyydzb 
        WHERE yp_code = p_yp_code LIMIT 1;

        -- 获取处方药品信息
        SELECT med_name, ord_qty, ord_uom INTO v_yp_name, v_ord_qty, v_ord_uom
        FROM rms_t_pres_med 
        WHERE code = p_code AND his_code = p_yp_code LIMIT 1;

        -- 获取药品最大发药量
        SELECT zdfyl INTO v_yp_yyzl
        FROM rms_t_drug_zdfyl  
        WHERE drug_code = p_yp_code AND unit = v_ord_uom LIMIT 1;

        -- 获取处方类型信息
        SELECT hosp_flag, pres_type INTO v_hosp_flag, v_pres_type2
        FROM rms_t_pres  
        WHERE Code = p_code LIMIT 1;

        -- 门诊用药总量检查
        IF v_hosp_flag = 'op' AND (v_yp_yyzl != '' AND v_yp_yyzl IS NOT NULL) THEN
            SET v_value = CAST(v_yp_yyzl AS DECIMAL(10,2)) - CAST(v_ord_qty AS DECIMAL(10,2));
            IF v_value < 0 THEN
                INSERT INTO rms_t_pres_fx 
                SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT047', 'yyzlwt', '用药总量', '用药总量问题', 
                    CONCAT(v_yp_name, '超出最大发药量,医院要求门诊发药最大量为', v_yp_yyzl, v_ord_uom), '0', '用药总量';
            END IF;
        END IF;

        -- 外用软膏最大发药量检查（不能超过5支）
        SELECT COUNT(1) INTO v_n_count36 
        FROM rms_itf_hos_drug b 
        WHERE b.DRUG_CODE = p_yp_code 
        AND b.DRUG_FOR_NAME IN ('软膏剂', '乳膏剂', '凝胶(胶浆)剂', '眼膏剂', '茶剂', '滴眼剂');
        
        IF v_hosp_flag = 'op' AND v_n_count36 > 0 AND CAST(v_ord_qty AS DECIMAL(10,2)) > 5 THEN
            INSERT INTO rms_t_pres_fx 
            SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT047', 'yyzlwt', '用药总量', '用药总量问题', 
                CONCAT(v_yp_name, '超出最大发药量,医院要求门诊软膏、滴眼剂发药最大量为5支'), '0', '用药总量';
        END IF;

        -- 药品自定义科室检查
        SELECT COUNT(1) INTO v_n_count 
        FROM rms_t_med_zdy_dept  
        WHERE yp_code = p_yp_code;
        
        IF v_n_count > 0 THEN
            SELECT dept_code INTO v_ksdm 
            FROM rms_t_pres 
            WHERE code = p_code LIMIT 1;

            SELECT COUNT(1) INTO v_n_count1 
            FROM rms_t_med_zdy_dept     
            WHERE yp_code = p_yp_code AND dept_code = v_ksdm;

            IF v_n_count1 = 0 THEN
                INSERT INTO rms_t_pres_fx 
                SELECT p_code, v_yp_name, '', '1', '严重警示', 'RLT048', 'yyks', '用药科室', '用药科室问题', 
                    CONCAT('医院规定：', v_yp_name, '不能在该科室使用！'), '0', '用药科室';
            END IF;
        END IF;

        -- 药品自定义医生检查
        SELECT COUNT(1) INTO v_n_count5 
        FROM rms_t_med_zdy_doct  
        WHERE yp_code = p_yp_code;
        
        IF v_n_count5 > 0 THEN
            SELECT doct_code INTO v_ysdm 
            FROM rms_t_pres 
            WHERE code = p_code LIMIT 1;

            SELECT COUNT(1) INTO v_n_count3 
            FROM rms_t_med_zdy_doct     
            WHERE yp_code = p_yp_code AND doct_code = v_ysdm;

            IF v_n_count3 = 0 THEN
                INSERT INTO rms_t_pres_fx 
                SELECT p_code, v_yp_name, '', '2', '一般提示', 'RLT049', 'yyys', '用药医生', '用药医生问题', 
                    CONCAT(v_yp_name, '不能该医生使用！'), '0', '用药科室';
            END IF;
        END IF;

        -- 给药途径检查（取药用、遵医嘱、自用拦截）
        SELECT COUNT(1) INTO v_n_count9 
        FROM rms_t_pres_med 
        WHERE code = p_code 
        AND administer IN (
            SELECT ADM_CODE 
            FROM rms_itf_hos_admin_route 
            WHERE jzbs = '1'
        );
        
        IF v_n_count9 > 0 THEN 
            INSERT INTO rms_t_pres_fx 
            SELECT p_code, v_yp_name, '', '2', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', '给药途径错误', 
                '所有药品不能使用【取药用、遵医嘱、自用】给药途径！', '0', '给药途径错误';
        END IF;

        -- 自定义给药途径分析
        SELECT COUNT(1) INTO v_n_count12 
        FROM rms_t_med_zdy_gytj 
        WHERE yp_code = p_yp_code;
        
        SELECT COUNT(1) INTO v_n_count13  
        FROM rms_t_pres_med 
        WHERE code = p_code AND his_code = p_yp_code 
        AND administer NOT IN (
            SELECT gytj_code 
            FROM rms_t_med_zdy_gytj 
            WHERE yp_code = p_yp_code
        );
        
        IF v_n_count12 > 0 AND v_n_count13 > 0 THEN
            INSERT INTO rms_t_pres_fx 
            SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT026', 'GYTJWT', '给药途径错误', '给药途径问题', 
                CONCAT(v_yp_name, '不符合医院规定给药途径！'), '0', '给药途径';
        END IF;

        -- 住院临时医嘱频次分析
        SELECT COUNT(1) INTO v_n_count18 
        FROM rms_t_pres_med a, rms_t_pres b 
        WHERE a.code = p_code AND a.Code = b.code  
        AND freq IN ('01','13','15','14') AND his_code = p_yp_code;
        
        SELECT DRUG_NAME INTO v_tymc 
        FROM rms_itf_hos_drug 
        WHERE DRUG_CODE = p_yp_code LIMIT 1;

        IF v_hosp_flag = 'ip' AND v_pres_type2 = 'T' AND v_n_count18 >= 1 THEN
            DELETE FROM rms_t_pres_fx 
            WHERE Code = p_code AND wtcode = 'RLT030' 
            AND title LIKE '%频次%' AND ywa = v_tymc;
            
            SELECT COUNT(1) INTO v_n_count19 
            FROM rms_t_pres_fx a, rms_t_pres b 
            WHERE a.Code = b.code AND a.code = p_code;
            
            IF v_n_count19 = 0 THEN  
                UPDATE rms_t_pres SET flag = 0 WHERE code = p_code;
            END IF;
        END IF;

        -- 门诊注射剂"立即使用"频次分析
        SELECT COUNT(1) INTO v_n_count20 
        FROM rms_t_pres_med a  
        WHERE a.code = p_code AND freq IN ('01','13','15','14') 
        AND his_code = p_yp_code 
        AND (a.med_name LIKE '%针%' OR a.med_name LIKE '%注射%');

        IF v_hosp_flag = 'op' AND v_n_count20 >= 1 THEN
            DELETE FROM rms_t_pres_fx 
            WHERE Code = p_code AND wtcode = 'RLT030' 
            AND title LIKE '%频次%' AND ywa = v_tymc;
        END IF;

        -- 门诊抗菌药物注射剂允许低于正常频次
        SELECT COUNT(1) INTO v_n_count22 
        FROM rms_itf_hos_drug b 
        WHERE b.IS_ANTIBAC = '1' AND drug_code = p_yp_code 
        AND b.DRUG_PRODUCT_NAME LIKE '%注射%';

        SELECT b.DAILY_TIMES INTO v_DAILY_TIMES
        FROM rms_t_pres_med a, rms_itf_hos_frequency b
        WHERE code = p_code AND a.his_code = p_yp_code
        AND a.freq = b.FREQ_CODE LIMIT 1;

        SELECT MAX(yl_min) INTO v_yl_min 
        FROM rms_t_sda_cgl_result a, rms_t_byyydzb b
        WHERE a.sda_id = b.sda_id
        AND b.yp_code = p_yp_code
        AND a.reco_type = '2';

        IF v_hosp_flag = 'op' AND v_n_count22 >= 1 AND v_DAILY_TIMES < v_yl_min THEN
            DELETE FROM rms_t_pres_fx 
            WHERE Code = p_code AND wtcode = 'RLT030' 
            AND title LIKE '%频次%' AND ywa = v_tymc;
            
            SELECT COUNT(1) INTO v_n_count23 
            FROM rms_t_pres_fx a, rms_t_pres b 
            WHERE a.Code = b.code AND a.code = p_code;
            
            IF v_n_count21 = 0 THEN  
                UPDATE rms_t_pres SET flag = 0 WHERE code = p_code;
            END IF;
        END IF;

        -- 历史处方相互作用检查
        IF v_hosp_flag = 'ip' OR v_n_count6 > 0 THEN
            LEAVE main_block;
        END IF;

        SELECT card_code, pres_time, pres_id INTO v_card_code, v_his_time, v_pres_id
        FROM rms_t_pres 
        WHERE code = p_code LIMIT 1;
        
        SELECT COUNT(1) INTO v_n_count24
        FROM rms_t_drug_zdfyl 
        WHERE DRUG_CODE = p_yp_code;

        -- 创建临时表进行历史相互作用分析
        DROP TEMPORARY TABLE IF EXISTS temp_xhzy_ls;
        CREATE TEMPORARY TABLE temp_xhzy_ls (
            drug_name VARCHAR(50),
            dept_name VARCHAR(50),
            doct_name VARCHAR(50),
            mid VARCHAR(50)
        );

        INSERT INTO temp_xhzy_ls
        SELECT b.med_name, d.SPEC_NAME, a.doct_name, c.CID
        FROM rms_t_pres a, rms_t_pres_med b, rms_t_byyydzb c, rms_itf_hos_spec d
        WHERE a.code = b.Code 
        AND b.his_code = c.yp_code
        AND a.dept_code = d.SPEC_CODE
        AND pres_time > DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND hosp_flag = 'op'
        AND flag > -1
        AND flag <> 9
        AND a.prescription_type = '1'
        AND a.card_code = v_card_code
        AND a.pres_id <> v_pres_id;

        -- 插入相互作用警告
        INSERT INTO rms_t_pres_fx
        SELECT p_code, v_yp_name, b.drug_name, '1', '一般提示', 'RLT014', 'XHZYTS', '相互作用',
            CONCAT('【', v_yp_name, '】和【', CURDATE(), '】在', b.dept_name, '的【', b.doct_name, '】开的【', b.drug_name, '】存在相互作用'),
            CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), '; 机制：', IFNULL(CAST(mechanism AS CHAR(4000)), ''), ';', IFNULL(CONCAT('参考文献:', CAST(reference AS CHAR(4000))), '')),
            0, '配伍问题'
        FROM rms_t_xhzy_edi a, temp_xhzy_ls b
        WHERE yaowuA = v_mid AND yaowuB = b.mid
        AND sugflag = 'xhzy'
        UNION
        SELECT p_code, v_yp_name, b.drug_name, '1', '一般提示', 'RLT014', 'XHZYTS', '相互作用',
            CONCAT('【', v_yp_name, '】和【', CURDATE(), '】在', b.dept_name, '的【', b.doct_name, '】开的【', b.drug_name, '】存在相互作用'),
            CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), '; 机制：', IFNULL(CAST(mechanism AS CHAR(4000)), ''), ';', IFNULL(CONCAT('参考文献:', CAST(reference AS CHAR(4000))), '')),
            0, '配伍问题'
        FROM rms_t_xhzy_edi a, temp_xhzy_ls b
        WHERE yaowuA = b.mid AND yaowuB = v_mid
        AND sugflag = 'xhzy';

        DROP TEMPORARY TABLE temp_xhzy_ls;
    END main_block;

END
;;
delimiter ;

-- ----------------------------
-- Function structure for rms_get_gytj
-- ----------------------------
DROP FUNCTION IF EXISTS `rms_get_gytj`;
delimiter ;;
CREATE FUNCTION `rms_get_gytj`(sda_id_input VARCHAR(20))
 RETURNS varchar(500) CHARSET utf8mb4
  READS SQL DATA 
  DETERMINISTIC
BEGIN
    DECLARE gytj_result VARCHAR(500) DEFAULT '';
    
    SELECT GROUP_CONCAT(DISTINCT b.ms ORDER BY a.gytj_code SEPARATOR ';') INTO gytj_result
    FROM rms_t_sda_gytj a
    JOIN rms_t_tj_base b ON a.gytj_code = b.dm
    WHERE a.sda_id = sda_id_input AND a.bs = '0';
    
    RETURN IFNULL(gytj_result, '');
END
;;
delimiter ;

-- ----------------------------
-- Function structure for rms_get_gytj_by_code
-- ----------------------------
DROP FUNCTION IF EXISTS `rms_get_gytj_by_code`;
delimiter ;;
CREATE FUNCTION `rms_get_gytj_by_code`(code_input VARCHAR(50), yp_code_input VARCHAR(50))
 RETURNS varchar(20) CHARSET utf8mb4
  READS SQL DATA 
  DETERMINISTIC
BEGIN
    DECLARE gytj_result VARCHAR(20) DEFAULT '';
    
    SELECT administer INTO gytj_result
    FROM rms_t_pres_med 
    WHERE Code = code_input AND his_code = yp_code_input
    LIMIT 1;
    
    RETURN IFNULL(gytj_result, '');
END
;;
delimiter ;

-- ----------------------------
-- Function structure for rms_get_zdxx_cj
-- ----------------------------
DROP FUNCTION IF EXISTS `rms_get_zdxx_cj`;
delimiter ;;
CREATE FUNCTION `rms_get_zdxx_cj`(p_text VARCHAR(4000))
 RETURNS varchar(4000) CHARSET utf8mb4
  DETERMINISTIC
BEGIN
  DECLARE r VARCHAR(1000) DEFAULT '';
  DECLARE i VARCHAR(1000) DEFAULT '';
  DECLARE fenhao_index INT DEFAULT 0;
  DECLARE first_douhao_index INT DEFAULT 0;
  DECLARE last_douhao_index INT DEFAULT 0;
  DECLARE v_text VARCHAR(4000);

  SET v_text = p_text;

  -- 若无逗号且非空非空串，直接返回原文
  IF v_text IS NOT NULL AND v_text <> '' AND LOCATE(',', v_text) = 0 THEN
    RETURN v_text;
  END IF;

  -- 与原 MSSQL 行为一致：NULL 输入返回空串
  IF v_text IS NULL THEN
    RETURN r;
  END IF;

  loop_label: WHILE LOCATE(',', v_text) > 0 DO
    -- 取到首个分号之前的片段（等价于 T-SQL 的 SUBSTRING(@text, 0, CHARINDEX(';',@text)+1)）
    SET fenhao_index = LOCATE(';', v_text) + 1;
    SET i = LEFT(v_text, GREATEST(fenhao_index - 1, 0));

    IF i <> '' THEN
      SET first_douhao_index = LOCATE(',', i);
      SET last_douhao_index  = CHAR_LENGTH(i) - LOCATE(',', REVERSE(i)) + 1;

      -- 移除已处理片段
      SET v_text = REPLACE(v_text, i, '');

      -- 提取首个逗号与最后一个逗号之间的内容，长度计算保持与原逻辑一致
      IF last_douhao_index - 3 < 0 THEN
        SET r = CONCAT(r, SUBSTRING(i, first_douhao_index + 1, 0), '、');
      ELSE
        SET r = CONCAT(r, SUBSTRING(i, first_douhao_index + 1, last_douhao_index - 3), '、');
      END IF;
    ELSE
      -- 若本段为空，则直接把剩余文本并入结果并退出
      SET r = v_text;
      LEAVE loop_label;
    END IF;
  END WHILE loop_label;

  RETURN r;
END
;;
delimiter ;

-- ----------------------------
-- Function structure for rms_get_zongzhuanheng
-- ----------------------------
DROP FUNCTION IF EXISTS `rms_get_zongzhuanheng`;
delimiter ;;
CREATE FUNCTION `rms_get_zongzhuanheng`(yp_code NVARCHAR(50))
 RETURNS varchar(2000) CHARSET utf8mb4
  DETERMINISTIC
BEGIN 
    DECLARE values_str VARCHAR(2000);
    
    -- 使用GROUP_CONCAT拼接结果，并用|分隔
    SELECT GROUP_CONCAT(CONCAT('|', freq_name) SEPARATOR '')
    INTO values_str
    FROM rms_pc_zdy
    WHERE yp_code = yp_code;
    
    RETURN values_str;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for test
-- ----------------------------
DROP PROCEDURE IF EXISTS `test`;
delimiter ;;
CREATE PROCEDURE `test`(IN v1 int,OUT o1 int)
BEGIN
	SET o1 = v1 + 1;
  call rms_fx_gm('157da69914ec4dc5b6fd4e74249a38fa', '', '21095282');
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
