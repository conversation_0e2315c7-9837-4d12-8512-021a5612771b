<template>
  <div class="identity-drug-knowledge">
    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button
        type="primary"
        plain
        icon="el-icon-edit"
        size="mini"
        @click="handleEdit"
      >编辑标识</el-button>
      <el-button
        type="success"
        plain
        icon="el-icon-check"
        size="mini"
        @click="handleSave"
        :disabled="!editMode"
      >保存</el-button>
      <el-button
        type="info"
        plain
        icon="el-icon-close"
        size="mini"
        @click="handleCancel"
        :disabled="!editMode"
      >取消</el-button>
    </div>

    <!-- 药品标识信息 -->
    <el-card class="identity-card">
      <div slot="header" class="clearfix">
        <span>药品标识信息</span>
        <el-tag v-if="editMode" type="warning" size="mini" style="float: right;">编辑模式</el-tag>
        <el-tag v-else type="success" size="mini" style="float: right;">查看模式</el-tag>
      </div>
      
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="140px"
        :disabled="!editMode"
      >
        <el-row :gutter="20">
          <!-- 第一列 -->
          <el-col :span="8">
            <el-form-item label="抗菌药物" prop="isAntibac">
              <el-radio-group v-model="form.isAntibac">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="糖皮质激素" prop="glucocorticoid">
              <el-radio-group v-model="form.glucocorticoid">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="质子泵抑制剂" prop="protonPumpDrug">
              <el-radio-group v-model="form.protonPumpDrug">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="重点监控药物" prop="keyDrug">
              <el-radio-group v-model="form.keyDrug">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          
          <!-- 第二列 -->
          <el-col :span="8">
            <el-form-item label="血液制品" prop="bloodDrug">
              <el-radio-group v-model="form.bloodDrug">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="基本药物" prop="isBasicDrug">
              <el-radio-group v-model="form.isBasicDrug">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="抗肿瘤药物" prop="cancerDrug">
              <el-radio-group v-model="form.cancerDrug">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="毒麻药品" prop="isDjm">
              <el-radio-group v-model="form.isDjm">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          
          <!-- 第三列 -->
          <el-col :span="8">
            <el-form-item label="是否注射剂" prop="isInjection">
              <el-radio-group v-model="form.isInjection">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="中西药标志" prop="zxFlag">
              <el-select v-model="form.zxFlag" placeholder="请选择中西药标志">
                <el-option label="西药" value="1" />
                <el-option label="中成药" value="2" />
                <el-option label="草药" value="3" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="是否集采" prop="isPurchaseInternet">
              <el-radio-group v-model="form.isPurchaseInternet">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getDrugIdentity, updateDrugIdentity } from "@/api/rms/drugknowledge"

export default {
  name: "IdentityDrugKnowledge",
  props: {
    drug: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 编辑模式
      editMode: false,
      // 加载状态
      loading: false,
      // 表单数据
      form: {
        drugCode: null,
        isAntibac: '0',
        glucocorticoid: '0',
        protonPumpDrug: '0',
        keyDrug: '0',
        bloodDrug: '0',
        isBasicDrug: '0',
        cancerDrug: '0',
        isDjm: '0',
        isInjection: '0',
        zxFlag: '1',
        isPurchaseInternet: '0'
      },
      // 原始数据备份
      originalForm: {},
      // 表单验证规则
      rules: {
        isAntibac: [
          { required: true, message: "请选择是否抗菌药物", trigger: "change" }
        ],
        glucocorticoid: [
          { required: true, message: "请选择是否糖皮质激素", trigger: "change" }
        ],
        protonPumpDrug: [
          { required: true, message: "请选择是否质子泵抑制剂", trigger: "change" }
        ],
        keyDrug: [
          { required: true, message: "请选择是否重点监控药物", trigger: "change" }
        ],
        bloodDrug: [
          { required: true, message: "请选择是否血液制品", trigger: "change" }
        ],
        isBasicDrug: [
          { required: true, message: "请选择是否基本药物", trigger: "change" }
        ],
        cancerDrug: [
          { required: true, message: "请选择是否抗肿瘤药物", trigger: "change" }
        ],
        isDjm: [
          { required: true, message: "请选择是否毒麻药品", trigger: "change" }
        ],
        isInjection: [
          { required: true, message: "请选择是否注射剂", trigger: "change" }
        ],
        zxFlag: [
          { required: true, message: "请选择中西药标志", trigger: "change" }
        ],
        isPurchaseInternet: [
          { required: true, message: "请选择是否集采", trigger: "change" }
        ]
      }
    }
  },
  watch: {
    drug: {
      handler(newVal) {
        if (newVal && newVal.ypCode) {
          this.loadDrugIdentity()
        } else {
          // 如果没有药品信息，重置为默认值
          this.resetForm()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载药品标识信息
    loadDrugIdentity() {
      if (!this.drug || !this.drug.ypCode) return
      
      this.loading = true
      getDrugIdentity(this.drug.ypCode).then(response => {
        // 处理返回的数据，确保null或undefined值转换为字符串'0'
        const data = response.data || {}
        
        // 设置表单数据，确保所有值都是字符串类型
        this.form = {
          drugCode: this.drug.ypCode,
          isAntibac: this.normalizeValue(data.isAntibac, '0'),
          glucocorticoid: this.normalizeValue(data.glucocorticoid, '0'),
          protonPumpDrug: this.normalizeValue(data.protonPumpDrug, '0'),
          keyDrug: this.normalizeValue(data.keyDrug, '0'),
          bloodDrug: this.normalizeValue(data.bloodDrug, '0'),
          isBasicDrug: this.normalizeValue(data.isBasicDrug, '0'),
          cancerDrug: this.normalizeValue(data.cancerDrug, '0'),
          isDjm: this.normalizeValue(data.isDjm, '0'),
          isInjection: this.normalizeValue(data.isInjection, '0'),
          zxFlag: this.normalizeValue(data.zxFlag, '1'),
          isPurchaseInternet: this.normalizeValue(data.isPurchaseInternet, '0')
        }
        
        this.originalForm = { ...this.form }
        this.loading = false
      }).catch(() => {
        // 发生错误时，使用默认值
        this.resetForm()
        this.loading = false
      })
    },
    
    // 编辑
    handleEdit() {
      this.editMode = true
      this.originalForm = { ...this.form }
    },
    
    // 保存
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 设置药品编码
          this.form.drugCode = this.drug.ypCode
          
          this.loading = true
          updateDrugIdentity(this.form).then(response => {
            this.$modal.msgSuccess("保存成功")
            this.editMode = false
            this.originalForm = { ...this.form }
            this.loading = false
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    
    // 取消
    handleCancel() {
      this.form = { ...this.originalForm }
      this.editMode = false
    },
    
    // 标准化数据值，确保返回字符串类型
    normalizeValue(value, defaultValue) {
      if (value === null || value === undefined || value === '') {
        return defaultValue
      }
      return String(value)
    },
    
    // 重置表单为默认值
    resetForm() {
      this.form = {
        drugCode: this.drug ? this.drug.ypCode : null,
        isAntibac: '0',
        glucocorticoid: '0',
        protonPumpDrug: '0',
        keyDrug: '0',
        bloodDrug: '0',
        isBasicDrug: '0',
        cancerDrug: '0',
        isDjm: '0',
        isInjection: '0',
        zxFlag: '1',
        isPurchaseInternet: '0'
      }
      this.originalForm = { ...this.form }
    }
  }
}
</script>

<style scoped>
.identity-drug-knowledge {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.identity-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style> 