package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 处方明细信息对象 rms_t_pres_med
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
public class RmsTPresMed extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 处方编码 */
    @Excel(name = "处方编码")
    private String code;

    /** 药品名称（商品名） */
    @Excel(name = "药品名称", readConverterExp = "商=品名")
    private String medName;

    /** 医院药品代码 */
    @Excel(name = "医院药品代码")
    private String hisCode;

    /** 医保代码 */
    @Excel(name = "医保代码")
    private String insurCode;

    /** 批准文号 */
    @Excel(name = "批准文号")
    private String approval;

    /** 规格 */
    @Excel(name = "规格")
    private String spec;

    /** 组号 */
    @Excel(name = "组号")
    private String group;

    /** 用药理由 */
    @Excel(name = "用药理由")
    private String reason;

    /** 单次量单位 */
    @Excel(name = "单次量单位")
    private String doseUnit;

    /** 单次量 */
    @Excel(name = "单次量")
    private String dose;

    /** 开药数量 */
    @Excel(name = "开药数量")
    private String ordQty;

    /** 开药数量单位 */
    @Excel(name = "开药数量单位")
    private String ordUom;

    /** 频次代码 */
    @Excel(name = "频次代码")
    private String freq;

    /** 给药途径代码 */
    @Excel(name = "给药途径代码")
    private String administer;

    /** 用药开始时间（住院） */
    @Excel(name = "用药开始时间", readConverterExp = "住=院")
    private String beginTime;

    /** 用药结束时间（住院） */
    @Excel(name = "用药结束时间", readConverterExp = "住=院")
    private String endTime;

    /** 服药天数（门诊） */
    @Excel(name = "服药天数", readConverterExp = "门=诊")
    private String days;

    /** 煎煮代码 */
    @Excel(name = "煎煮代码")
    private String decoctionCode;

    /** 金额 */
    @Excel(name = "金额")
    private String money;

    /** 处方ID */
    @Excel(name = "处方ID")
    private String presId;

    /** 用药原因1 */
    @Excel(name = "用药原因1")
    private String medReason1;

    /** 用药说明 */
    @Excel(name = "用药说明")
    private String yysm;

    /** 备注 */
    @Excel(name = "备注")
    private String bz;

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public void setMedName(String medName)
    {
        this.medName = medName;
    }

    public String getMedName()
    {
        return medName;
    }

    public void setHisCode(String hisCode)
    {
        this.hisCode = hisCode;
    }

    public String getHisCode()
    {
        return hisCode;
    }

    public void setInsurCode(String insurCode)
    {
        this.insurCode = insurCode;
    }

    public String getInsurCode()
    {
        return insurCode;
    }

    public void setApproval(String approval)
    {
        this.approval = approval;
    }

    public String getApproval()
    {
        return approval;
    }

    public void setSpec(String spec)
    {
        this.spec = spec;
    }

    public String getSpec()
    {
        return spec;
    }

    public void setGroup(String group)
    {
        this.group = group;
    }

    public String getGroup()
    {
        return group;
    }

    public void setReason(String reason)
    {
        this.reason = reason;
    }

    public String getReason()
    {
        return reason;
    }

    public void setDoseUnit(String doseUnit)
    {
        this.doseUnit = doseUnit;
    }

    public String getDoseUnit()
    {
        return doseUnit;
    }

    public void setDose(String dose)
    {
        this.dose = dose;
    }

    public String getDose()
    {
        return dose;
    }

    public void setOrdQty(String ordQty)
    {
        this.ordQty = ordQty;
    }

    public String getOrdQty()
    {
        return ordQty;
    }

    public void setOrdUom(String ordUom)
    {
        this.ordUom = ordUom;
    }

    public String getOrdUom()
    {
        return ordUom;
    }

    public void setFreq(String freq)
    {
        this.freq = freq;
    }

    public String getFreq()
    {
        return freq;
    }

    public void setAdminister(String administer)
    {
        this.administer = administer;
    }

    public String getAdminister()
    {
        return administer;
    }

    public void setBeginTime(String beginTime)
    {
        this.beginTime = beginTime;
    }

    public String getBeginTime()
    {
        return beginTime;
    }

    public void setEndTime(String endTime)
    {
        this.endTime = endTime;
    }

    public String getEndTime()
    {
        return endTime;
    }

    public void setDays(String days)
    {
        this.days = days;
    }

    public String getDays()
    {
        return days;
    }

    public void setDecoctionCode(String decoctionCode)
    {
        this.decoctionCode = decoctionCode;
    }

    public String getDecoctionCode()
    {
        return decoctionCode;
    }

    public void setMoney(String money)
    {
        this.money = money;
    }

    public String getMoney()
    {
        return money;
    }

    public void setPresId(String presId)
    {
        this.presId = presId;
    }

    public String getPresId()
    {
        return presId;
    }

    public void setMedReason1(String medReason1)
    {
        this.medReason1 = medReason1;
    }

    public String getMedReason1()
    {
        return medReason1;
    }

    public void setYysm(String yysm)
    {
        this.yysm = yysm;
    }

    public String getYysm()
    {
        return yysm;
    }

    public void setBz(String bz)
    {
        this.bz = bz;
    }

    public String getBz()
    {
        return bz;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("medName", getMedName())
            .append("hisCode", getHisCode())
            .append("insurCode", getInsurCode())
            .append("approval", getApproval())
            .append("spec", getSpec())
            .append("group", getGroup())
            .append("reason", getReason())
            .append("doseUnit", getDoseUnit())
            .append("dose", getDose())
            .append("ordQty", getOrdQty())
            .append("ordUom", getOrdUom())
            .append("freq", getFreq())
            .append("administer", getAdminister())
            .append("beginTime", getBeginTime())
            .append("endTime", getEndTime())
            .append("days", getDays())
            .append("decoctionCode", getDecoctionCode())
            .append("money", getMoney())
            .append("presId", getPresId())
            .append("medReason1", getMedReason1())
            .append("yysm", getYysm())
            .append("bz", getBz())
            .toString();
    }
}
