package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.RmsTIcd10Base;

/**
 * 诊断基础信息表Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface IRmsTIcd10BaseService 
{
    /**
     * 查询诊断基础信息表
     * 
     * @param id 诊断基础信息表主键
     * @return 诊断基础信息表
     */
    public RmsTIcd10Base selectRmsTIcd10BaseById(Long id);

    /**
     * 查询诊断基础信息表列表
     * 
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 诊断基础信息表集合
     */
    public List<RmsTIcd10Base> selectRmsTIcd10BaseList(RmsTIcd10Base rmsTIcd10Base);

    /**
     * 新增诊断基础信息表
     * 
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 结果
     */
    public int insertRmsTIcd10Base(RmsTIcd10Base rmsTIcd10Base);

    /**
     * 修改诊断基础信息表
     * 
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 结果
     */
    public int updateRmsTIcd10Base(RmsTIcd10Base rmsTIcd10Base);

    /**
     * 批量删除诊断基础信息表
     * 
     * @param ids 需要删除的诊断基础信息表主键集合
     * @return 结果
     */
    public int deleteRmsTIcd10BaseByIds(Long[] ids);

    /**
     * 删除诊断基础信息表信息
     * 
     * @param id 诊断基础信息表主键
     * @return 结果
     */
    public int deleteRmsTIcd10BaseById(Long id);
}
