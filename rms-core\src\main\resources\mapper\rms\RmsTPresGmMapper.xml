<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTPresGmMapper">
    
    <resultMap type="RmsTPresGm" id="RmsTPresGmResult">
        <result property="code"    column="code"    />
        <result property="type"    column="type"    />
        <result property="name"    column="name"    />
        <result property="gmdm"    column="gmdm"    />
    </resultMap>

    <sql id="selectRmsTPresGmVo">
        select code, type, name, gmdm from rms_t_pres_gm
    </sql>

    <select id="selectRmsTPresGmList" parameterType="RmsTPresGm" resultMap="RmsTPresGmResult">
        <include refid="selectRmsTPresGmVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="gmdm != null  and gmdm != ''"> and gmdm = #{gmdm}</if>
        </where>
    </select>
    
    <select id="selectRmsTPresGmByCode" parameterType="String" resultMap="RmsTPresGmResult">
        <include refid="selectRmsTPresGmVo"/>
        where code = #{code}
    </select>

    <insert id="insertRmsTPresGm" parameterType="RmsTPresGm">
        insert into rms_t_pres_gm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="type != null">type,</if>
            <if test="name != null">name,</if>
            <if test="gmdm != null">gmdm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="gmdm != null">#{gmdm},</if>
         </trim>
    </insert>

    <update id="updateRmsTPresGm" parameterType="RmsTPresGm">
        update rms_t_pres_gm
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="name != null">name = #{name},</if>
            <if test="gmdm != null">gmdm = #{gmdm},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteRmsTPresGmByCode" parameterType="String">
        delete from rms_t_pres_gm where code = #{code}
    </delete>

    <delete id="deleteRmsTPresGmByCodes" parameterType="String">
        delete from rms_t_pres_gm where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>