import request from '@/utils/request'

// 查询药品常规用量条件列表
export function listTsdacglcondition(query) {
  return request({
    url: '/rms/tsdacglcondition/list',
    method: 'get',
    params: query
  })
}

// 查询药品常规用量条件详细
export function getTsdacglcondition(id) {
  return request({
    url: '/rms/tsdacglcondition/' + id,
    method: 'get'
  })
}

// 新增药品常规用量条件
export function addTsdacglcondition(data) {
  return request({
    url: '/rms/tsdacglcondition',
    method: 'post',
    data: data
  })
}

// 修改药品常规用量条件
export function updateTsdacglcondition(data) {
  return request({
    url: '/rms/tsdacglcondition',
    method: 'put',
    data: data
  })
}

// 删除药品常规用量条件
export function delTsdacglcondition(id) {
  return request({
    url: '/rms/tsdacglcondition/' + id,
    method: 'delete'
  })
}
