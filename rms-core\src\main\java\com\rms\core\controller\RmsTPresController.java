package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTPres;
import com.rms.core.service.IRmsTPresService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 处方信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@RestController
@RequestMapping("/rms/tpres")
public class RmsTPresController extends BaseController
{
    @Autowired
    private IRmsTPresService rmsTPresService;

    /**
     * 查询处方信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpres:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTPres rmsTPres)
    {
        startPage();
        List<RmsTPres> list = rmsTPresService.selectRmsTPresList(rmsTPres);
        return getDataTable(list);
    }

    /**
     * 导出处方信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpres:export')")
    @Log(title = "处方信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTPres rmsTPres)
    {
        List<RmsTPres> list = rmsTPresService.selectRmsTPresList(rmsTPres);
        ExcelUtil<RmsTPres> util = new ExcelUtil<RmsTPres>(RmsTPres.class);
        util.exportExcel(response, list, "处方信息数据");
    }

    /**
     * 获取处方信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpres:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(rmsTPresService.selectRmsTPresByCode(code));
    }

    /**
     * 新增处方信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpres:add')")
    @Log(title = "处方信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTPres rmsTPres)
    {
        return toAjax(rmsTPresService.insertRmsTPres(rmsTPres));
    }

    /**
     * 修改处方信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpres:edit')")
    @Log(title = "处方信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTPres rmsTPres)
    {
        return toAjax(rmsTPresService.updateRmsTPres(rmsTPres));
    }

    /**
     * 删除处方信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpres:remove')")
    @Log(title = "处方信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(rmsTPresService.deleteRmsTPresByCodes(codes));
    }
}
