package com.rms.core.domain.dto;

import javax.validation.constraints.NotBlank;

/**
 * 医生信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class DoctorDTO {

    /**
     * 医生代码
     */
    @NotBlank(message = "医生代码不能为空")
    private String code;

    /**
     * 医生姓名
     */
    @NotBlank(message = "医生姓名不能为空")
    private String name;

    /**
     * 医生级别代码
     */
    @NotBlank(message = "医生级别代码不能为空")
    private String type;

    /**
     * 医生级别名称
     */
    @NotBlank(message = "医生级别名称不能为空")
    private String typeName;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    @Override
    public String toString() {
        return "DoctorDTO{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", typeName='" + typeName + '\'' +
                '}';
    }
}
