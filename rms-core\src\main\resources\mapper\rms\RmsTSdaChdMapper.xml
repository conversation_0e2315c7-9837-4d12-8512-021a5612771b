<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaChdMapper">
    
    <resultMap type="RmsTSdaChd" id="RmsTSdaChdResult">
        <result property="id"    column="id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="chdJdMin"    column="chd_jd_min"    />
        <result property="chdJdMax"    column="chd_jd_max"    />
        <result property="result"    column="result"    />
        <result property="chBs"    column="ch_bs"    />
        <result property="ageMin"    column="age_min"    />
        <result property="ageMax"    column="age_max"    />
        <result property="ispb"    column="ispb"    />
    </resultMap>

    <sql id="selectRmsTSdaChdVo">
        select id, sda_id, chd_jd_min, chd_jd_max, result, ch_bs, age_min, age_max, ispb from rms_t_sda_chd
    </sql>

    <select id="selectRmsTSdaChdList" parameterType="RmsTSdaChd" resultMap="RmsTSdaChdResult">
        <include refid="selectRmsTSdaChdVo"/>
        <where>  
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="chdJdMin != null "> and chd_jd_min = #{chdJdMin}</if>
            <if test="chdJdMax != null "> and chd_jd_max = #{chdJdMax}</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="chBs != null  and chBs != ''"> and ch_bs = #{chBs}</if>
            <if test="ageMin != null "> and age_min = #{ageMin}</if>
            <if test="ageMax != null "> and age_max = #{ageMax}</if>
            <if test="ispb != null  and ispb != ''"> and ispb = #{ispb}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaChdById" parameterType="Long" resultMap="RmsTSdaChdResult">
        <include refid="selectRmsTSdaChdVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaChd" parameterType="RmsTSdaChd" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_sda_chd
        (sda_id, chd_jd_min, chd_jd_max, result, ch_bs, age_min, age_max, ispb)
        values (#{sdaId}, #{chdJdMin}, #{chdJdMax}, #{result}, #{chBs}, #{ageMin}, #{ageMax}, #{ispb})
    </insert>

    <update id="updateRmsTSdaChd" parameterType="RmsTSdaChd">
        update rms_t_sda_chd
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="chdJdMin != null">chd_jd_min = #{chdJdMin},</if>
            <if test="chdJdMax != null">chd_jd_max = #{chdJdMax},</if>
            <if test="result != null">result = #{result},</if>
            <if test="chBs != null">ch_bs = #{chBs},</if>
            <if test="ageMin != null">age_min = #{ageMin},</if>
            <if test="ageMax != null">age_max = #{ageMax},</if>
            <if test="ispb != null">ispb = #{ispb},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaChdById" parameterType="Long">
        delete from rms_t_sda_chd where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaChdByIds" parameterType="Long">
        delete from rms_t_sda_chd where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>