package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.DrugQueryResult;
import com.rms.core.domain.RmsTSdaChd;
import com.rms.core.domain.RmsTSdaElder;
import com.rms.core.domain.RmsTSdaGestation;
import com.rms.core.domain.RmsTSdaSex;
import com.rms.core.domain.RmsTSdaGytj;
import com.rms.core.domain.RmsTTjBase;
import com.rms.core.domain.RmsItfHosDrug;
import com.rms.core.domain.RmsTXhzyEdi;
import com.rms.core.domain.RmsTXhzyEdiZy;
import com.rms.core.domain.IncompatibleDrugResult;
import com.rms.core.domain.RmsTSdaIcd10Info;
import com.rms.core.domain.RmsTIcd10Base;
import com.rms.core.domain.DiagnosisRuleResult;
import com.rms.core.domain.RmsTSdaGm;
import com.rms.core.domain.RmsTGmbase;

/**
 * 药品知识库Service接口
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface IRmsDrugKnowledgeService
{
    /**
     * 根据关键字查询药品
     *
     * @param keyword 检索关键字
     * @return 药品查询结果集合
     */
    public List<DrugQueryResult> searchDrugs(String keyword);

    /**
     * 根据sdaId查询儿童用药规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 儿童用药规则库集合
     */
    public List<RmsTSdaChd> getChildDrugRulesBySdaId(Long sdaId);

    /**
     * 新增儿童用药规则库
     *
     * @param rmsTSdaChd 儿童用药规则库
     * @return 结果
     */
    public int insertChildDrugRule(RmsTSdaChd rmsTSdaChd);

    /**
     * 修改儿童用药规则库
     *
     * @param rmsTSdaChd 儿童用药规则库
     * @return 结果
     */
    public int updateChildDrugRule(RmsTSdaChd rmsTSdaChd);

    /**
     * 删除儿童用药规则库
     *
     * @param id 儿童用药规则库主键
     * @return 结果
     */
    public int deleteChildDrugRule(Long id);

    /**
     * 批量删除儿童用药规则库
     *
     * @param ids 儿童用药规则库主键数组
     * @return 结果
     */
    public int deleteChildDrugRules(Long[] ids);

    /**
     * 根据sdaId查询老年人用药规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 老年人用药规则库集合
     */
    public List<RmsTSdaElder> getElderDrugRulesBySdaId(Long sdaId);

    /**
     * 新增老年人用药规则库
     *
     * @param rmsTSdaElder 老年人用药规则库
     * @return 结果
     */
    public int insertElderDrugRule(RmsTSdaElder rmsTSdaElder);

    /**
     * 修改老年人用药规则库
     *
     * @param rmsTSdaElder 老年人用药规则库
     * @return 结果
     */
    public int updateElderDrugRule(RmsTSdaElder rmsTSdaElder);

    /**
     * 删除老年人用药规则库
     *
     * @param id 老年人用药规则库主键
     * @return 结果
     */
    public int deleteElderDrugRule(Long id);

    /**
     * 批量删除老年人用药规则库
     *
     * @param ids 老年人用药规则库主键数组
     * @return 结果
     */
    public int deleteElderDrugRules(Long[] ids);

    /**
     * 根据sdaId查询孕妇用药规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 孕妇用药规则库集合
     */
    public List<RmsTSdaGestation> getPregnancyDrugRulesBySdaId(Long sdaId);

    /**
     * 新增孕妇用药规则库
     *
     * @param rmsTSdaGestation 孕妇用药规则库
     * @return 结果
     */
    public int insertPregnancyDrugRule(RmsTSdaGestation rmsTSdaGestation);

    /**
     * 修改孕妇用药规则库
     *
     * @param rmsTSdaGestation 孕妇用药规则库
     * @return 结果
     */
    public int updatePregnancyDrugRule(RmsTSdaGestation rmsTSdaGestation);

    /**
     * 删除孕妇用药规则库
     *
     * @param id 孕妇用药规则库主键
     * @return 结果
     */
    public int deletePregnancyDrugRule(Long id);

    /**
     * 批量删除孕妇用药规则库
     *
     * @param ids 孕妇用药规则库主键数组
     * @return 结果
     */
    public int deletePregnancyDrugRules(Long[] ids);

    /**
     * 根据sdaId查询性别用药规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 性别用药规则库集合
     */
    public List<RmsTSdaSex> getSexDrugRulesBySdaId(Long sdaId);

    /**
     * 新增性别用药规则库
     *
     * @param rmsTSdaSex 性别用药规则库
     * @return 结果
     */
    public int insertSexDrugRule(RmsTSdaSex rmsTSdaSex);

    /**
     * 修改性别用药规则库
     *
     * @param rmsTSdaSex 性别用药规则库
     * @return 结果
     */
    public int updateSexDrugRule(RmsTSdaSex rmsTSdaSex);

    /**
     * 删除性别用药规则库
     *
     * @param id 性别用药规则库主键
     * @return 结果
     */
    public int deleteSexDrugRule(Long id);

    /**
     * 批量删除性别用药规则库
     *
     * @param ids 性别用药规则库主键数组
     * @return 结果
     */
    public int deleteSexDrugRules(Long[] ids);

    /**
     * 获取所有给药途径基础数据
     *
     * @return 给药途径基础数据集合
     */
    public List<RmsTTjBase> getAllRouteTypes();

    /**
     * 根据sdaId查询药品的给药途径信息
     *
     * @param sdaId 标准数据ID
     * @return 给药途径集合
     */
    public List<RmsTSdaGytj> getRouteDrugRulesBySdaId(Long sdaId);

    /**
     * 为药品添加给药途径
     *
     * @param sdaId 标准数据ID
     * @param routeCodes 给药途径代码数组
     * @return 结果
     */
    public int addRouteDrugRules(Long sdaId, String[] routeCodes);

    /**
     * 删除药品的给药途径
     *
     * @param ids 给药途径ID数组
     * @return 结果
     */
    public int deleteRouteDrugRules(Long[] ids);

    /**
     * 根据drugCode查询药品标识信息
     *
     * @param drugCode 药品编码
     * @return 药品标识信息
     */
    public RmsItfHosDrug getDrugIdentityByDrugCode(String drugCode);

    /**
     * 更新药品标识信息
     *
     * @param rmsItfHosDrug 药品标识信息
     * @return 结果
     */
    public int updateDrugIdentity(RmsItfHosDrug rmsItfHosDrug);

    /**
     * 获取给药途径基础数据
     *
     * @return 给药途径基础数据集合
     */
    public List<RmsTTjBase> getRouteBaseList();

    /**
     * 根据sdaId查询常规用量规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 常规用量规则库集合
     */
    public List<java.util.Map<String, Object>> getDoseRulesBySdaId(Long sdaId);

    /**
     * 新增常规用量规则库
     *
     * @param params 常规用量规则参数
     * @return 结果
     */
    public int insertDoseRule(java.util.Map<String, Object> params);

    /**
     * 修改常规用量规则库
     *
     * @param params 常规用量规则参数
     * @return 结果
     */
    public int updateDoseRule(java.util.Map<String, Object> params);

    /**
     * 删除常规用量规则库
     *
     * @param id 常规用量规则库主键
     * @return 结果
     */
    public int deleteDoseRule(Long id);

    /**
     * 批量删除常规用量规则库
     *
     * @param ids 常规用量规则库主键数组
     * @return 结果
     */
    public int deleteDoseRules(Long[] ids);

    // ========== 配伍禁忌相关方法 ==========

    /**
     * 根据sdaId查询配伍禁忌信息（西药）
     *
     * @param sdaId 标准数据ID
     * @return 配伍禁忌集合
     */
    public List<IncompatibleDrugResult> getIncompatibleRulesBySdaId(Long sdaId);

    /**
     * 新增配伍禁忌规则（西药）
     *
     * @param rmsTXhzyEdi 配伍禁忌规则
     * @return 结果
     */
    public int insertIncompatibleRule(RmsTXhzyEdi rmsTXhzyEdi);

    /**
     * 修改配伍禁忌规则（西药）
     *
     * @param rmsTXhzyEdi 配伍禁忌规则
     * @return 结果
     */
    public int updateIncompatibleRule(RmsTXhzyEdi rmsTXhzyEdi);

    /**
     * 删除配伍禁忌规则（西药）
     *
     * @param id 配伍禁忌规则主键
     * @return 结果
     */
    public int deleteIncompatibleRule(Long id);

    /**
     * 批量删除配伍禁忌规则（西药）
     *
     * @param ids 配伍禁忌规则主键数组
     * @return 结果
     */
    public int deleteIncompatibleRules(Long[] ids);

    /**
     * 根据sdaId查询配伍禁忌信息（中药）
     *
     * @param sdaId 标准数据ID
     * @return 配伍禁忌集合
     */
    public List<IncompatibleDrugResult> getIncompatibleRulesZyBySdaId(Long sdaId);

    /**
     * 新增配伍禁忌规则（中药）
     *
     * @param rmsTXhzyEdiZy 配伍禁忌规则
     * @return 结果
     */
    public int insertIncompatibleRuleZy(RmsTXhzyEdiZy rmsTXhzyEdiZy);

    /**
     * 修改配伍禁忌规则（中药）
     *
     * @param rmsTXhzyEdiZy 配伍禁忌规则
     * @return 结果
     */
    public int updateIncompatibleRuleZy(RmsTXhzyEdiZy rmsTXhzyEdiZy);

    /**
     * 删除配伍禁忌规则（中药）
     *
     * @param id 配伍禁忌规则主键
     * @return 结果
     */
    public int deleteIncompatibleRuleZy(Long id);

    /**
     * 批量删除配伍禁忌规则（中药）
     *
     * @param ids 配伍禁忌规则主键数组
     * @return 结果
     */
    public int deleteIncompatibleRulesZy(Long[] ids);

    // ========== 药物与诊断相关方法 ==========

    /**
     * 根据sdaId查询药物与诊断信息（包含ICD信息）
     *
     * @param sdaId 标准数据ID
     * @return 药物与诊断信息集合
     */
    public List<DiagnosisRuleResult> getDiagnosisRulesBySdaId(Long sdaId);

    /**
     * 新增药物与诊断信息
     *
     * @param rmsTSdaIcd10Info 药物与诊断信息
     * @return 结果
     */
    public int insertDiagnosisRule(RmsTSdaIcd10Info rmsTSdaIcd10Info);

    /**
     * 删除药物与诊断信息
     *
     * @param id 药物与诊断信息主键
     * @return 结果
     */
    public int deleteDiagnosisRule(Long id);

    /**
     * 批量删除药物与诊断信息
     *
     * @param ids 药物与诊断信息主键数组
     * @return 结果
     */
    public int deleteDiagnosisRules(Long[] ids);

    /**
     * 根据关键字搜索ICD诊断信息
     *
     * @param keyword 搜索关键字
     * @return ICD诊断信息集合
     */
    public List<RmsTIcd10Base> searchDiagnosis(String keyword);

    /**
     * 根据sdaId查询药物禁忌症信息（包含ICD信息）
     *
     * @param sdaId 标准数据ID
     * @return 药物禁忌症信息集合
     */
    public List<DiagnosisRuleResult> getContraindicationRulesBySdaId(Long sdaId);

    /**
     * 新增药物禁忌症信息
     *
     * @param rmsTSdaIcd10Info 药物禁忌症信息
     * @return 结果
     */
    public int insertContraindicationRule(RmsTSdaIcd10Info rmsTSdaIcd10Info);

    /**
     * 删除药物禁忌症信息
     *
     * @param id 药物禁忌症信息主键
     * @return 结果
     */
    public int deleteContraindicationRule(Long id);

    /**
     * 批量删除药物禁忌症信息
     *
     * @param ids 药物禁忌症信息主键数组
     * @return 结果
     */
    public int deleteContraindicationRules(Long[] ids);

    // ========== 过敏知识库相关方法 ==========

    /**
     * 获取所有过敏基础数据
     *
     * @return 过敏基础数据集合
     */
    public List<RmsTGmbase> getAllergyBaseList();

    /**
     * 根据sdaId查询药品过敏信息（包含过敏名称）
     *
     * @param sdaId 标准数据ID
     * @return 药品过敏信息集合
     */
    public List<java.util.Map<String, Object>> getAllergyRulesBySdaId(Long sdaId);

    /**
     * 新增药品过敏信息
     *
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    public int insertAllergyRule(RmsTSdaGm rmsTSdaGm);

    /**
     * 修改药品过敏信息
     *
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    public int updateAllergyRule(RmsTSdaGm rmsTSdaGm);

    /**
     * 删除药品过敏信息
     *
     * @param id 药品过敏信息主键
     * @return 结果
     */
    public int deleteAllergyRule(Long id);

    /**
     * 批量删除药品过敏信息
     *
     * @param ids 药品过敏信息主键数组
     * @return 结果
     */
    public int deleteAllergyRules(Long[] ids);

    /**
     * 根据关键字搜索过敏信息
     *
     * @param keyword 搜索关键字
     * @return 过敏信息集合
     */
    public List<java.util.Map<String, Object>> searchAllergy(String keyword);

    /**
     * 根据关键字搜索过敏基础数据
     *
     * @param keyword 搜索关键字
     * @return 过敏基础数据集合
     */
    public List<RmsTGmbase> searchAllergyBase(String keyword);
}
