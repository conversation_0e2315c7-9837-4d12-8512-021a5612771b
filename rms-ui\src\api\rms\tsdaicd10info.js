import request from '@/utils/request'

// 查询药物与诊断信息列表
export function listTsdaicd10info(query) {
  return request({
    url: '/rms/tsdaicd10info/list',
    method: 'get',
    params: query
  })
}

// 查询药物与诊断信息详细
export function getTsdaicd10info(id) {
  return request({
    url: '/rms/tsdaicd10info/' + id,
    method: 'get'
  })
}

// 新增药物与诊断信息
export function addTsdaicd10info(data) {
  return request({
    url: '/rms/tsdaicd10info',
    method: 'post',
    data: data
  })
}

// 修改药物与诊断信息
export function updateTsdaicd10info(data) {
  return request({
    url: '/rms/tsdaicd10info',
    method: 'put',
    data: data
  })
}

// 删除药物与诊断信息
export function delTsdaicd10info(id) {
  return request({
    url: '/rms/tsdaicd10info/' + id,
    method: 'delete'
  })
}
