package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.RmsTTjBase;

/**
 * 给药途径基础表Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IRmsTTjBaseService 
{
    /**
     * 查询给药途径基础表
     * 
     * @param name 给药途径基础表主键
     * @return 给药途径基础表
     */
    public RmsTTjBase selectRmsTTjBaseByName(String name);

    /**
     * 查询给药途径基础表列表
     * 
     * @param rmsTTjBase 给药途径基础表
     * @return 给药途径基础表集合
     */
    public List<RmsTTjBase> selectRmsTTjBaseList(RmsTTjBase rmsTTjBase);

    /**
     * 新增给药途径基础表
     * 
     * @param rmsTTjBase 给药途径基础表
     * @return 结果
     */
    public int insertRmsTTjBase(RmsTTjBase rmsTTjBase);

    /**
     * 修改给药途径基础表
     * 
     * @param rmsTTjBase 给药途径基础表
     * @return 结果
     */
    public int updateRmsTTjBase(RmsTTjBase rmsTTjBase);

    /**
     * 批量删除给药途径基础表
     * 
     * @param names 需要删除的给药途径基础表主键集合
     * @return 结果
     */
    public int deleteRmsTTjBaseByNames(String[] names);

    /**
     * 删除给药途径基础表信息
     * 
     * @param name 给药途径基础表主键
     * @return 结果
     */
    public int deleteRmsTTjBaseByName(String name);
}
