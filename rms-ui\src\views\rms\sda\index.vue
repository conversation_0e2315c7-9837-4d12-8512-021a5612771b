<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item prop="searchValue">
        <el-input
          v-model="queryParams.searchValue"
          placeholder="请输入查询关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rms:sda:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['rms:sda:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['rms:sda:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['rms:sda:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="sdaList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编码" align="center" prop="id" width="100" />
      <el-table-column label="药物类型码" align="center" prop="mid" width="100" />
      <el-table-column label="药品名称" align="center" prop="ym" />
      <el-table-column label="规格" align="center" prop="ywgg" />
      <el-table-column label="批准文号" align="center" prop="pzwh" />
      <el-table-column label="生产企业" align="center" prop="scqy" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rms:sda:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rms:sda:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改药品说明书对话框 -->
    <el-dialog :title="title" center :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- 基本信息 -->
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="药品名称" prop="Ym">
                <el-input v-model="form.Ym" placeholder="请输入药品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="拼音缩写" prop="Sp">
                <el-input v-model="form.Sp" placeholder="请输入拼音缩写" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="通用名称" prop="tymc">
                <el-input v-model="form.tymc" placeholder="请输入通用名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位" prop="UnitRem">
                <el-input v-model="form.UnitRem" placeholder="请输入单位" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="汉语拼音" prop="hypy">
                <el-input v-model="form.hypy" placeholder="请输入汉语拼音" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="英文名称" prop="yymc">
                <el-input v-model="form.yymc" placeholder="请输入英文名称" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 化学信息 -->
        <div class="form-section">
          <h4 class="section-title">化学信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分子式" prop="fzs">
                <el-input v-model="form.fzs" placeholder="请输入分子式" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分子量" prop="fzl">
                <el-input v-model="form.fzl" placeholder="请输入分子量" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="化学名称" prop="hxmc">
                <el-input v-model="form.hxmc" type="textarea" :rows="2" placeholder="请输入化学名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="主要成分" prop="zycf">
                <el-input v-model="form.zycf" type="textarea" :rows="2" placeholder="请输入主要成分" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="性状" prop="xz">
                <el-input v-model="form.xz" type="textarea" :rows="2" placeholder="请输入性状描述" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 药理信息 -->
        <div class="form-section">
          <h4 class="section-title">药理信息</h4>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="药理毒理" prop="yldl">
                <el-input v-model="form.yldl" type="textarea" :rows="3" placeholder="请输入药理毒理" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="药代动力学" prop="yddlx">
                <el-input v-model="form.yddlx" type="textarea" :rows="3" placeholder="请输入药代动力学" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="适应症" prop="syz">
                <el-input v-model="form.syz" type="textarea" :rows="3" placeholder="请输入适应症" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用法用量" prop="yfyl">
                <el-input v-model="form.yfyl" type="textarea" :rows="3" placeholder="请输入用法用量" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 安全信息 -->
        <div class="form-section">
          <h4 class="section-title">安全信息</h4>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="不良反应" prop="blfy">
                <el-input v-model="form.blfy" type="textarea" :rows="3" placeholder="请输入不良反应" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="禁忌症" prop="jjz">
                <el-input v-model="form.jjz" type="textarea" :rows="3" placeholder="请输入禁忌症" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="注意事项" prop="zysx">
                <el-input v-model="form.zysx" type="textarea" :rows="3" placeholder="请输入注意事项" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="孕妇用药" prop="yfyy">
                <el-input v-model="form.yfyy" type="textarea" :rows="2" placeholder="请输入孕妇用药" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="儿童用药" prop="etyy">
                <el-input v-model="form.etyy" type="textarea" :rows="2" placeholder="请输入儿童用药" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="老年人用药" prop="lnryy">
                <el-input v-model="form.lnryy" type="textarea" :rows="2" placeholder="请输入老年人用药" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="相互作用" prop="xhzy">
                <el-input v-model="form.xhzy" type="textarea" :rows="3" placeholder="请输入相互作用" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="药物过量" prop="ywgl">
                <el-input v-model="form.ywgl" type="textarea" :rows="3" placeholder="请输入药物过量" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 规格信息 -->
        <div class="form-section">
          <h4 class="section-title">规格信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="药物规格" prop="ywgg">
                <el-input v-model="form.ywgg" placeholder="请输入药物规格" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="储存条件" prop="cctj">
                <el-input v-model="form.cctj" placeholder="请输入储存条件" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="要点提示" prop="ydts">
                <el-input v-model="form.ydts" type="textarea" :rows="2" placeholder="请输入要点提示" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 生产信息 -->
        <div class="form-section">
          <h4 class="section-title">生产信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="生产企业" prop="scqy">
                <el-input v-model="form.scqy" placeholder="请输入生产企业" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="批准文号" prop="pzwh">
                <el-input v-model="form.pzwh" placeholder="请输入批准文号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="药物类型码" prop="mid">
                <el-input v-model="form.mid" placeholder="请输入药物类型码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="毒性" prop="dx">
                <el-input v-model="form.dx" placeholder="请输入毒性" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 药品详情查看对话框 -->
    <el-dialog title="药品说明书" center :visible.sync="viewOpen" width="1200px" append-to-body>
      <div class="drug-detail-container">
        <!-- 药品基本信息卡片 -->
        <el-card class="detail-card" shadow="hover">
          <div slot="header" class="detail-card-header">
            <i class="el-icon-medicine"></i>
            <span>基本信息</span>
          </div>
          <div class="detail-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">药品名称：</label>
                  <span class="detail-value drug-name">{{ viewForm.Ym || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">通用名称：</label>
                  <span class="detail-value">{{ viewForm.tymc || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">英文名称：</label>
                  <span class="detail-value">{{ viewForm.yymc || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">药物规格：</label>
                  <span class="detail-value">{{ viewForm.ywgg || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">药物分类：</label>
                  <span class="detail-value">{{ viewForm.mid || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">存储条件：</label>
                  <span class="detail-value">{{ viewForm.cctj || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 生产企业信息卡片 -->
        <el-card class="detail-card" shadow="hover">
          <div slot="header" class="detail-card-header">
            <i class="el-icon-office-building"></i>
            <span>生产企业信息</span>
          </div>
          <div class="detail-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="detail-item">
                  <label class="detail-label">生产企业：</label>
                  <span class="detail-value">{{ viewForm.scqy || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <label class="detail-label">批准文号：</label>
                  <span class="detail-value">{{ viewForm.pzwh || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 化学成分信息卡片 -->
        <el-card class="detail-card" shadow="hover">
          <div slot="header" class="detail-card-header">
            <i class="el-icon-connection"></i>
            <span>化学成分信息</span>
          </div>
          <div class="detail-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="detail-item">
                  <label class="detail-label">分子式：</label>
                  <span class="detail-value">{{ viewForm.fzs || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <label class="detail-label">分子量：</label>
                  <span class="detail-value">{{ viewForm.fzl || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">主要成分：</label>
                  <div class="detail-value detail-text">{{ viewForm.zycf || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">性状：</label>
                  <div class="detail-value detail-text">{{ viewForm.xz || '-' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 药理信息卡片 -->
        <el-card class="detail-card" shadow="hover">
          <div slot="header" class="detail-card-header">
            <i class="el-icon-cpu"></i>
            <span>药理信息</span>
          </div>
          <div class="detail-content">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">适应症：</label>
                  <div class="detail-value detail-text">{{ viewForm.syz || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">用法用量：</label>
                  <div class="detail-value detail-text">{{ viewForm.yfyl || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">药理毒理：</label>
                  <div class="detail-value detail-text">{{ viewForm.yldl || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">药代动力学：</label>
                  <div class="detail-value detail-text">{{ viewForm.yddlx || '-' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 安全信息卡片 -->
        <el-card class="detail-card" shadow="hover">
          <div slot="header" class="detail-card-header">
            <i class="el-icon-warning"></i>
            <span>安全信息</span>
          </div>
          <div class="detail-content">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">不良反应：</label>
                  <div class="detail-value detail-text">{{ viewForm.blfy || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">禁忌症：</label>
                  <div class="detail-value detail-text">{{ viewForm.jjz || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">注意事项：</label>
                  <div class="detail-value detail-text">{{ viewForm.zysx || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">药物相互作用：</label>
                  <div class="detail-value detail-text">{{ viewForm.xhzy || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="detail-item">
                  <label class="detail-label">药物过量：</label>
                  <div class="detail-value detail-text">{{ viewForm.ywgl || '-' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 特殊人群用药卡片 -->
        <el-card class="detail-card" shadow="hover">
          <div slot="header" class="detail-card-header">
            <i class="el-icon-user"></i>
            <span>特殊人群用药</span>
          </div>
          <div class="detail-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">孕妇用药：</label>
                  <div class="detail-value detail-text">{{ viewForm.yfyy || '-' }}</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">儿童用药：</label>
                  <div class="detail-value detail-text">{{ viewForm.etyy || '-' }}</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <label class="detail-label">老年人用药：</label>
                  <div class="detail-value detail-text">{{ viewForm.lnryy || '-' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSda, getSda, delSda, addSda, updateSda } from "@/api/rms/sda"

export default {
  name: "Sda",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 药品说明书表格数据
      sdaList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: null,
        Ym: null,
        Sp: null,
        hypy: null,
        UnitRem: null,
        tymc: null,
        yymc: null,
        hxmc: null,
        zycf: null,
        fzs: null,
        fzl: null,
        xz: null,
        yldl: null,
        yddlx: null,
        syz: null,
        yfyl: null,
        blfy: null,
        jjz: null,
        zysx: null,
        yfyy: null,
        etyy: null,
        lnryy: null,
        xhzy: null,
        ywgl: null,
        ywgg: null,
        cctj: null,
        ydts: null,
        ylgzd: null,
        fygzd: null,
        ydgzd: null,
        jxbs2: null,
        zxybs: null,
        ly: null,
        lyBs: null,
        scqy: null,
        bs: null,
        pzwh: null,
        cpym: null,
        cpsp: null,
        lnrBs: null,
        yfBs: null,
        yfqsyBs: null,
        yfsyhBs: null,
        brBs: null,
        ggBs: null,
        sgBs: null,
        hggBs: null,
        hsgBs: null,
        agemin: null,
        agemax: null,
        gytjBs: null,
        gytjBs1: null,
        gmdm: null,
        dc1: null,
        dr1: null,
        mid: null,
        dx: null,
      },
      // 表单参数
      form: {},
      // 查看详情数据
      viewForm: {},
      // 表单校验
      rules: {
        Ym: [
          { required: true, message: "药品名称不能为空", trigger: "blur" }
        ],
        tymc: [
          { required: true, message: "通用名称不能为空", trigger: "blur" }
        ],
        ywgg: [
          { required: true, message: "药物规格不能为空", trigger: "blur" }
        ],
        mid: [
          { required: true, message: "药物类型码不能为空", trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询药品说明书列表 */
    getList() {
      this.loading = true
      listSda(this.queryParams).then(response => {
        this.sdaList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        Ym: null,
        Sp: null,
        hypy: null,
        UnitRem: null,
        tymc: null,
        yymc: null,
        hxmc: null,
        zycf: null,
        fzs: null,
        fzl: null,
        xz: null,
        yldl: null,
        yddlx: null,
        syz: null,
        yfyl: null,
        blfy: null,
        jjz: null,
        zysx: null,
        yfyy: null,
        etyy: null,
        lnryy: null,
        xhzy: null,
        ywgl: null,
        ywgg: null,
        cctj: null,
        ydts: null,
        ylgzd: null,
        fygzd: null,
        ydgzd: null,
        jxbs2: null,
        zxybs: null,
        ly: null,
        lyBs: null,
        scqy: null,
        bs: null,
        pzwh: null,
        cpym: null,
        cpsp: null,
        lnrBs: null,
        yfBs: null,
        yfqsyBs: null,
        yfsyhBs: null,
        brBs: null,
        ggBs: null,
        sgBs: null,
        hggBs: null,
        hsgBs: null,
        agemin: null,
        agemax: null,
        gytjBs: null,
        gytjBs1: null,
        gmdm: null,
        dc1: null,
        dr1: null,
        mid: null,
        dx: null,
        createTime: null,
        updateTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      // 搜索时使用药品名称进行匹配
      this.queryParams.Ym = this.queryParams.searchValue
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.queryParams.searchValue = null
      this.queryParams.Ym = null
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加药品说明书"
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      const id = row.id
      getSda(id).then(response => {
        this.viewForm = response.data
        this.viewOpen = true
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getSda(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改药品说明书"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSda(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addSda(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除药品说明书编号为"' + ids + '"的数据项？').then(function() {
        return delSda(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('rms/sda/export', {
        ...this.queryParams
      }, `sda_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 30px;
}

.section-title {
  color: #409EFF;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 15px;
  margin-top: 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e6f7ff;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 30px;
  height: 2px;
  background-color: #409EFF;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-dialog__body {
  padding: 20px 25px;
  max-height: 600px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: center;
  padding: 20px 0 10px 0;
}

/* 药品详情样式 */
.drug-detail-container {
  padding: 0 10px;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.detail-card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #409EFF;
  font-size: 15px;
}

.detail-card-header i {
  margin-right: 8px;
  font-size: 16px;
}

.detail-content {
  padding: 15px 20px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  min-height: 24px;
}

.detail-label {
  flex-shrink: 0;
  width: 120px;
  font-weight: 500;
  color: #666;
  font-size: 13px;
  line-height: 1.5;
}

.detail-value {
  flex: 1;
  color: #333;
  font-size: 13px;
  line-height: 1.5;
  word-break: break-word;
}

.detail-text {
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
  white-space: pre-wrap;
  min-height: 20px;
}

.drug-name {
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
}

.detail-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
}

.detail-card .el-card__header {
  padding: 15px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-bottom: 1px solid #e4e7ed;
}

.detail-card .el-card__body {
  padding: 0;
}
</style>
