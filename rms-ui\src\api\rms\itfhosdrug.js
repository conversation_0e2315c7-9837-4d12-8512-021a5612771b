import request from '@/utils/request'

// 查询医院药品信息列表
export function listItfhosdrug(query) {
  return request({
    url: '/rms/itfhosdrug/list',
    method: 'get',
    params: query
  })
}

// 查询医院药品信息详细
export function getItfhosdrug(drugCode) {
  return request({
    url: '/rms/itfhosdrug/' + drugCode,
    method: 'get'
  })
}

// 新增医院药品信息
export function addItfhosdrug(data) {
  return request({
    url: '/rms/itfhosdrug',
    method: 'post',
    data: data
  })
}

// 修改医院药品信息
export function updateItfhosdrug(data) {
  return request({
    url: '/rms/itfhosdrug',
    method: 'put',
    data: data
  })
}

// 删除医院药品信息
export function delItfhosdrug(drugCode) {
  return request({
    url: '/rms/itfhosdrug/' + drugCode,
    method: 'delete'
  })
}
