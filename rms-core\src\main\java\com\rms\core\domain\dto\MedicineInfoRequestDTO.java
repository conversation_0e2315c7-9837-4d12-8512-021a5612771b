package com.rms.core.domain.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 药品信息查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class MedicineInfoRequestDTO {

    /**
     * 基本信息
     */
    @Valid
    @NotNull(message = "基本信息不能为空")
    private BaseInfoDTO baseInfo;

    /**
     * 详细信息
     */
    @Valid
    @NotNull(message = "详细信息不能为空")
    private MedicineInfoDetailsDTO details;

    public BaseInfoDTO getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseInfoDTO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public MedicineInfoDetailsDTO getDetails() {
        return details;
    }

    public void setDetails(MedicineInfoDetailsDTO details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "MedicineInfoRequestDTO{" +
                "baseInfo=" + baseInfo +
                ", details=" + details +
                '}';
    }
}
