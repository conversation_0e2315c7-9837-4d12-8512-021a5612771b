import request from '@/utils/request'

// 查询处方问题类别列表
export function listCfwtlb(query) {
  return request({
    url: '/rms/cfwtlb/list',
    method: 'get',
    params: query
  })
}

// 查询处方问题类别详细
export function getCfwtlb(cfwtbh) {
  return request({
    url: '/rms/cfwtlb/' + cfwtbh,
    method: 'get'
  })
}

// 新增处方问题类别
export function addCfwtlb(data) {
  return request({
    url: '/rms/cfwtlb',
    method: 'post',
    data: data
  })
}

// 修改处方问题类别
export function updateCfwtlb(data) {
  return request({
    url: '/rms/cfwtlb',
    method: 'put',
    data: data
  })
}

// 删除处方问题类别
export function delCfwtlb(cfwtbh) {
  return request({
    url: '/rms/cfwtlb/' + cfwtbh,
    method: 'delete'
  })
}
