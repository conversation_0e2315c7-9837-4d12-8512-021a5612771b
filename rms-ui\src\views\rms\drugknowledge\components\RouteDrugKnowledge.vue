<template>
  <div class="route-drug-knowledge">
    <el-card>
      <div slot="header" class="clearfix">
        <span>给药途径维护</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-refresh"
          @click="loadData"
        >
          刷新
        </el-button>
      </div>
      
      <el-transfer
        ref="routeTransfer"
        v-model="selectedRouteValues"
        :data="transferData"
        :titles="['可选给药途径', '已选给药途径']"
        :button-texts="['移除', '添加']"
        :render-content="renderContent"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}'
        }"
        filterable
        :filter-placeholder="'请输入给药途径名称'"
        :filter-method="filterMethod"
        :props="{
          key: 'key',
          label: 'label',
          disabled: 'disabled'
        }"
        @change="handleChange"
        style="min-height: 340px; align-items: center; justify-content: center; display: flex;"
      >
      </el-transfer>
    </el-card>
  </div>
</template>

<script>
import { getAllRouteTypes, getRouteDrugRules, addRouteDrugRules, deleteRouteDrugRules } from "@/api/rms/drugknowledge"

export default {
  name: "RouteDrugKnowledge",
  props: {
    drug: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 穿梭框数据源
      transferData: [],
      // 已选择的给药途径值
      selectedRouteValues: [],
      // 所有给药途径数据
      allRoutes: [],
      // 已选择的给药途径数据（从接口获取）
      selectedRouteList: [],
      // 给药途径映射
      routeMap: {}
    }
  },
  watch: {
    drug: {
      handler(newVal) {
        if (newVal && newVal.sdaId) {
          this.loadData()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载所有数据
    loadData() {
      this.loadAllRoutes()
      this.loadSelectedRoutes()
    },
    
    // 加载所有给药途径
    loadAllRoutes() {
      getAllRouteTypes().then(response => {
        this.allRoutes = response.data || []
        // 创建映射关系
        this.routeMap = {}
        this.allRoutes.forEach(route => {
          this.routeMap[route.dm] = {
            name: route.name,
            ms: route.ms
          }
        })
        this.buildTransferData()
      })
    },
    
    // 加载已选择的给药途径
    loadSelectedRoutes() {
      if (!this.drug || !this.drug.sdaId) return
      
      getRouteDrugRules(this.drug.sdaId).then(response => {
        this.selectedRouteList = response.data || []
        // 更新已选择的值
        this.selectedRouteValues = this.selectedRouteList.map(route => route.gytjCode)
        this.buildTransferData()
      }).catch(() => {
        this.selectedRouteList = []
        this.selectedRouteValues = []
        this.buildTransferData()
      })
    },
    
    // 构建穿梭框数据
    buildTransferData() {
      this.transferData = this.allRoutes.map(route => ({
        key: route.dm,
        label: route.name,
        description: route.ms,
        disabled: false
      }))
    },
    
    // 自定义渲染内容
    renderContent(h, option) {
      return h('div', {
        style: {
          padding: '8px 0',
          fontSize: '14px',
          color: '#303133',
          lineHeight: '1.4'
        }
      }, option.description || '暂无描述')
    },
    
    // 自定义筛选方法
    filterMethod(query, item) {
      // 支持按名称、代码、描述搜索
      const searchText = query.toLowerCase()
      return item.label.toLowerCase().includes(searchText) ||
             item.key.toLowerCase().includes(searchText) ||
             (item.description && item.description.toLowerCase().includes(searchText))
    },
    
    // 处理穿梭框变化
    handleChange(value, direction, movedKeys) {
      if (direction === 'right') {
        // 添加给药途径
        this.addRoutes(movedKeys)
      } else {
        // 删除给药途径
        this.deleteRoutes(movedKeys)
      }
    },
    
    // 添加给药途径
    addRoutes(routeCodes) {
      if (routeCodes.length === 0) return
      
      addRouteDrugRules(this.drug.sdaId, routeCodes).then(response => {
        this.$modal.msgSuccess(`成功添加 ${routeCodes.length} 个给药途径`)
        this.loadSelectedRoutes()
      }).catch(() => {
        this.$modal.msgError("添加失败")
        // 失败时需要恢复原状态
        this.selectedRouteValues = this.selectedRouteList.map(route => route.gytjCode)
      })
    },
    
    // 删除给药途径
    deleteRoutes(routeCodes) {
      if (routeCodes.length === 0) return
      
      // 找到要删除的记录ID
      const idsToDelete = []
      routeCodes.forEach(code => {
        const route = this.selectedRouteList.find(r => r.gytjCode === code)
        if (route) {
          idsToDelete.push(route.id)
        }
      })
      
      if (idsToDelete.length === 0) {
        this.$modal.msgError("未找到要删除的记录")
        return
      }
      
      deleteRouteDrugRules(idsToDelete).then(response => {
        this.$modal.msgSuccess(`成功删除 ${idsToDelete.length} 个给药途径`)
        this.loadSelectedRoutes()
      }).catch(() => {
        this.$modal.msgError("删除失败")
        // 失败时需要恢复原状态
        this.selectedRouteValues = this.selectedRouteList.map(route => route.gytjCode)
      })
    }
  }
}
</script>

<style scoped>
.route-drug-knowledge {
  padding: 20px 20px 0 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

/* 穿梭框样式调整 */
.el-transfer {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.el-transfer-panel {
  width: 300px;
  min-height: 400px;
}

.el-transfer-panel__header {
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 15px;
  font-weight: 500;
}

.el-transfer-panel__body {
  height: 350px;
}

.el-transfer-panel__list {
  height: 300px;
  overflow-y: auto;
}

.el-transfer-panel__item {
  height: auto;
  padding: 8px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.el-transfer-panel__item:hover {
  background-color: #f5f7fa;
}

.el-transfer-panel__item.is-checked {
  background-color: #e6f7ff;
}



/* 穿梭框条目样式 */
.el-transfer-panel__item .el-checkbox__label {
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
}

/* 筛选输入框样式 */
.el-transfer-panel__filter .el-input__inner {
  height: 32px;
  border-radius: 4px;
}

/* 按钮区域样式 */
.el-transfer__buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 30px;
}

.el-transfer__buttons .el-button {
  margin: 0 0 10px 0;
  padding: 8px 15px;
  border-radius: 4px;
}

.el-transfer__buttons .el-button:last-child {
  margin-bottom: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-transfer {
    max-width: 100%;
  }
  
  .el-transfer-panel {
    width: 280px;
  }
  
  .el-transfer__buttons {
    margin: 0 15px;
  }
}
</style>
