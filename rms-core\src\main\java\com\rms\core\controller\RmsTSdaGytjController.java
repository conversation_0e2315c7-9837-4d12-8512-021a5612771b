package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaGytj;
import com.rms.core.service.IRmsTSdaGytjService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 标准药品给药途径表Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/rms/tsdagytj")
public class RmsTSdaGytjController extends BaseController
{
    @Autowired
    private IRmsTSdaGytjService rmsTSdaGytjService;

    /**
     * 查询标准药品给药途径表列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagytj:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaGytj rmsTSdaGytj)
    {
        startPage();
        List<RmsTSdaGytj> list = rmsTSdaGytjService.selectRmsTSdaGytjList(rmsTSdaGytj);
        return getDataTable(list);
    }

    /**
     * 导出标准药品给药途径表列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagytj:export')")
    @Log(title = "标准药品给药途径表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaGytj rmsTSdaGytj)
    {
        List<RmsTSdaGytj> list = rmsTSdaGytjService.selectRmsTSdaGytjList(rmsTSdaGytj);
        ExcelUtil<RmsTSdaGytj> util = new ExcelUtil<RmsTSdaGytj>(RmsTSdaGytj.class);
        util.exportExcel(response, list, "标准药品给药途径表数据");
    }

    /**
     * 获取标准药品给药途径表详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagytj:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaGytjService.selectRmsTSdaGytjById(id));
    }

    /**
     * 新增标准药品给药途径表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagytj:add')")
    @Log(title = "标准药品给药途径表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaGytj rmsTSdaGytj)
    {
        return toAjax(rmsTSdaGytjService.insertRmsTSdaGytj(rmsTSdaGytj));
    }

    /**
     * 修改标准药品给药途径表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagytj:edit')")
    @Log(title = "标准药品给药途径表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaGytj rmsTSdaGytj)
    {
        return toAjax(rmsTSdaGytjService.updateRmsTSdaGytj(rmsTSdaGytj));
    }

    /**
     * 删除标准药品给药途径表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagytj:remove')")
    @Log(title = "标准药品给药途径表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaGytjService.deleteRmsTSdaGytjByIds(ids));
    }
}
