package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.RmsTSdaCglCondition;

/**
 * 药品常规用量条件Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IRmsTSdaCglConditionService 
{
    /**
     * 查询药品常规用量条件
     * 
     * @param id 药品常规用量条件主键
     * @return 药品常规用量条件
     */
    public RmsTSdaCglCondition selectRmsTSdaCglConditionById(Long id);

    /**
     * 查询药品常规用量条件列表
     * 
     * @param rmsTSdaCglCondition 药品常规用量条件
     * @return 药品常规用量条件集合
     */
    public List<RmsTSdaCglCondition> selectRmsTSdaCglConditionList(RmsTSdaCglCondition rmsTSdaCglCondition);

    /**
     * 新增药品常规用量条件
     * 
     * @param rmsTSdaCglCondition 药品常规用量条件
     * @return 结果
     */
    public int insertRmsTSdaCglCondition(RmsTSdaCglCondition rmsTSdaCglCondition);

    /**
     * 修改药品常规用量条件
     * 
     * @param rmsTSdaCglCondition 药品常规用量条件
     * @return 结果
     */
    public int updateRmsTSdaCglCondition(RmsTSdaCglCondition rmsTSdaCglCondition);

    /**
     * 批量删除药品常规用量条件
     * 
     * @param ids 需要删除的药品常规用量条件主键集合
     * @return 结果
     */
    public int deleteRmsTSdaCglConditionByIds(Long[] ids);

    /**
     * 删除药品常规用量条件信息
     * 
     * @param id 药品常规用量条件主键
     * @return 结果
     */
    public int deleteRmsTSdaCglConditionById(Long id);
}
