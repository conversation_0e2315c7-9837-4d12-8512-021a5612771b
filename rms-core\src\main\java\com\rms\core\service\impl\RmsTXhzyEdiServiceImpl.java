package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTXhzyEdiMapper;
import com.rms.core.domain.RmsTXhzyEdi;
import com.rms.core.service.IRmsTXhzyEdiService;

/**
 * 药品相互作用Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class RmsTXhzyEdiServiceImpl implements IRmsTXhzyEdiService
{
    @Autowired
    private RmsTXhzyEdiMapper rmsTXhzyEdiMapper;

    /**
     * 查询药品相互作用
     *
     * @param id 药品相互作用主键
     * @return 药品相互作用
     */
    @Override
    public RmsTXhzyEdi selectRmsTXhzyEdiById(Long id)
    {
        return rmsTXhzyEdiMapper.selectRmsTXhzyEdiById(id);
    }

    /**
     * 查询药品相互作用列表
     *
     * @param rmsTXhzyEdi 药品相互作用
     * @return 药品相互作用
     */
    @Override
    public List<RmsTXhzyEdi> selectRmsTXhzyEdiList(RmsTXhzyEdi rmsTXhzyEdi)
    {
        return rmsTXhzyEdiMapper.selectRmsTXhzyEdiList(rmsTXhzyEdi);
    }

    /**
     * 新增药品相互作用
     *
     * @param rmsTXhzyEdi 药品相互作用
     * @return 结果
     */
    @Override
    public int insertRmsTXhzyEdi(RmsTXhzyEdi rmsTXhzyEdi)
    {
        return rmsTXhzyEdiMapper.insertRmsTXhzyEdi(rmsTXhzyEdi);
    }

    /**
     * 修改药品相互作用
     *
     * @param rmsTXhzyEdi 药品相互作用
     * @return 结果
     */
    @Override
    public int updateRmsTXhzyEdi(RmsTXhzyEdi rmsTXhzyEdi)
    {
        return rmsTXhzyEdiMapper.updateRmsTXhzyEdi(rmsTXhzyEdi);
    }

    /**
     * 批量删除药品相互作用
     *
     * @param ids 需要删除的药品相互作用主键
     * @return 结果
     */
    @Override
    public int deleteRmsTXhzyEdiByIds(Long[] ids)
    {
        return rmsTXhzyEdiMapper.deleteRmsTXhzyEdiByIds(ids);
    }

    /**
     * 删除药品相互作用信息
     *
     * @param id 药品相互作用主键
     * @return 结果
     */
    @Override
    public int deleteRmsTXhzyEdiById(Long id)
    {
        return rmsTXhzyEdiMapper.deleteRmsTXhzyEdiById(id);
    }
}
