public class Tester {

    public static void main(String[] args) {
        System.out.println(buildPrescriptionAnalyzeRequest());
    }

    private static String buildPrescriptionAnalyzeRequest() {
        return "{\n" +
                "  \"baseInfo\": {\n" +
                "    \"source\": \"HIS\",\n" +
                "    \"hospCode\": \"H001\",\n" +
                "    \"deptCode\": \"D002\",\n" +
                "    \"deptName\": \"内科\",\n" +
                "    \"doctor\": {\n" +
                "      \"code\": \"DR003\",\n" +
                "      \"name\": \"张三\",\n" +
                "      \"type\": \"1\",\n" +
                "      \"typeName\": \"主治医师\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"details\": {\n" +
                "    \"isUpload\": 0,\n" +
                "    \"hisTime\": \"2025-07-20 10:30:00\",\n" +
                "    \"hospFlag\": \"op\",\n" +
                "    \"treatType\": 100,\n" +
                "    \"treatCode\": \"T000123\",\n" +
                "    \"patient\": {\n" +
                "      \"name\": \"李四\",\n" +
                "      \"isInfant\": 0,\n" +
                "      \"birth\": \"1990-01-15\",\n" +
                "      \"sex\": \"男\",\n" +
                "      \"weight\": 65,\n" +
                "      \"height\": 175,\n" +
                "      \"allergicData\": [\n" +
                "        {\n" +
                "          \"type\": 1,\n" +
                "          \"name\": \"青霉素\",\n" +
                "          \"code\": \"ALG001\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"diagnoseData\": [\n" +
                "        {\n" +
                "          \"type\": 2,\n" +
                "          \"name\": \"高血压\",\n" +
                "          \"code\": \"I10\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"prescriptionData\": [\n" +
                "      {\n" +
                "        \"id\": \"RX001\",\n" +
                "        \"reason\": \"常规治疗\",\n" +
                "        \"isCurrent\": 1,\n" +
                "        \"presType\": \"L\",\n" +
                "        \"presTime\": \"2025-07-20 10:30:00\",\n" +
                "        \"prescriptionType\": 1,\n" +
                "        \"presSm\": \"每日一次\",\n" +
                "        \"medicineData\": [\n" +
                "          {\n" +
                "            \"name\": \"硝苯地平片\",\n" +
                "            \"hisCode\": \"M001\",\n" +
                "            \"spec\": \"10mg/片\",\n" +
                "            \"group\": \"1\",\n" +
                "            \"reason\": \"降压\",\n" +
                "            \"doseUnit\": \"mg\",\n" +
                "            \"dose\": 10,\n" +
                "            \"freq\": \"QD\",\n" +
                "            \"administer\": \"PO\",\n" +
                "            \"beginTime\": \"2025-07-20 10:30:00\",\n" +
                "            \"endTime\": \"2025-07-27 10:30:00\",\n" +
                "            \"days\": 7,\n" +
                "            \"money\": 30.5,\n" +
                "            \"yysm\": \"每日一次\",\n" +
                "            \"qydd\": \"院内取药\"\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }

}
