import request from '@/utils/request'

// 根据关键字查询药品
export function searchDrugs(keyword) {
  return request({
    url: '/rms/drugknowledge/search',
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

// 根据sdaId查询儿童用药规则库信息
export function getChildDrugRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/childRules/' + sdaId,
    method: 'get'
  })
}

// 新增儿童用药规则库
export function addChildDrugRule(data) {
  return request({
    url: '/rms/drugknowledge/childRules',
    method: 'post',
    data: data
  })
}

// 修改儿童用药规则库
export function updateChildDrugRule(data) {
  return request({
    url: '/rms/drugknowledge/childRules',
    method: 'put',
    data: data
  })
}

// 删除儿童用药规则库
export function deleteChildDrugRule(id) {
  return request({
    url: '/rms/drugknowledge/childRules/' + id,
    method: 'delete'
  })
}

// 批量删除儿童用药规则库
export function deleteChildDrugRules(ids) {
  return request({
    url: '/rms/drugknowledge/childRules',
    method: 'delete',
    data: ids
  })
}

// 查询年龄代码列表
export function getAgeList() {
  return request({
    url: '/rms/drugknowledge/ageList',
    method: 'get'
  })
}

// 根据sdaId查询老年人用药规则库信息
export function getElderDrugRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/elderRules/' + sdaId,
    method: 'get'
  })
}

// 新增老年人用药规则库
export function addElderDrugRule(data) {
  return request({
    url: '/rms/drugknowledge/elderRules',
    method: 'post',
    data: data
  })
}

// 修改老年人用药规则库
export function updateElderDrugRule(data) {
  return request({
    url: '/rms/drugknowledge/elderRules',
    method: 'put',
    data: data
  })
}

// 删除老年人用药规则库
export function deleteElderDrugRule(id) {
  return request({
    url: '/rms/drugknowledge/elderRules/' + id,
    method: 'delete'
  })
}

// 批量删除老年人用药规则库
export function deleteElderDrugRules(ids) {
  return request({
    url: '/rms/drugknowledge/elderRules',
    method: 'delete',
    data: ids
  })
}

// 根据sdaId查询孕妇用药规则库信息
export function getPregnancyDrugRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/pregnancyRules/' + sdaId,
    method: 'get'
  })
}

// 新增孕妇用药规则库
export function addPregnancyDrugRule(data) {
  return request({
    url: '/rms/drugknowledge/pregnancyRules',
    method: 'post',
    data: data
  })
}

// 修改孕妇用药规则库
export function updatePregnancyDrugRule(data) {
  return request({
    url: '/rms/drugknowledge/pregnancyRules',
    method: 'put',
    data: data
  })
}

// 删除孕妇用药规则库
export function deletePregnancyDrugRule(id) {
  return request({
    url: '/rms/drugknowledge/pregnancyRules/' + id,
    method: 'delete'
  })
}

// 批量删除孕妇用药规则库
export function deletePregnancyDrugRules(ids) {
  return request({
    url: '/rms/drugknowledge/pregnancyRules',
    method: 'delete',
    data: ids
  })
}

// 获取所有给药途径基础数据
export function getAllRouteTypes() {
  return request({
    url: '/rms/drugknowledge/routeTypes',
    method: 'get'
  })
}

// 根据sdaId查询药品的给药途径信息
export function getRouteDrugRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/routeRules/' + sdaId,
    method: 'get'
  })
}

// 为药品添加给药途径
export function addRouteDrugRules(sdaId, routeCodes) {
  return request({
    url: '/rms/drugknowledge/routeRules',
    method: 'post',
    params: { sdaId: sdaId },
    data: routeCodes
  })
}

// 删除药品的给药途径
export function deleteRouteDrugRules(ids) {
  return request({
    url: '/rms/drugknowledge/routeRules',
    method: 'delete',
    data: ids
  })
}

// 根据drugCode查询药品标识信息
export function getDrugIdentity(drugCode) {
  return request({
    url: '/rms/drugknowledge/identity/' + drugCode,
    method: 'get'
  })
}

// 更新药品标识信息
export function updateDrugIdentity(data) {
  return request({
    url: '/rms/drugknowledge/identity',
    method: 'put',
    data: data
  })
}

// 获取给药途径基础数据
export function getRouteBaseList() {
  return request({
    url: '/rms/drugknowledge/routeBaseList',
    method: 'get'
  })
}

// 根据sdaId查询常规用量规则库信息
export function getDoseRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/doseRules/' + sdaId,
    method: 'get'
  })
}

// 新增常规用量规则库
export function addDoseRule(data) {
  return request({
    url: '/rms/drugknowledge/doseRules',
    method: 'post',
    data: data
  })
}

// 修改常规用量规则库
export function updateDoseRule(data) {
  return request({
    url: '/rms/drugknowledge/doseRules',
    method: 'put',
    data: data
  })
}

// 删除常规用量规则库
export function deleteDoseRule(id) {
  return request({
    url: '/rms/drugknowledge/doseRules/' + id,
    method: 'delete'
  })
}

// 批量删除常规用量规则库
export function deleteDoseRules(ids) {
  return request({
    url: '/rms/drugknowledge/doseRules',
    method: 'delete',
    data: ids
  })
}

// ========== 配伍禁忌相关API ==========

// 根据sdaId查询配伍禁忌信息（西药）
export function getIncompatibleRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/incompatibleRules/' + sdaId,
    method: 'get'
  })
}

// 新增配伍禁忌规则（西药）
export function addIncompatibleRule(data) {
  return request({
    url: '/rms/drugknowledge/incompatibleRules',
    method: 'post',
    data: data
  })
}

// 修改配伍禁忌规则（西药）
export function updateIncompatibleRule(data) {
  return request({
    url: '/rms/drugknowledge/incompatibleRules',
    method: 'put',
    data: data
  })
}

// 删除配伍禁忌规则（西药）
export function deleteIncompatibleRule(id) {
  return request({
    url: '/rms/drugknowledge/incompatibleRules/' + id,
    method: 'delete'
  })
}

// 批量删除配伍禁忌规则（西药）
export function deleteIncompatibleRules(ids) {
  return request({
    url: '/rms/drugknowledge/incompatibleRules',
    method: 'delete',
    data: ids
  })
}

// 根据sdaId查询配伍禁忌信息（中药）
export function getIncompatibleRulesZy(sdaId) {
  return request({
    url: '/rms/drugknowledge/incompatibleRulesZy/' + sdaId,
    method: 'get'
  })
}

// 新增配伍禁忌规则（中药）
export function addIncompatibleRuleZy(data) {
  return request({
    url: '/rms/drugknowledge/incompatibleRulesZy',
    method: 'post',
    data: data
  })
}

// 修改配伍禁忌规则（中药）
export function updateIncompatibleRuleZy(data) {
  return request({
    url: '/rms/drugknowledge/incompatibleRulesZy',
    method: 'put',
    data: data
  })
}

// 删除配伍禁忌规则（中药）
export function deleteIncompatibleRuleZy(id) {
  return request({
    url: '/rms/drugknowledge/incompatibleRulesZy/' + id,
    method: 'delete'
  })
}

// 批量删除配伍禁忌规则（中药）
export function deleteIncompatibleRulesZy(ids) {
  return request({
    url: '/rms/drugknowledge/incompatibleRulesZy',
    method: 'delete',
    data: ids
  })
}

// ========== 药物与诊断相关API ==========

// 根据sdaId查询药物与诊断信息
export function getDiagnosisRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/diagnosisRules/' + sdaId,
    method: 'get'
  })
}

// 新增药物与诊断信息
export function addDiagnosisRule(data) {
  return request({
    url: '/rms/drugknowledge/diagnosisRules',
    method: 'post',
    data: data
  })
}

// 删除药物与诊断信息
export function deleteDiagnosisRule(id) {
  return request({
    url: '/rms/drugknowledge/diagnosisRules/' + id,
    method: 'delete'
  })
}

// 批量删除药物与诊断信息
export function deleteDiagnosisRules(ids) {
  return request({
    url: '/rms/drugknowledge/diagnosisRules',
    method: 'delete',
    data: ids
  })
}

// 根据关键字搜索ICD诊断信息
export function searchDiagnosis(keyword) {
  return request({
    url: '/rms/drugknowledge/searchDiagnosis',
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

// ========== 药物禁忌症相关API ==========

// 根据sdaId查询药物禁忌症信息
export function getContraindicationRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/contraindicationRules/' + sdaId,
    method: 'get'
  })
}

// 新增药物禁忌症信息
export function addContraindicationRule(data) {
  return request({
    url: '/rms/drugknowledge/contraindicationRules',
    method: 'post',
    data: data
  })
}

// 删除药物禁忌症信息
export function deleteContraindicationRule(id) {
  return request({
    url: '/rms/drugknowledge/contraindicationRules/' + id,
    method: 'delete'
  })
}

// 批量删除药物禁忌症信息
export function deleteContraindicationRules(ids) {
  return request({
    url: '/rms/drugknowledge/contraindicationRules',
    method: 'delete',
    data: ids
  })
}

// ========== 过敏知识库相关API ==========

// 获取所有过敏基础数据
export function getAllergyBaseList() {
  return request({
    url: '/rms/drugknowledge/allergyBaseList',
    method: 'get'
  })
}

// 根据sdaId查询药品过敏信息
export function getAllergyRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/allergyRules/' + sdaId,
    method: 'get'
  })
}

// 新增药品过敏信息
export function addAllergyRule(data) {
  return request({
    url: '/rms/drugknowledge/allergyRules',
    method: 'post',
    data: data
  })
}

// 修改药品过敏信息
export function updateAllergyRule(data) {
  return request({
    url: '/rms/drugknowledge/allergyRules',
    method: 'put',
    data: data
  })
}

// 删除药品过敏信息
export function deleteAllergyRule(id) {
  return request({
    url: '/rms/drugknowledge/allergyRules/' + id,
    method: 'delete'
  })
}

// 批量删除药品过敏信息
export function deleteAllergyRules(ids) {
  return request({
    url: '/rms/drugknowledge/allergyRules',
    method: 'delete',
    data: ids
  })
}

// 根据关键字搜索过敏信息
export function searchAllergy(keyword) {
  return request({
    url: '/rms/drugknowledge/searchAllergy',
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

// 根据关键字搜索过敏基础数据
export function searchAllergyBase(keyword) {
  return request({
    url: '/rms/drugknowledge/searchAllergyBase',
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

