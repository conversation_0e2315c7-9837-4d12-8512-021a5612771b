package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 药品说明书对象 rms_t_sda
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
public class RmsTSda extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long ID;

    /** 药品名称 */
    @Excel(name = "药品名称")
    private String Ym;

    /** 拼音缩写 */
    @Excel(name = "拼音缩写")
    private String Sp;

    /** 汉语拼音 */
    @Excel(name = "汉语拼音")
    private String hypy;

    /** 单位 */
    @Excel(name = "单位")
    private String UnitRem;

    /** 通用名称 */
    @Excel(name = "通用名称")
    private String tymc;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String yymc;

    /** 化学名称 */
    @Excel(name = "化学名称")
    private String hxmc;

    /** 主要成分 */
    @Excel(name = "主要成分")
    private String zycf;

    /** 分子式 */
    @Excel(name = "分子式")
    private String fzs;

    /** 分子量 */
    @Excel(name = "分子量")
    private String fzl;

    /** 性状 */
    @Excel(name = "性状")
    private String xz;

    /** 药理毒理 */
    @Excel(name = "药理毒理")
    private String yldl;

    /** 药代动力学 */
    @Excel(name = "药代动力学")
    private String yddlx;

    /** 适应症 */
    @Excel(name = "适应症")
    private String syz;

    /** 用法用量 */
    @Excel(name = "用法用量")
    private String yfyl;

    /** 不良反应 */
    @Excel(name = "不良反应")
    private String blfy;

    /** 禁忌症 */
    @Excel(name = "禁忌症")
    private String jjz;

    /** 注意事项 */
    @Excel(name = "注意事项")
    private String zysx;

    /** 孕妇用药 */
    @Excel(name = "孕妇用药")
    private String yfyy;

    /** 儿童用药 */
    @Excel(name = "儿童用药")
    private String etyy;

    /** 老年人用药 */
    @Excel(name = "老年人用药")
    private String lnryy;

    /** 相互作用 */
    @Excel(name = "相互作用")
    private String xhzy;

    /** 药物过量 */
    @Excel(name = "药物过量")
    private String ywgl;

    /** 药物规格 */
    @Excel(name = "药物规格")
    private String ywgg;

    /** 储存条件 */
    @Excel(name = "储存条件")
    private String cctj;

    /** 要点提示 */
    @Excel(name = "要点提示")
    private String ydts;

    /** 用量规则代码？ */
    @Excel(name = "用量规则代码？")
    private Long ylgzd;

    /** 发药规则代码？ */
    @Excel(name = "发药规则代码？")
    private Long fygzd;

    /** 用药规则代码？ */
    @Excel(name = "用药规则代码？")
    private Long ydgzd;

    /** 禁忌标识2？ */
    @Excel(name = "禁忌标识2？")
    private Long jxbs2;

    /** 注意事项标识？ */
    @Excel(name = "注意事项标识？")
    private Long zxybs;

    /** 来源？ */
    @Excel(name = "来源？")
    private String ly;

    /** 来源标识？ */
    @Excel(name = "来源标识？")
    private String lyBs;

    /** 生产企业 */
    @Excel(name = "生产企业")
    private String scqy;

    /** 标识？ */
    @Excel(name = "标识？")
    private String bs;

    /** 批准文号 */
    @Excel(name = "批准文号")
    private String pzwh;

    /** 产品英文名？ */
    @Excel(name = "产品英文名？")
    private String cpym;

    /** 产品商品名？ */
    @Excel(name = "产品商品名？")
    private String cpsp;

    /** 老年人标识？ */
    @Excel(name = "老年人标识？")
    private String lnrBs;

    /** 孕妇标识？ */
    @Excel(name = "孕妇标识？")
    private String yfBs;

    /** 孕妇期授乳标识？ */
    @Excel(name = "孕妇期授乳标识？")
    private String yfqsyBs;

    /** 孕妇授乳哺乳标识？ */
    @Excel(name = "孕妇授乳哺乳标识？")
    private String yfsyhBs;

    /** 哺乳标识？ */
    @Excel(name = "哺乳标识？")
    private String brBs;

    /** 规格标识？ */
    @Excel(name = "规格标识？")
    private String ggBs;

    /** 肾功能标识？ */
    @Excel(name = "肾功能标识？")
    private String sgBs;

    /** 肝功能标识？ */
    @Excel(name = "肝功能标识？")
    private String hggBs;

    /** 肝肾功能标识？ */
    @Excel(name = "肝肾功能标识？")
    private String hsgBs;

    /** 最小年龄 */
    @Excel(name = "最小年龄")
    private Long agemin;

    /** 最大年龄 */
    @Excel(name = "最大年龄")
    private Long agemax;

    /** 给药途径标识？ */
    @Excel(name = "给药途径标识？")
    private String gytjBs;

    /** 给药途径标识1？ */
    @Excel(name = "给药途径标识1？")
    private String gytjBs1;

    /** 过敏代码？ */
    @Excel(name = "过敏代码？")
    private String gmdm;

    /** 数据字段1？ */
    @Excel(name = "数据字段1？")
    private String dc1;

    /** 数据关系1？ */
    @Excel(name = "数据关系1？")
    private Long dr1;

    /** 类型码 */
    @Excel(name = "类型码")
    private String mid;

    /** 毒性？ */
    @Excel(name = "毒性？")
    private String dx;

    public void setID(Long ID)
    {
        this.ID = ID;
    }

    public Long getID()
    {
        return ID;
    }

    public void setYm(String Ym)
    {
        this.Ym = Ym;
    }

    public String getYm()
    {
        return Ym;
    }

    public void setSp(String Sp)
    {
        this.Sp = Sp;
    }

    public String getSp()
    {
        return Sp;
    }

    public void setHypy(String hypy)
    {
        this.hypy = hypy;
    }

    public String getHypy()
    {
        return hypy;
    }

    public void setUnitRem(String UnitRem)
    {
        this.UnitRem = UnitRem;
    }

    public String getUnitRem()
    {
        return UnitRem;
    }

    public void setTymc(String tymc)
    {
        this.tymc = tymc;
    }

    public String getTymc()
    {
        return tymc;
    }

    public void setYymc(String yymc)
    {
        this.yymc = yymc;
    }

    public String getYymc()
    {
        return yymc;
    }

    public void setHxmc(String hxmc)
    {
        this.hxmc = hxmc;
    }

    public String getHxmc()
    {
        return hxmc;
    }

    public void setZycf(String zycf)
    {
        this.zycf = zycf;
    }

    public String getZycf()
    {
        return zycf;
    }

    public void setFzs(String fzs)
    {
        this.fzs = fzs;
    }

    public String getFzs()
    {
        return fzs;
    }

    public void setFzl(String fzl)
    {
        this.fzl = fzl;
    }

    public String getFzl()
    {
        return fzl;
    }

    public void setXz(String xz)
    {
        this.xz = xz;
    }

    public String getXz()
    {
        return xz;
    }

    public void setYldl(String yldl)
    {
        this.yldl = yldl;
    }

    public String getYldl()
    {
        return yldl;
    }

    public void setYddlx(String yddlx)
    {
        this.yddlx = yddlx;
    }

    public String getYddlx()
    {
        return yddlx;
    }

    public void setSyz(String syz)
    {
        this.syz = syz;
    }

    public String getSyz()
    {
        return syz;
    }

    public void setYfyl(String yfyl)
    {
        this.yfyl = yfyl;
    }

    public String getYfyl()
    {
        return yfyl;
    }

    public void setBlfy(String blfy)
    {
        this.blfy = blfy;
    }

    public String getBlfy()
    {
        return blfy;
    }

    public void setJjz(String jjz)
    {
        this.jjz = jjz;
    }

    public String getJjz()
    {
        return jjz;
    }

    public void setZysx(String zysx)
    {
        this.zysx = zysx;
    }

    public String getZysx()
    {
        return zysx;
    }

    public void setYfyy(String yfyy)
    {
        this.yfyy = yfyy;
    }

    public String getYfyy()
    {
        return yfyy;
    }

    public void setEtyy(String etyy)
    {
        this.etyy = etyy;
    }

    public String getEtyy()
    {
        return etyy;
    }

    public void setLnryy(String lnryy)
    {
        this.lnryy = lnryy;
    }

    public String getLnryy()
    {
        return lnryy;
    }

    public void setXhzy(String xhzy)
    {
        this.xhzy = xhzy;
    }

    public String getXhzy()
    {
        return xhzy;
    }

    public void setYwgl(String ywgl)
    {
        this.ywgl = ywgl;
    }

    public String getYwgl()
    {
        return ywgl;
    }

    public void setYwgg(String ywgg)
    {
        this.ywgg = ywgg;
    }

    public String getYwgg()
    {
        return ywgg;
    }

    public void setCctj(String cctj)
    {
        this.cctj = cctj;
    }

    public String getCctj()
    {
        return cctj;
    }

    public void setYdts(String ydts)
    {
        this.ydts = ydts;
    }

    public String getYdts()
    {
        return ydts;
    }

    public void setYlgzd(Long ylgzd)
    {
        this.ylgzd = ylgzd;
    }

    public Long getYlgzd()
    {
        return ylgzd;
    }

    public void setFygzd(Long fygzd)
    {
        this.fygzd = fygzd;
    }

    public Long getFygzd()
    {
        return fygzd;
    }

    public void setYdgzd(Long ydgzd)
    {
        this.ydgzd = ydgzd;
    }

    public Long getYdgzd()
    {
        return ydgzd;
    }

    public void setJxbs2(Long jxbs2)
    {
        this.jxbs2 = jxbs2;
    }

    public Long getJxbs2()
    {
        return jxbs2;
    }

    public void setZxybs(Long zxybs)
    {
        this.zxybs = zxybs;
    }

    public Long getZxybs()
    {
        return zxybs;
    }

    public void setLy(String ly)
    {
        this.ly = ly;
    }

    public String getLy()
    {
        return ly;
    }

    public void setLyBs(String lyBs)
    {
        this.lyBs = lyBs;
    }

    public String getLyBs()
    {
        return lyBs;
    }

    public void setScqy(String scqy)
    {
        this.scqy = scqy;
    }

    public String getScqy()
    {
        return scqy;
    }

    public void setBs(String bs)
    {
        this.bs = bs;
    }

    public String getBs()
    {
        return bs;
    }

    public void setPzwh(String pzwh)
    {
        this.pzwh = pzwh;
    }

    public String getPzwh()
    {
        return pzwh;
    }

    public void setCpym(String cpym)
    {
        this.cpym = cpym;
    }

    public String getCpym()
    {
        return cpym;
    }

    public void setCpsp(String cpsp)
    {
        this.cpsp = cpsp;
    }

    public String getCpsp()
    {
        return cpsp;
    }

    public void setLnrBs(String lnrBs)
    {
        this.lnrBs = lnrBs;
    }

    public String getLnrBs()
    {
        return lnrBs;
    }

    public void setYfBs(String yfBs)
    {
        this.yfBs = yfBs;
    }

    public String getYfBs()
    {
        return yfBs;
    }

    public void setYfqsyBs(String yfqsyBs)
    {
        this.yfqsyBs = yfqsyBs;
    }

    public String getYfqsyBs()
    {
        return yfqsyBs;
    }

    public void setYfsyhBs(String yfsyhBs)
    {
        this.yfsyhBs = yfsyhBs;
    }

    public String getYfsyhBs()
    {
        return yfsyhBs;
    }

    public void setBrBs(String brBs)
    {
        this.brBs = brBs;
    }

    public String getBrBs()
    {
        return brBs;
    }

    public void setGgBs(String ggBs)
    {
        this.ggBs = ggBs;
    }

    public String getGgBs()
    {
        return ggBs;
    }

    public void setSgBs(String sgBs)
    {
        this.sgBs = sgBs;
    }

    public String getSgBs()
    {
        return sgBs;
    }

    public void setHggBs(String hggBs)
    {
        this.hggBs = hggBs;
    }

    public String getHggBs()
    {
        return hggBs;
    }

    public void setHsgBs(String hsgBs)
    {
        this.hsgBs = hsgBs;
    }

    public String getHsgBs()
    {
        return hsgBs;
    }

    public void setAgemin(Long agemin)
    {
        this.agemin = agemin;
    }

    public Long getAgemin()
    {
        return agemin;
    }

    public void setAgemax(Long agemax)
    {
        this.agemax = agemax;
    }

    public Long getAgemax()
    {
        return agemax;
    }

    public void setGytjBs(String gytjBs)
    {
        this.gytjBs = gytjBs;
    }

    public String getGytjBs()
    {
        return gytjBs;
    }

    public void setGytjBs1(String gytjBs1)
    {
        this.gytjBs1 = gytjBs1;
    }

    public String getGytjBs1()
    {
        return gytjBs1;
    }

    public void setGmdm(String gmdm)
    {
        this.gmdm = gmdm;
    }

    public String getGmdm()
    {
        return gmdm;
    }

    public void setDc1(String dc1)
    {
        this.dc1 = dc1;
    }

    public String getDc1()
    {
        return dc1;
    }

    public void setDr1(Long dr1)
    {
        this.dr1 = dr1;
    }

    public Long getDr1()
    {
        return dr1;
    }

    public void setMid(String mid)
    {
        this.mid = mid;
    }

    public String getMid()
    {
        return mid;
    }

    public void setDx(String dx)
    {
        this.dx = dx;
    }

    public String getDx()
    {
        return dx;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ID", getID())
            .append("Ym", getYm())
            .append("Sp", getSp())
            .append("hypy", getHypy())
            .append("UnitRem", getUnitRem())
            .append("tymc", getTymc())
            .append("yymc", getYymc())
            .append("hxmc", getHxmc())
            .append("zycf", getZycf())
            .append("fzs", getFzs())
            .append("fzl", getFzl())
            .append("xz", getXz())
            .append("yldl", getYldl())
            .append("yddlx", getYddlx())
            .append("syz", getSyz())
            .append("yfyl", getYfyl())
            .append("blfy", getBlfy())
            .append("jjz", getJjz())
            .append("zysx", getZysx())
            .append("yfyy", getYfyy())
            .append("etyy", getEtyy())
            .append("lnryy", getLnryy())
            .append("xhzy", getXhzy())
            .append("ywgl", getYwgl())
            .append("ywgg", getYwgg())
            .append("cctj", getCctj())
            .append("ydts", getYdts())
            .append("ylgzd", getYlgzd())
            .append("fygzd", getFygzd())
            .append("ydgzd", getYdgzd())
            .append("jxbs2", getJxbs2())
            .append("zxybs", getZxybs())
            .append("ly", getLy())
            .append("lyBs", getLyBs())
            .append("scqy", getScqy())
            .append("bs", getBs())
            .append("pzwh", getPzwh())
            .append("cpym", getCpym())
            .append("cpsp", getCpsp())
            .append("lnrBs", getLnrBs())
            .append("yfBs", getYfBs())
            .append("yfqsyBs", getYfqsyBs())
            .append("yfsyhBs", getYfsyhBs())
            .append("brBs", getBrBs())
            .append("ggBs", getGgBs())
            .append("sgBs", getSgBs())
            .append("hggBs", getHggBs())
            .append("hsgBs", getHsgBs())
            .append("agemin", getAgemin())
            .append("agemax", getAgemax())
            .append("gytjBs", getGytjBs())
            .append("gytjBs1", getGytjBs1())
            .append("gmdm", getGmdm())
            .append("dc1", getDc1())
            .append("dr1", getDr1())
            .append("mid", getMid())
            .append("dx", getDx())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
