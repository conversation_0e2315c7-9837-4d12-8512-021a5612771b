package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 中药相互作用对象 rms_t_xhzy_edi_zy
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public class RmsTXhzyEdiZy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** A药物ID */
    @Excel(name = "A药物ID")
    private Long yaowua;

    /** B药物ID */
    @Excel(name = "B药物ID")
    private Long yaowub;

    /** 相互作用效果 */
    @Excel(name = "相互作用效果")
    private String effect;

    /** 作用机制 */
    @Excel(name = "作用机制")
    private String mechanism;

    /** 相关药物 */
    @Excel(name = "相关药物")
    private String relatedrug;

    /** 参考文献 */
    @Excel(name = "参考文献")
    private String reference;

    /** 重要性标识：0-严重；1-一般；2-其它 */
    @Excel(name = "重要性标识：0-严重；1-一般；2-其它")
    private String impBs;

    /** 标识（未用） */
    @Excel(name = "标识", readConverterExp = "未=用")
    private String jxBs;

    /** 建议 */
    @Excel(name = "建议")
    private String recommandations;

    /** 主要信息（未用） */
    @Excel(name = "主要信息", readConverterExp = "未=用")
    private String main;

    /** 标识（未用） */
    @Excel(name = "标识", readConverterExp = "未=用")
    private String sjbs;

    /** 关系（未用） */
    @Excel(name = "关系", readConverterExp = "未=用")
    private Long gx;

    /** 建议标识：pwjj-配伍禁忌；xhzy-相互作用 */
    @Excel(name = "建议标识：pwjj-配伍禁忌；xhzy-相互作用")
    private String sugflag;

    /** 类型（未用） */
    @Excel(name = "类型", readConverterExp = "未=用")
    private String type;

    /** 重要性（未用） */
    @Excel(name = "重要性", readConverterExp = "未=用")
    private Long significance;

    /** 起效时间（未用） */
    @Excel(name = "起效时间", readConverterExp = "未=用")
    private Long onset;

    /** 文档（未用） */
    @Excel(name = "文档", readConverterExp = "未=用")
    private Long documentation;

    /** 效果类型（未用） */
    @Excel(name = "效果类型", readConverterExp = "未=用")
    private Long effecttype;

    /** 结果等级：0-禁忌，1-问题，2-提示 */
    @Excel(name = "结果等级：0-禁忌，1-问题，2-提示")
    private String result;

    /** 是否屏蔽 */
    @Excel(name = "是否屏蔽")
    private String ispb;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setYaowua(Long yaowua)
    {
        this.yaowua = yaowua;
    }

    public Long getYaowua()
    {
        return yaowua;
    }

    public void setYaowub(Long yaowub)
    {
        this.yaowub = yaowub;
    }

    public Long getYaowub()
    {
        return yaowub;
    }

    public void setEffect(String effect)
    {
        this.effect = effect;
    }

    public String getEffect()
    {
        return effect;
    }

    public void setMechanism(String mechanism)
    {
        this.mechanism = mechanism;
    }

    public String getMechanism()
    {
        return mechanism;
    }

    public void setRelatedrug(String relatedrug)
    {
        this.relatedrug = relatedrug;
    }

    public String getRelatedrug()
    {
        return relatedrug;
    }

    public void setReference(String reference)
    {
        this.reference = reference;
    }

    public String getReference()
    {
        return reference;
    }

    public void setImpBs(String impBs)
    {
        this.impBs = impBs;
    }

    public String getImpBs()
    {
        return impBs;
    }

    public void setJxBs(String jxBs)
    {
        this.jxBs = jxBs;
    }

    public String getJxBs()
    {
        return jxBs;
    }

    public void setRecommandations(String recommandations)
    {
        this.recommandations = recommandations;
    }

    public String getRecommandations()
    {
        return recommandations;
    }

    public void setMain(String main)
    {
        this.main = main;
    }

    public String getMain()
    {
        return main;
    }

    public void setSjbs(String sjbs)
    {
        this.sjbs = sjbs;
    }

    public String getSjbs()
    {
        return sjbs;
    }

    public void setGx(Long gx)
    {
        this.gx = gx;
    }

    public Long getGx()
    {
        return gx;
    }

    public void setSugflag(String sugflag)
    {
        this.sugflag = sugflag;
    }

    public String getSugflag()
    {
        return sugflag;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }

    public void setSignificance(Long significance)
    {
        this.significance = significance;
    }

    public Long getSignificance()
    {
        return significance;
    }

    public void setOnset(Long onset)
    {
        this.onset = onset;
    }

    public Long getOnset()
    {
        return onset;
    }

    public void setDocumentation(Long documentation)
    {
        this.documentation = documentation;
    }

    public Long getDocumentation()
    {
        return documentation;
    }

    public void setEffecttype(Long effecttype)
    {
        this.effecttype = effecttype;
    }

    public Long getEffecttype()
    {
        return effecttype;
    }

    public void setResult(String result)
    {
        this.result = result;
    }

    public String getResult()
    {
        return result;
    }

    public void setIspb(String ispb)
    {
        this.ispb = ispb;
    }

    public String getIspb()
    {
        return ispb;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("yaowua", getYaowua())
            .append("yaowub", getYaowub())
            .append("effect", getEffect())
            .append("mechanism", getMechanism())
            .append("relatedrug", getRelatedrug())
            .append("reference", getReference())
            .append("impBs", getImpBs())
            .append("jxBs", getJxBs())
            .append("recommandations", getRecommandations())
            .append("main", getMain())
            .append("sjbs", getSjbs())
            .append("gx", getGx())
            .append("remark", getRemark())
            .append("sugflag", getSugflag())
            .append("type", getType())
            .append("significance", getSignificance())
            .append("onset", getOnset())
            .append("documentation", getDocumentation())
            .append("effecttype", getEffecttype())
            .append("result", getResult())
            .append("ispb", getIspb())
            .toString();
    }
}
