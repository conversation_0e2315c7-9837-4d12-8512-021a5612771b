package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaAgeMapper;
import com.rms.core.domain.RmsTSdaAge;
import com.rms.core.service.IRmsTSdaAgeService;

/**
 * 年龄代码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class RmsTSdaAgeServiceImpl implements IRmsTSdaAgeService
{
    @Autowired
    private RmsTSdaAgeMapper rmsTSdaAgeMapper;

    /**
     * 查询年龄代码
     *
     * @param id 年龄代码主键
     * @return 年龄代码
     */
    @Override
    public RmsTSdaAge selectRmsTSdaAgeById(Long id)
    {
        return rmsTSdaAgeMapper.selectRmsTSdaAgeById(id);
    }

    /**
     * 查询年龄代码列表
     *
     * @param rmsTSdaAge 年龄代码
     * @return 年龄代码
     */
    @Override
    public List<RmsTSdaAge> selectRmsTSdaAgeList(RmsTSdaAge rmsTSdaAge)
    {
        return rmsTSdaAgeMapper.selectRmsTSdaAgeList(rmsTSdaAge);
    }

    /**
     * 新增年龄代码
     *
     * @param rmsTSdaAge 年龄代码
     * @return 结果
     */
    @Override
    public int insertRmsTSdaAge(RmsTSdaAge rmsTSdaAge)
    {
        return rmsTSdaAgeMapper.insertRmsTSdaAge(rmsTSdaAge);
    }

    /**
     * 修改年龄代码
     *
     * @param rmsTSdaAge 年龄代码
     * @return 结果
     */
    @Override
    public int updateRmsTSdaAge(RmsTSdaAge rmsTSdaAge)
    {
        return rmsTSdaAgeMapper.updateRmsTSdaAge(rmsTSdaAge);
    }

    /**
     * 批量删除年龄代码
     *
     * @param ids 需要删除的年龄代码主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaAgeByIds(Long[] ids)
    {
        return rmsTSdaAgeMapper.deleteRmsTSdaAgeByIds(ids);
    }

    /**
     * 删除年龄代码信息
     *
     * @param id 年龄代码主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaAgeById(Long id)
    {
        return rmsTSdaAgeMapper.deleteRmsTSdaAgeById(id);
    }
}
