import request from '@/utils/request'

// 查询处方分析结果列表
export function listTpresfx(query) {
  return request({
    url: '/rms/tpresfx/list',
    method: 'get',
    params: query
  })
}

// 查询处方分析结果详细
export function getTpresfx(id) {
  return request({
    url: '/rms/tpresfx/' + id,
    method: 'get'
  })
}

// 新增处方分析结果
export function addTpresfx(data) {
  return request({
    url: '/rms/tpresfx',
    method: 'post',
    data: data
  })
}

// 修改处方分析结果
export function updateTpresfx(data) {
  return request({
    url: '/rms/tpresfx',
    method: 'put',
    data: data
  })
}

// 删除处方分析结果
export function delTpresfx(id) {
  return request({
    url: '/rms/tpresfx/' + id,
    method: 'delete'
  })
}
