package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 诊断基础信息表对象 rms_t_icd10_base
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public class RmsTIcd10Base extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 序号ID */
    private Long id;

    /** 诊断编码 */
    @Excel(name = "诊断编码")
    private String code;

    /** ICD-10编码 */
    @Excel(name = "ICD-10编码")
    private String icdCode;

    /** 分级码或父级码 */
    @Excel(name = "分级码或父级码")
    private String fjm;

    /** ICD-10诊断名称 */
    @Excel(name = "ICD-10诊断名称")
    private String icdName;

    /** 拼音缩写 */
    @Excel(name = "拼音缩写")
    private String sp;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public void setIcdCode(String icdCode)
    {
        this.icdCode = icdCode;
    }

    public String getIcdCode()
    {
        return icdCode;
    }

    public void setFjm(String fjm)
    {
        this.fjm = fjm;
    }

    public String getFjm()
    {
        return fjm;
    }

    public void setIcdName(String icdName)
    {
        this.icdName = icdName;
    }

    public String getIcdName()
    {
        return icdName;
    }

    public void setSp(String sp)
    {
        this.sp = sp;
    }

    public String getSp()
    {
        return sp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("code", getCode())
            .append("icdCode", getIcdCode())
            .append("fjm", getFjm())
            .append("icdName", getIcdName())
            .append("sp", getSp())
            .append("remark", getRemark())
            .toString();
    }
}
