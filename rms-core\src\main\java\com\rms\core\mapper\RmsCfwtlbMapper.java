package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsCfwtlb;

/**
 * 处方问题类别Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface RmsCfwtlbMapper 
{
    /**
     * 查询处方问题类别
     * 
     * @param cfwtbh 处方问题类别主键
     * @return 处方问题类别
     */
    public RmsCfwtlb selectRmsCfwtlbByCfwtbh(String cfwtbh);

    /**
     * 查询处方问题类别列表
     * 
     * @param rmsCfwtlb 处方问题类别
     * @return 处方问题类别集合
     */
    public List<RmsCfwtlb> selectRmsCfwtlbList(RmsCfwtlb rmsCfwtlb);

    /**
     * 新增处方问题类别
     * 
     * @param rmsCfwtlb 处方问题类别
     * @return 结果
     */
    public int insertRmsCfwtlb(RmsCfwtlb rmsCfwtlb);

    /**
     * 修改处方问题类别
     * 
     * @param rmsCfwtlb 处方问题类别
     * @return 结果
     */
    public int updateRmsCfwtlb(RmsCfwtlb rmsCfwtlb);

    /**
     * 删除处方问题类别
     * 
     * @param cfwtbh 处方问题类别主键
     * @return 结果
     */
    public int deleteRmsCfwtlbByCfwtbh(String cfwtbh);

    /**
     * 批量删除处方问题类别
     * 
     * @param cfwtbhs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsCfwtlbByCfwtbhs(String[] cfwtbhs);
}
