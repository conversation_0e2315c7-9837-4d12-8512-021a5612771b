package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsItfHosDrug;
import com.rms.core.service.IRmsItfHosDrugService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 医院药品信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/rms/itfhosdrug")
public class RmsItfHosDrugController extends BaseController
{
    @Autowired
    private IRmsItfHosDrugService rmsItfHosDrugService;

    /**
     * 查询医院药品信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:itfhosdrug:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsItfHosDrug rmsItfHosDrug)
    {
        startPage();
        List<RmsItfHosDrug> list = rmsItfHosDrugService.selectRmsItfHosDrugList(rmsItfHosDrug);
        return getDataTable(list);
    }

    /**
     * 导出医院药品信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:itfhosdrug:export')")
    @Log(title = "医院药品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsItfHosDrug rmsItfHosDrug)
    {
        List<RmsItfHosDrug> list = rmsItfHosDrugService.selectRmsItfHosDrugList(rmsItfHosDrug);
        ExcelUtil<RmsItfHosDrug> util = new ExcelUtil<RmsItfHosDrug>(RmsItfHosDrug.class);
        util.exportExcel(response, list, "医院药品信息数据");
    }

    /**
     * 获取医院药品信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:itfhosdrug:query')")
    @GetMapping(value = "/{drugCode}")
    public AjaxResult getInfo(@PathVariable("drugCode") String drugCode)
    {
        return success(rmsItfHosDrugService.selectRmsItfHosDrugByDrugCode(drugCode));
    }

    /**
     * 新增医院药品信息
     */
    @PreAuthorize("@ss.hasPermi('rms:itfhosdrug:add')")
    @Log(title = "医院药品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsItfHosDrug rmsItfHosDrug)
    {
        return toAjax(rmsItfHosDrugService.insertRmsItfHosDrug(rmsItfHosDrug));
    }

    /**
     * 修改医院药品信息
     */
    @PreAuthorize("@ss.hasPermi('rms:itfhosdrug:edit')")
    @Log(title = "医院药品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsItfHosDrug rmsItfHosDrug)
    {
        return toAjax(rmsItfHosDrugService.updateRmsItfHosDrug(rmsItfHosDrug));
    }

    /**
     * 删除医院药品信息
     */
    @PreAuthorize("@ss.hasPermi('rms:itfhosdrug:remove')")
    @Log(title = "医院药品信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{drugCodes}")
    public AjaxResult remove(@PathVariable String[] drugCodes)
    {
        return toAjax(rmsItfHosDrugService.deleteRmsItfHosDrugByDrugCodes(drugCodes));
    }
}
