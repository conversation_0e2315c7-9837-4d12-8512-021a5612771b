package com.rms;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class }, scanBasePackages = { "com.rms.*", "com.rms.*" })
@MapperScan(basePackages = {"com.rms.*.mapper"})
public class RMSApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(RMSApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  合理用药系统启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
