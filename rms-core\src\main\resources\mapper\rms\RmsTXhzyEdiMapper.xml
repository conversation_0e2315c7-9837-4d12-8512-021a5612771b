<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTXhzyEdiMapper">
    
    <resultMap type="RmsTXhzyEdi" id="RmsTXhzyEdiResult">
        <result property="id"    column="id"    />
        <result property="yaowua"    column="yaowua"    />
        <result property="yaowub"    column="yaowub"    />
        <result property="effect"    column="effect"    />
        <result property="mechanism"    column="mechanism"    />
        <result property="relatedrug"    column="relatedrug"    />
        <result property="reference"    column="reference"    />
        <result property="impBs"    column="imp_bs"    />
        <result property="jxBs"    column="jx_bs"    />
        <result property="recommandations"    column="recommandations"    />
        <result property="main"    column="main"    />
        <result property="sjbs"    column="sjbs"    />
        <result property="gx"    column="gx"    />
        <result property="remark"    column="remark"    />
        <result property="sugflag"    column="sugflag"    />
        <result property="type"    column="type"    />
        <result property="significance"    column="significance"    />
        <result property="onset"    column="onset"    />
        <result property="documentation"    column="documentation"    />
        <result property="effecttype"    column="effecttype"    />
        <result property="result"    column="result"    />
        <result property="ispb"    column="ispb"    />
        <result property="addUser"    column="add_user"    />
        <result property="addTime"    column="add_time"    />
        <result property="editUser"    column="edit_user"    />
        <result property="editTime"    column="edit_time"    />
    </resultMap>

    <sql id="selectRmsTXhzyEdiVo">
        select id, yaowua, yaowub, effect, mechanism, relatedrug, reference, imp_bs, jx_bs, recommandations, main, sjbs, gx, remark, sugflag, type, significance, onset, documentation, effecttype, result, ispb, add_user, add_time, edit_user, edit_time from rms_t_xhzy_edi
    </sql>

    <select id="selectRmsTXhzyEdiList" parameterType="RmsTXhzyEdi" resultMap="RmsTXhzyEdiResult">
        <include refid="selectRmsTXhzyEdiVo"/>
        <where>  
            <if test="yaowua != null "> and yaowua = #{yaowua}</if>
            <if test="yaowub != null "> and yaowub = #{yaowub}</if>
            <if test="effect != null  and effect != ''"> and effect = #{effect}</if>
            <if test="mechanism != null  and mechanism != ''"> and mechanism = #{mechanism}</if>
            <if test="relatedrug != null  and relatedrug != ''"> and relatedrug = #{relatedrug}</if>
            <if test="reference != null  and reference != ''"> and reference = #{reference}</if>
            <if test="impBs != null  and impBs != ''"> and imp_bs = #{impBs}</if>
            <if test="jxBs != null  and jxBs != ''"> and jx_bs = #{jxBs}</if>
            <if test="recommandations != null  and recommandations != ''"> and recommandations = #{recommandations}</if>
            <if test="main != null  and main != ''"> and main = #{main}</if>
            <if test="sjbs != null  and sjbs != ''"> and sjbs = #{sjbs}</if>
            <if test="gx != null "> and gx = #{gx}</if>
            <if test="sugflag != null  and sugflag != ''"> and sugflag = #{sugflag}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="significance != null "> and significance = #{significance}</if>
            <if test="onset != null "> and onset = #{onset}</if>
            <if test="documentation != null "> and documentation = #{documentation}</if>
            <if test="effecttype != null "> and effecttype = #{effecttype}</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="ispb != null  and ispb != ''"> and ispb = #{ispb}</if>
            <if test="addUser != null  and addUser != ''"> and add_user = #{addUser}</if>
            <if test="addTime != null  and addTime != ''"> and add_time = #{addTime}</if>
            <if test="editUser != null  and editUser != ''"> and edit_user = #{editUser}</if>
            <if test="editTime != null  and editTime != ''"> and edit_time = #{editTime}</if>
        </where>
    </select>
    
    <select id="selectRmsTXhzyEdiById" parameterType="Long" resultMap="RmsTXhzyEdiResult">
        <include refid="selectRmsTXhzyEdiVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTXhzyEdi" parameterType="RmsTXhzyEdi" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_xhzy_edi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="yaowua != null">yaowua,</if>
            <if test="yaowub != null">yaowub,</if>
            <if test="effect != null">effect,</if>
            <if test="mechanism != null">mechanism,</if>
            <if test="relatedrug != null">relatedrug,</if>
            <if test="reference != null">reference,</if>
            <if test="impBs != null">imp_bs,</if>
            <if test="jxBs != null">jx_bs,</if>
            <if test="recommandations != null">recommandations,</if>
            <if test="main != null">main,</if>
            <if test="sjbs != null">sjbs,</if>
            <if test="gx != null">gx,</if>
            <if test="remark != null">remark,</if>
            <if test="sugflag != null">sugflag,</if>
            <if test="type != null">type,</if>
            <if test="significance != null">significance,</if>
            <if test="onset != null">onset,</if>
            <if test="documentation != null">documentation,</if>
            <if test="effecttype != null">effecttype,</if>
            <if test="result != null">result,</if>
            <if test="ispb != null">ispb,</if>
            <if test="addUser != null">add_user,</if>
            <if test="addTime != null">add_time,</if>
            <if test="editUser != null">edit_user,</if>
            <if test="editTime != null">edit_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="yaowua != null">#{yaowua},</if>
            <if test="yaowub != null">#{yaowub},</if>
            <if test="effect != null">#{effect},</if>
            <if test="mechanism != null">#{mechanism},</if>
            <if test="relatedrug != null">#{relatedrug},</if>
            <if test="reference != null">#{reference},</if>
            <if test="impBs != null">#{impBs},</if>
            <if test="jxBs != null">#{jxBs},</if>
            <if test="recommandations != null">#{recommandations},</if>
            <if test="main != null">#{main},</if>
            <if test="sjbs != null">#{sjbs},</if>
            <if test="gx != null">#{gx},</if>
            <if test="remark != null">#{remark},</if>
            <if test="sugflag != null">#{sugflag},</if>
            <if test="type != null">#{type},</if>
            <if test="significance != null">#{significance},</if>
            <if test="onset != null">#{onset},</if>
            <if test="documentation != null">#{documentation},</if>
            <if test="effecttype != null">#{effecttype},</if>
            <if test="result != null">#{result},</if>
            <if test="ispb != null">#{ispb},</if>
            <if test="addUser != null">#{addUser},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="editUser != null">#{editUser},</if>
            <if test="editTime != null">#{editTime},</if>
         </trim>
    </insert>

    <update id="updateRmsTXhzyEdi" parameterType="RmsTXhzyEdi">
        update rms_t_xhzy_edi
        <trim prefix="SET" suffixOverrides=",">
            <if test="yaowua != null">yaowua = #{yaowua},</if>
            <if test="yaowub != null">yaowub = #{yaowub},</if>
            <if test="effect != null">effect = #{effect},</if>
            <if test="mechanism != null">mechanism = #{mechanism},</if>
            <if test="relatedrug != null">relatedrug = #{relatedrug},</if>
            <if test="reference != null">reference = #{reference},</if>
            <if test="impBs != null">imp_bs = #{impBs},</if>
            <if test="jxBs != null">jx_bs = #{jxBs},</if>
            <if test="recommandations != null">recommandations = #{recommandations},</if>
            <if test="main != null">main = #{main},</if>
            <if test="sjbs != null">sjbs = #{sjbs},</if>
            <if test="gx != null">gx = #{gx},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="sugflag != null">sugflag = #{sugflag},</if>
            <if test="type != null">type = #{type},</if>
            <if test="significance != null">significance = #{significance},</if>
            <if test="onset != null">onset = #{onset},</if>
            <if test="documentation != null">documentation = #{documentation},</if>
            <if test="effecttype != null">effecttype = #{effecttype},</if>
            <if test="result != null">result = #{result},</if>
            <if test="ispb != null">ispb = #{ispb},</if>
            <if test="addUser != null">add_user = #{addUser},</if>
            <if test="addTime != null">add_time = #{addTime},</if>
            <if test="editUser != null">edit_user = #{editUser},</if>
            <if test="editTime != null">edit_time = #{editTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTXhzyEdiById" parameterType="Long">
        delete from rms_t_xhzy_edi where id = #{id}
    </delete>

    <delete id="deleteRmsTXhzyEdiByIds" parameterType="String">
        delete from rms_t_xhzy_edi where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap type="com.rms.core.domain.IncompatibleDrugResult" id="IncompatibleDrugResultMap">
        <result property="id" column="id" />
        <result property="yaowua" column="yaowua" />
        <result property="yaowub" column="yaowub" />
        <result property="yaowuaName" column="yaowua_name" />
        <result property="yaowubName" column="yaowub_name" />
        <result property="effect" column="effect" />
        <result property="mechanism" column="mechanism" />
        <result property="relatedrug" column="relatedrug" />
        <result property="reference" column="reference" />
        <result property="impBs" column="imp_bs" />
        <result property="recommandations" column="recommandations" />
        <result property="sugflag" column="sugflag" />
        <result property="result" column="result" />
        <result property="ispb" column="ispb" />
        <result property="remark" column="remark" />
    </resultMap>

    <select id="selectIncompatibleRulesBySdaId" parameterType="Long" resultMap="IncompatibleDrugResultMap">
        SELECT 
            e.id,
            e.yaowua,
            e.yaowub,
            s1.ym as yaowua_name,
            s2.ym as yaowub_name,
            e.effect,
            e.mechanism,
            e.relatedrug,
            e.reference,
            e.imp_bs,
            e.recommandations,
            e.sugflag,
            e.result,
            e.ispb,
            e.remark
        FROM rms_t_xhzy_edi e
        LEFT JOIN rms_t_sda s1 ON e.yaowua = s1.id
        LEFT JOIN rms_t_sda s2 ON e.yaowub = s2.id
        WHERE e.yaowua = #{sdaId} OR e.yaowub = #{sdaId}
    </select>
</mapper>