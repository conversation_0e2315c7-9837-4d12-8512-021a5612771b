import request from '@/utils/request'

// 查询给药途径基础表列表
export function listTtjbase(query) {
  return request({
    url: '/rms/ttjbase/list',
    method: 'get',
    params: query
  })
}

// 查询给药途径基础表详细
export function getTtjbase(name) {
  return request({
    url: '/rms/ttjbase/' + name,
    method: 'get'
  })
}

// 新增给药途径基础表
export function addTtjbase(data) {
  return request({
    url: '/rms/ttjbase',
    method: 'post',
    data: data
  })
}

// 修改给药途径基础表
export function updateTtjbase(data) {
  return request({
    url: '/rms/ttjbase',
    method: 'put',
    data: data
  })
}

// 删除给药途径基础表
export function delTtjbase(name) {
  return request({
    url: '/rms/ttjbase/' + name,
    method: 'delete'
  })
}
