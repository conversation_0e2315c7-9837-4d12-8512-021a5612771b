package com.rms.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 处方信息对象 rms_t_pres
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
public class RmsTPres extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 处方编码 */
    private String code;

    /** 医院编码 */
    @Excel(name = "医院编码")
    private String hospCode;

    /** 科室代码 */
    @Excel(name = "科室代码")
    private String deptCode;

    /** 科室名称 */
    @Excel(name = "科室名称")
    private String deptName;

    /** 医生代码 */
    @Excel(name = "医生代码")
    private String doctCode;

    /** 医生名称 */
    @Excel(name = "医生名称")
    private String doctName;

    /** 医生级别代码 */
    @Excel(name = "医生级别代码")
    private String doctType;

    /** 医生级别名称 */
    @Excel(name = "医生级别名称")
    private String doctTypeName;

    /** HIS时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "HIS时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date hisTime;

    /** 门诊/住院标识：op-门诊；ip-住院 */
    @Excel(name = "门诊/住院标识：op-门诊；ip-住院")
    private String hospFlag;

    /** 就诊类型 */
    @Excel(name = "就诊类型")
    private String treatType;

    /** 就诊号 */
    @Excel(name = "就诊号")
    private String treatCode;

    /** 床位号 */
    @Excel(name = "床位号")
    private String bedNo;

    /** 患者姓名 */
    @Excel(name = "患者姓名")
    private String name;

    /** 出生日期 */
    @Excel(name = "出生日期")
    private String birth;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 体重（千克） */
    @Excel(name = "体重", readConverterExp = "千=克")
    private String weight;

    /** 身高（厘米） */
    @Excel(name = "身高", readConverterExp = "厘=米")
    private String height;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 病历号 */
    @Excel(name = "病历号")
    private String medicalRecord;

    /** 卡类型 */
    @Excel(name = "卡类型")
    private String cardType;

    /** 卡号 */
    @Excel(name = "卡号")
    private String cardCode;

    /** 孕期单位（天、周、月） */
    @Excel(name = "孕期单位", readConverterExp = "天=、周、月")
    private String pregnantUnit;

    /** 孕期 */
    @Excel(name = "孕期")
    private String pregnant;

    /** 过敏信息 */
    @Excel(name = "过敏信息")
    private String allInfo;

    /** 诊断信息 */
    @Excel(name = "诊断信息")
    private String diaInfo;

    /** 处方号 */
    @Excel(name = "处方号")
    private String presId;

    /** 处方理由 */
    @Excel(name = "处方理由")
    private String reason;

    /** 是否紧急处方：0-否；1-是 */
    @Excel(name = "是否紧急处方：0-否；1-是")
    private String isUrgent;

    /** 是否新开处方：0-否；1-是 */
    @Excel(name = "是否新开处方：0-否；1-是")
    private String isNew;

    /** 是否当前处方：0-否；1-是 */
    @Excel(name = "是否当前处方：0-否；1-是")
    private String isCurrent;

    /** 医嘱类型：L-长期医嘱；T-临时医嘱 */
    @Excel(name = "医嘱类型：L-长期医嘱；T-临时医嘱")
    private String presType;

    /** 处方时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "处方时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date presTime;

    /** 出院带药标识：0-否；1-是 */
    @Excel(name = "出院带药标识：0-否；1-是")
    private String dischargeDrug;

    /** 中药处方(医嘱)服用类型：0-未定；1-内服；2-外敷 */
    @Excel(name = "中药处方(医嘱)服用类型：0-未定；1-内服；2-外敷")
    private String admType;

    /** 要求 */
    @Excel(name = "要求")
    private String requir;

    /** 次数 */
    @Excel(name = "次数")
    private String cs1;

    /** 天数 */
    @Excel(name = "天数")
    private String ts;

    /** 煎煮溶剂 */
    @Excel(name = "煎煮溶剂")
    private String solvent;

    /** 剂量 */
    @Excel(name = "剂量")
    private String jl;

    /** 次数 */
    @Excel(name = "次数")
    private String cs2;

    /** 煎药类别 */
    @Excel(name = "煎药类别")
    private String lb;

    /** 方式1 */
    @Excel(name = "方式1")
    private String fs1;

    /** 方式2 */
    @Excel(name = "方式2")
    private String fs2;

    /** 处方(医嘱)类型：0-未定；1-西药；2-中药(草药) */
    @Excel(name = "处方(医嘱)类型：0-未定；1-西药；2-中药(草药)")
    private String prescriptionType;

    /** 级别（未用） */
    @Excel(name = "级别", readConverterExp = "未=用")
    private String level;

    /** 处方状态：-1-临时处方；0-无问题处方；8-问题处方；10-进入审方中心处方；11-审核不通过处方；12-审核通过处方；13-双签通过处方；14-超时通过处方 */
    @Excel(name = "处方状态：-1-临时处方；0-无问题处方；8-问题处方；10-进入审方中心处方；11-审核不通过处方；12-审核通过处方；13-双签通过处方；14-超时通过处方")
    private Long flag;

    /** 医生已读 */
    @Excel(name = "医生已读")
    private String isReadDoc;

    /** 药师已读 */
    @Excel(name = "药师已读")
    private String isReadYs;

    /** 文本 */
    @Excel(name = "文本")
    private String text;

    /** 中医主病 */
    @Excel(name = "中医主病")
    private String zyzb;

    /** 中医主病代码 */
    @Excel(name = "中医主病代码")
    private String zyzbCode;

    /** 中医主症 */
    @Excel(name = "中医主症")
    private String zyzz;

    /** 中医主症代码 */
    @Excel(name = "中医主症代码")
    private String zyzzCode;

    /** 处方理由1 */
    @Excel(name = "处方理由1")
    private String reason1;

    /** 处方说明 */
    @Excel(name = "处方说明")
    private String presSm;

    /** 处方理由2 */
    @Excel(name = "处方理由2")
    private String reason2;

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public void setHospCode(String hospCode)
    {
        this.hospCode = hospCode;
    }

    public String getHospCode()
    {
        return hospCode;
    }

    public void setDeptCode(String deptCode)
    {
        this.deptCode = deptCode;
    }

    public String getDeptCode()
    {
        return deptCode;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDoctCode(String doctCode)
    {
        this.doctCode = doctCode;
    }

    public String getDoctCode()
    {
        return doctCode;
    }

    public void setDoctName(String doctName)
    {
        this.doctName = doctName;
    }

    public String getDoctName()
    {
        return doctName;
    }

    public void setDoctType(String doctType)
    {
        this.doctType = doctType;
    }

    public String getDoctType()
    {
        return doctType;
    }

    public void setDoctTypeName(String doctTypeName)
    {
        this.doctTypeName = doctTypeName;
    }

    public String getDoctTypeName()
    {
        return doctTypeName;
    }

    public void setHisTime(Date hisTime)
    {
        this.hisTime = hisTime;
    }

    public Date getHisTime()
    {
        return hisTime;
    }

    public void setHospFlag(String hospFlag)
    {
        this.hospFlag = hospFlag;
    }

    public String getHospFlag()
    {
        return hospFlag;
    }

    public void setTreatType(String treatType)
    {
        this.treatType = treatType;
    }

    public String getTreatType()
    {
        return treatType;
    }

    public void setTreatCode(String treatCode)
    {
        this.treatCode = treatCode;
    }

    public String getTreatCode()
    {
        return treatCode;
    }

    public void setBedNo(String bedNo)
    {
        this.bedNo = bedNo;
    }

    public String getBedNo()
    {
        return bedNo;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }

    public void setBirth(String birth)
    {
        this.birth = birth;
    }

    public String getBirth()
    {
        return birth;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getSex()
    {
        return sex;
    }

    public void setWeight(String weight)
    {
        this.weight = weight;
    }

    public String getWeight()
    {
        return weight;
    }

    public void setHeight(String height)
    {
        this.height = height;
    }

    public String getHeight()
    {
        return height;
    }

    public void setIdCard(String idCard)
    {
        this.idCard = idCard;
    }

    public String getIdCard()
    {
        return idCard;
    }

    public void setMedicalRecord(String medicalRecord)
    {
        this.medicalRecord = medicalRecord;
    }

    public String getMedicalRecord()
    {
        return medicalRecord;
    }

    public void setCardType(String cardType)
    {
        this.cardType = cardType;
    }

    public String getCardType()
    {
        return cardType;
    }

    public void setCardCode(String cardCode)
    {
        this.cardCode = cardCode;
    }

    public String getCardCode()
    {
        return cardCode;
    }

    public void setPregnantUnit(String pregnantUnit)
    {
        this.pregnantUnit = pregnantUnit;
    }

    public String getPregnantUnit()
    {
        return pregnantUnit;
    }

    public void setPregnant(String pregnant)
    {
        this.pregnant = pregnant;
    }

    public String getPregnant()
    {
        return pregnant;
    }

    public void setAllInfo(String allInfo)
    {
        this.allInfo = allInfo;
    }

    public String getAllInfo()
    {
        return allInfo;
    }

    public void setDiaInfo(String diaInfo)
    {
        this.diaInfo = diaInfo;
    }

    public String getDiaInfo()
    {
        return diaInfo;
    }

    public void setPresId(String presId)
    {
        this.presId = presId;
    }

    public String getPresId()
    {
        return presId;
    }

    public void setReason(String reason)
    {
        this.reason = reason;
    }

    public String getReason()
    {
        return reason;
    }

    public void setIsUrgent(String isUrgent)
    {
        this.isUrgent = isUrgent;
    }

    public String getIsUrgent()
    {
        return isUrgent;
    }

    public void setIsNew(String isNew)
    {
        this.isNew = isNew;
    }

    public String getIsNew()
    {
        return isNew;
    }

    public void setIsCurrent(String isCurrent)
    {
        this.isCurrent = isCurrent;
    }

    public String getIsCurrent()
    {
        return isCurrent;
    }

    public void setPresType(String presType)
    {
        this.presType = presType;
    }

    public String getPresType()
    {
        return presType;
    }

    public void setPresTime(Date presTime)
    {
        this.presTime = presTime;
    }

    public Date getPresTime()
    {
        return presTime;
    }

    public void setDischargeDrug(String dischargeDrug)
    {
        this.dischargeDrug = dischargeDrug;
    }

    public String getDischargeDrug()
    {
        return dischargeDrug;
    }

    public void setAdmType(String admType)
    {
        this.admType = admType;
    }

    public String getAdmType()
    {
        return admType;
    }

    public void setRequir(String requir)
    {
        this.requir = requir;
    }

    public String getRequir()
    {
        return requir;
    }

    public void setCs1(String cs1)
    {
        this.cs1 = cs1;
    }

    public String getCs1()
    {
        return cs1;
    }

    public void setTs(String ts)
    {
        this.ts = ts;
    }

    public String getTs()
    {
        return ts;
    }

    public void setSolvent(String solvent)
    {
        this.solvent = solvent;
    }

    public String getSolvent()
    {
        return solvent;
    }

    public void setJl(String jl)
    {
        this.jl = jl;
    }

    public String getJl()
    {
        return jl;
    }

    public void setCs2(String cs2)
    {
        this.cs2 = cs2;
    }

    public String getCs2()
    {
        return cs2;
    }

    public void setLb(String lb)
    {
        this.lb = lb;
    }

    public String getLb()
    {
        return lb;
    }

    public void setFs1(String fs1)
    {
        this.fs1 = fs1;
    }

    public String getFs1()
    {
        return fs1;
    }

    public void setFs2(String fs2)
    {
        this.fs2 = fs2;
    }

    public String getFs2()
    {
        return fs2;
    }

    public void setPrescriptionType(String prescriptionType)
    {
        this.prescriptionType = prescriptionType;
    }

    public String getPrescriptionType()
    {
        return prescriptionType;
    }

    public void setLevel(String level)
    {
        this.level = level;
    }

    public String getLevel()
    {
        return level;
    }

    public void setFlag(Long flag)
    {
        this.flag = flag;
    }

    public Long getFlag()
    {
        return flag;
    }

    public void setIsReadDoc(String isReadDoc)
    {
        this.isReadDoc = isReadDoc;
    }

    public String getIsReadDoc()
    {
        return isReadDoc;
    }

    public void setIsReadYs(String isReadYs)
    {
        this.isReadYs = isReadYs;
    }

    public String getIsReadYs()
    {
        return isReadYs;
    }

    public void setText(String text)
    {
        this.text = text;
    }

    public String getText()
    {
        return text;
    }

    public void setZyzb(String zyzb)
    {
        this.zyzb = zyzb;
    }

    public String getZyzb()
    {
        return zyzb;
    }

    public void setZyzbCode(String zyzbCode)
    {
        this.zyzbCode = zyzbCode;
    }

    public String getZyzbCode()
    {
        return zyzbCode;
    }

    public void setZyzz(String zyzz)
    {
        this.zyzz = zyzz;
    }

    public String getZyzz()
    {
        return zyzz;
    }

    public void setZyzzCode(String zyzzCode)
    {
        this.zyzzCode = zyzzCode;
    }

    public String getZyzzCode()
    {
        return zyzzCode;
    }

    public void setReason1(String reason1)
    {
        this.reason1 = reason1;
    }

    public String getReason1()
    {
        return reason1;
    }

    public void setPresSm(String presSm)
    {
        this.presSm = presSm;
    }

    public String getPresSm()
    {
        return presSm;
    }

    public void setReason2(String reason2)
    {
        this.reason2 = reason2;
    }

    public String getReason2()
    {
        return reason2;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("hospCode", getHospCode())
            .append("deptCode", getDeptCode())
            .append("deptName", getDeptName())
            .append("doctCode", getDoctCode())
            .append("doctName", getDoctName())
            .append("doctType", getDoctType())
            .append("doctTypeName", getDoctTypeName())
            .append("hisTime", getHisTime())
            .append("hospFlag", getHospFlag())
            .append("treatType", getTreatType())
            .append("treatCode", getTreatCode())
            .append("bedNo", getBedNo())
            .append("name", getName())
            .append("birth", getBirth())
            .append("sex", getSex())
            .append("weight", getWeight())
            .append("height", getHeight())
            .append("idCard", getIdCard())
            .append("medicalRecord", getMedicalRecord())
            .append("cardType", getCardType())
            .append("cardCode", getCardCode())
            .append("pregnantUnit", getPregnantUnit())
            .append("pregnant", getPregnant())
            .append("allInfo", getAllInfo())
            .append("diaInfo", getDiaInfo())
            .append("presId", getPresId())
            .append("reason", getReason())
            .append("isUrgent", getIsUrgent())
            .append("isNew", getIsNew())
            .append("isCurrent", getIsCurrent())
            .append("presType", getPresType())
            .append("presTime", getPresTime())
            .append("dischargeDrug", getDischargeDrug())
            .append("admType", getAdmType())
            .append("requir", getRequir())
            .append("cs1", getCs1())
            .append("ts", getTs())
            .append("solvent", getSolvent())
            .append("jl", getJl())
            .append("cs2", getCs2())
            .append("lb", getLb())
            .append("fs1", getFs1())
            .append("fs2", getFs2())
            .append("prescriptionType", getPrescriptionType())
            .append("level", getLevel())
            .append("flag", getFlag())
            .append("isReadDoc", getIsReadDoc())
            .append("isReadYs", getIsReadYs())
            .append("text", getText())
            .append("zyzb", getZyzb())
            .append("zyzbCode", getZyzbCode())
            .append("zyzz", getZyzz())
            .append("zyzzCode", getZyzzCode())
            .append("reason1", getReason1())
            .append("presSm", getPresSm())
            .append("reason2", getReason2())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
