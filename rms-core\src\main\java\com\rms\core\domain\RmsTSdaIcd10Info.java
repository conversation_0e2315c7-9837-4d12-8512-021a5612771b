package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 药物与诊断信息对象 rms_t_sda_icd10_info
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public class RmsTSdaIcd10Info extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 药品说明书ID */
    @Excel(name = "药品说明书ID")
    private Long sdaId;

    /** 诊断编码 */
    @Excel(name = "诊断编码")
    private String diagnoses;

    /** 扩展（未用） */
    @Excel(name = "扩展", readConverterExp = "未=用")
    private Long expand;

    /** 标识 */
    @Excel(name = "标识")
    private Long bs;

    /** （未用） */
    @Excel(name = "", readConverterExp = "未=用")
    private Long gzd;

    /** （未用） */
    @Excel(name = "", readConverterExp = "未=用")
    private String pri;

    /** （未用） */
    @Excel(name = "", readConverterExp = "未=用")
    private String illhis;

    /** （未用） */
    @Excel(name = "", readConverterExp = "未=用")
    private Long sdaIcd10StencilExpand;

    /** 是否屏蔽 */
    @Excel(name = "是否屏蔽")
    private String ispb;

    /** （未用） */
    @Excel(name = "", readConverterExp = "未=用")
    private String editTime;

    /** （未用） */
    @Excel(name = "", readConverterExp = "未=用")
    private String editUser;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setDiagnoses(String diagnoses)
    {
        this.diagnoses = diagnoses;
    }

    public String getDiagnoses()
    {
        return diagnoses;
    }

    public void setExpand(Long expand)
    {
        this.expand = expand;
    }

    public Long getExpand()
    {
        return expand;
    }

    public void setBs(Long bs)
    {
        this.bs = bs;
    }

    public Long getBs()
    {
        return bs;
    }

    public void setGzd(Long gzd)
    {
        this.gzd = gzd;
    }

    public Long getGzd()
    {
        return gzd;
    }

    public void setPri(String pri)
    {
        this.pri = pri;
    }

    public String getPri()
    {
        return pri;
    }

    public void setIllhis(String illhis)
    {
        this.illhis = illhis;
    }

    public String getIllhis()
    {
        return illhis;
    }

    public void setSdaIcd10StencilExpand(Long sdaIcd10StencilExpand)
    {
        this.sdaIcd10StencilExpand = sdaIcd10StencilExpand;
    }

    public Long getSdaIcd10StencilExpand()
    {
        return sdaIcd10StencilExpand;
    }

    public void setIspb(String ispb)
    {
        this.ispb = ispb;
    }

    public String getIspb()
    {
        return ispb;
    }

    public void setEditTime(String editTime)
    {
        this.editTime = editTime;
    }

    public String getEditTime()
    {
        return editTime;
    }

    public void setEditUser(String editUser)
    {
        this.editUser = editUser;
    }

    public String getEditUser()
    {
        return editUser;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sdaId", getSdaId())
            .append("diagnoses", getDiagnoses())
            .append("expand", getExpand())
            .append("bs", getBs())
            .append("gzd", getGzd())
            .append("pri", getPri())
            .append("remark", getRemark())
            .append("illhis", getIllhis())
            .append("sdaIcd10StencilExpand", getSdaIcd10StencilExpand())
            .append("ispb", getIspb())
            .append("editTime", getEditTime())
            .append("editUser", getEditUser())
            .toString();
    }
}
