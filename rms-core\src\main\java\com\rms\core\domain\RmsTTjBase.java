package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 给药途径基础表对象 rms_t_tj_base
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public class RmsTTjBase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 给药途径名称 */
    @Excel(name = "给药途径名称")
    private String name;

    /** 描述 */
    @Excel(name = "描述")
    private String ms;

    /** 代码 */
    @Excel(name = "代码")
    private String dm;

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }

    public void setMs(String ms)
    {
        this.ms = ms;
    }

    public String getMs()
    {
        return ms;
    }

    public void setDm(String dm)
    {
        this.dm = dm;
    }

    public String getDm()
    {
        return dm;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("name", getName())
            .append("ms", getMs())
            .append("dm", getDm())
            .toString();
    }
}
