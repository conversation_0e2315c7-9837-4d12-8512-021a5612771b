import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Scanner;

/**
 * RMS API 调用演示程序
 * 
 * 本示例演示如何调用合理用药与事前审方系统的三个核心API接口：
 * 1. 处方分析接口 - 分析处方合理性
 * 2. 处方审核结果查询接口 - 查询处方审核状态
 * 3. 药品信息查询接口 - 获取药品要点提示
 * 
 * 使用方法：
 * 1. 确保RMS系统已启动并运行在 http://localhost:8080
 * 2. 编译：javac RmsApiDemo.java
 * 3. 运行：java RmsApiDemo
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public class RmsApiDemo {
    
    // API服务基础URL
    private static final String BASE_URL = "http://localhost:8080/api/v1";
    
    // 请求超时时间（毫秒）
    private static final int TIMEOUT = 30000;
    
    public static void main(String[] args) {
        RmsApiDemo demo = new RmsApiDemo();

        System.out.println("=".repeat(80));
        System.out.println("RMS API 调用演示程序");
        System.out.println("=".repeat(80));
        System.out.println("API服务地址: " + BASE_URL);
        System.out.println("请确保RMS系统已启动并运行在 http://localhost:8080");
        System.out.println("=".repeat(80));

        try {
            // 检查API服务是否可用
            if (!demo.checkApiAvailability()) {
                System.err.println("❌ API服务不可用，请检查服务是否已启动");
                return;
            }

            System.out.println("✅ API服务连接正常");

            // 按照业务流程顺序调用三个接口

            // 1. 处方分析
            System.out.println("\n" + "=".repeat(50));
            System.out.println("【步骤1】调用处方分析接口");
            System.out.println("=".repeat(50));
            String analyzeResponse = demo.callPrescriptionAnalyze();
            demo.parseAnalyzeResponse(analyzeResponse);

            // 2. 查询审核结果
            System.out.println("\n" + "=".repeat(50));
            System.out.println("【步骤2】查询处方审核结果");
            System.out.println("=".repeat(50));
            String checkResponse = demo.callPrescriptionCheckResult();
            demo.parseCheckResultResponse(checkResponse);

            // 3. 查询药品信息
            System.out.println("\n" + "=".repeat(50));
            System.out.println("【步骤3】查询药品信息");
            System.out.println("=".repeat(50));
            String infoResponse = demo.callMedicineInfo();
            demo.parseMedicineInfoResponse(infoResponse);

            System.out.println("\n" + "=".repeat(80));
            System.out.println("🎉 所有API调用演示完成！");
            System.out.println("=".repeat(80));

        } catch (Exception e) {
            System.err.println("❌ API调用过程中发生错误：" + e.getMessage());
            e.printStackTrace();
            System.err.println("\n故障排查建议：");
            System.err.println("1. 检查RMS系统是否已启动");
            System.err.println("2. 确认API服务端口是否为8080");
            System.err.println("3. 检查网络连接是否正常");
            System.err.println("4. 查看服务端日志获取详细错误信息");
        }
    }

    /**
     * 检查API服务是否可用
     * 通过发送简单的HTTP请求来测试连接
     *
     * @return true表示服务可用，false表示不可用
     */
    private boolean checkApiAvailability() {
        try {
            URL url = new URL(BASE_URL.replace("/api/v1", "") + "/actuator/health");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            connection.disconnect();

            return responseCode == 200;
        } catch (Exception e) {
            // 如果健康检查端点不可用，尝试直接访问API端点
            try {
                URL url = new URL(BASE_URL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);

                int responseCode = connection.getResponseCode();
                connection.disconnect();

                // 404是正常的，说明服务在运行但端点不支持GET
                return responseCode == 404 || responseCode == 405;
            } catch (Exception ex) {
                return false;
            }
        }
    }

    /**
     * 解析处方分析响应结果
     *
     * @param response API响应JSON字符串
     */
    private void parseAnalyzeResponse(String response) {
        System.out.println("📊 处方分析结果解析：");
        try {
            if (response.contains("\"code\":200")) {
                System.out.println("✅ 分析成功");

                // 简单的JSON解析（生产环境建议使用JSON库）
                if (response.contains("\"problemLevel\":0")) {
                    System.out.println("   问题级别：无问题");
                } else if (response.contains("\"problemLevel\":1")) {
                    System.out.println("   问题级别：其他问题");
                } else if (response.contains("\"problemLevel\":2")) {
                    System.out.println("   问题级别：一般问题");
                } else if (response.contains("\"problemLevel\":3")) {
                    System.out.println("   问题级别：严重问题");
                }

                if (response.contains("\"doubleSignFlag\":1")) {
                    System.out.println("   双签要求：需要双签");
                } else {
                    System.out.println("   双签要求：不需要双签");
                }
            } else {
                System.out.println("❌ 分析失败");
            }
        } catch (Exception e) {
            System.out.println("❌ 响应解析失败：" + e.getMessage());
        }
    }

    /**
     * 解析处方审核结果响应
     *
     * @param response API响应JSON字符串
     */
    private void parseCheckResultResponse(String response) {
        System.out.println("📋 审核结果解析：");
        try {
            if (response.contains("\"code\":200")) {
                System.out.println("✅ 查询成功");

                if (response.contains("\"checkStatus\":0")) {
                    System.out.println("   审核状态：无需人工干预或审核通过");
                } else if (response.contains("\"checkStatus\":1")) {
                    System.out.println("   审核状态：待审核");
                } else if (response.contains("\"checkStatus\":2")) {
                    System.out.println("   审核状态：审核不通过");
                }
            } else {
                System.out.println("❌ 查询失败");
            }
        } catch (Exception e) {
            System.out.println("❌ 响应解析失败：" + e.getMessage());
        }
    }

    /**
     * 解析药品信息响应结果
     *
     * @param response API响应JSON字符串
     */
    private void parseMedicineInfoResponse(String response) {
        System.out.println("💊 药品信息解析：");
        try {
            if (response.contains("\"code\":200")) {
                System.out.println("✅ 查询成功");

                // 提取药品名称和拼音缩写
                String drugName = extractJsonValue(response, "ym");
                String pinyin = extractJsonValue(response, "sp");

                if (drugName != null) {
                    System.out.println("   药品名称：" + drugName);
                }
                if (pinyin != null) {
                    System.out.println("   拼音缩写：" + pinyin);
                }
            } else {
                System.out.println("❌ 查询失败");
            }
        } catch (Exception e) {
            System.out.println("❌ 响应解析失败：" + e.getMessage());
        }
    }

    /**
     * 简单的JSON值提取方法
     * 注意：这是一个简化的实现，生产环境建议使用专业的JSON解析库
     *
     * @param json JSON字符串
     * @param key 要提取的键
     * @return 提取的值，如果未找到返回null
     */
    private String extractJsonValue(String json, String key) {
        try {
            String pattern = "\"" + key + "\":\"";
            int startIndex = json.indexOf(pattern);
            if (startIndex == -1) {
                return null;
            }
            startIndex += pattern.length();
            int endIndex = json.indexOf("\"", startIndex);
            if (endIndex == -1) {
                return null;
            }
            return json.substring(startIndex, endIndex);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 调用处方分析接口
     * 功能：分析处方合理性，检查配伍禁忌、用药剂量、过敏风险等
     * 
     * @return API响应结果
     * @throws Exception 调用异常
     */
    public String callPrescriptionAnalyze() throws Exception {
        String url = BASE_URL + "/prescription/analyze";
        
        // 构建请求JSON数据
        String requestJson = buildPrescriptionAnalyzeRequest();
        
        System.out.println("请求URL: " + url);
        System.out.println("请求数据: " + requestJson);
        
        // 发送HTTP POST请求
        String response = sendPostRequest(url, requestJson);
        
        System.out.println("响应结果: " + response);
        return response;
    }
    
    /**
     * 调用处方审核结果查询接口
     * 功能：查询指定处方的审核状态（通过、待审核、不通过）
     * 
     * @return API响应结果
     * @throws Exception 调用异常
     */
    public String callPrescriptionCheckResult() throws Exception {
        String url = BASE_URL + "/prescription/check-result";
        
        // 构建请求JSON数据
        String requestJson = buildPrescriptionCheckResultRequest();
        
        System.out.println("请求URL: " + url);
        System.out.println("请求数据: " + requestJson);
        
        // 发送HTTP POST请求
        String response = sendPostRequest(url, requestJson);
        
        System.out.println("响应结果: " + response);
        return response;
    }
    
    /**
     * 调用药品信息查询接口
     * 功能：获取药品的要点提示信息（医保提示、抗菌提示、说明书等）
     * 
     * @return API响应结果
     * @throws Exception 调用异常
     */
    public String callMedicineInfo() throws Exception {
        String url = BASE_URL + "/medicine/info";
        
        // 构建请求JSON数据
        String requestJson = buildMedicineInfoRequest();
        
        System.out.println("请求URL: " + url);
        System.out.println("请求数据: " + requestJson);
        
        // 发送HTTP POST请求
        String response = sendPostRequest(url, requestJson);
        
        System.out.println("响应结果: " + response);
        return response;
    }
    
    /**
     * 发送HTTP POST请求的通用方法
     * 包含详细的错误处理和日志输出
     *
     * @param urlString 请求URL
     * @param jsonData 请求JSON数据
     * @return 响应结果
     * @throws Exception 请求异常
     */
    private String sendPostRequest(String urlString, String jsonData) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            // 设置请求方法和属性
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "RmsApiDemo/1.0");
            connection.setDoOutput(true);
            connection.setConnectTimeout(TIMEOUT);
            connection.setReadTimeout(TIMEOUT);

            System.out.println("🔗 建立连接...");

            // 发送请求数据
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
                System.out.println("📤 请求数据已发送 (" + input.length + " 字节)");
            }

            // 获取响应状态码
            int responseCode = connection.getResponseCode();
            String responseMessage = connection.getResponseMessage();

            System.out.println("📥 HTTP状态: " + responseCode + " " + responseMessage);

            // 读取响应数据
            InputStream inputStream;
            if (responseCode >= 200 && responseCode < 300) {
                inputStream = connection.getInputStream();
                System.out.println("✅ 请求成功");
            } else {
                inputStream = connection.getErrorStream();
                System.out.println("❌ 请求失败");
            }

            if (inputStream == null) {
                throw new Exception("无法获取响应流，状态码：" + responseCode);
            }

            StringBuilder response = new StringBuilder();
            try (Scanner scanner = new Scanner(inputStream, StandardCharsets.UTF_8.name())) {
                while (scanner.hasNextLine()) {
                    response.append(scanner.nextLine());
                }
            }

            String responseBody = response.toString();
            System.out.println("📄 响应长度: " + responseBody.length() + " 字符");

            // 如果是错误状态码，抛出异常
            if (responseCode >= 400) {
                throw new Exception("HTTP错误 " + responseCode + ": " + responseMessage +
                                  "\n响应内容: " + responseBody);
            }

            return responseBody;

        } catch (Exception e) {
            System.err.println("❌ 请求异常: " + e.getMessage());
            throw e;
        } finally {
            connection.disconnect();
            System.out.println("🔌 连接已关闭");
        }
    }
    
    /**
     * 构建处方分析请求的JSON数据
     * 包含完整的患者信息、诊断信息、处方信息和药品信息
     * 
     * @return JSON字符串
     */
    private String buildPrescriptionAnalyzeRequest() {
        return "{\n" +
                "  \"baseInfo\": {\n" +
                "    \"source\": \"HIS\",\n" +
                "    \"hospCode\": \"H001\",\n" +
                "    \"deptCode\": \"D002\",\n" +
                "    \"deptName\": \"内科\",\n" +
                "    \"doctor\": {\n" +
                "      \"code\": \"DR003\",\n" +
                "      \"name\": \"张三\",\n" +
                "      \"type\": \"1\",\n" +
                "      \"typeName\": \"主治医师\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"details\": {\n" +
                "    \"isUpload\": 0,\n" +
                "    \"hisTime\": \"2025-07-20 10:30:00\",\n" +
                "    \"hospFlag\": \"op\",\n" +
                "    \"treatType\": 100,\n" +
                "    \"treatCode\": \"T000123\",\n" +
                "    \"patient\": {\n" +
                "      \"name\": \"李四\",\n" +
                "      \"isInfant\": 0,\n" +
                "      \"birth\": \"1990-01-15\",\n" +
                "      \"sex\": \"男\",\n" +
                "      \"weight\": 65,\n" +
                "      \"height\": 175,\n" +
                "      \"allergicData\": [\n" +
                "        {\n" +
                "          \"type\": 1,\n" +
                "          \"name\": \"青霉素\",\n" +
                "          \"code\": \"ALG001\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"diagnoseData\": [\n" +
                "        {\n" +
                "          \"type\": 2,\n" +
                "          \"name\": \"高血压\",\n" +
                "          \"code\": \"I10\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"prescriptionData\": [\n" +
                "      {\n" +
                "        \"id\": \"RX001\",\n" +
                "        \"reason\": \"常规治疗\",\n" +
                "        \"isCurrent\": 1,\n" +
                "        \"presType\": \"L\",\n" +
                "        \"presTime\": \"2025-07-20 10:30:00\",\n" +
                "        \"prescriptionType\": 1,\n" +
                "        \"presSm\": \"每日一次\",\n" +
                "        \"medicineData\": [\n" +
                "          {\n" +
                "            \"name\": \"硝苯地平片\",\n" +
                "            \"hisCode\": \"M001\",\n" +
                "            \"spec\": \"10mg/片\",\n" +
                "            \"group\": \"1\",\n" +
                "            \"reason\": \"降压\",\n" +
                "            \"doseUnit\": \"mg\",\n" +
                "            \"dose\": 10,\n" +
                "            \"freq\": \"QD\",\n" +
                "            \"administer\": \"PO\",\n" +
                "            \"beginTime\": \"2025-07-20 10:30:00\",\n" +
                "            \"endTime\": \"2025-07-27 10:30:00\",\n" +
                "            \"days\": 7,\n" +
                "            \"money\": 30.5,\n" +
                "            \"yysm\": \"每日一次\",\n" +
                "            \"qydd\": \"院内取药\"\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }
    
    /**
     * 构建处方审核结果查询请求的JSON数据
     * 用于查询指定处方的审核状态
     * 
     * @return JSON字符串
     */
    private String buildPrescriptionCheckResultRequest() {
        return "{\n" +
                "  \"baseInfo\": {\n" +
                "    \"source\": \"HIS\",\n" +
                "    \"hospCode\": \"H001\",\n" +
                "    \"deptCode\": \"D002\",\n" +
                "    \"deptName\": \"内科\",\n" +
                "    \"doctor\": {\n" +
                "      \"code\": \"DR003\",\n" +
                "      \"name\": \"张三\",\n" +
                "      \"type\": \"1\",\n" +
                "      \"typeName\": \"主治医师\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"details\": {\n" +
                "    \"hospFlag\": \"op\",\n" +
                "    \"treatCode\": \"T000123\",\n" +
                "    \"prescriptionId\": \"RX001\"\n" +
                "  }\n" +
                "}";
    }
    
    /**
     * 构建药品信息查询请求的JSON数据
     * 用于获取药品的要点提示信息
     * 
     * @return JSON字符串
     */
    private String buildMedicineInfoRequest() {
        return "{\n" +
                "  \"baseInfo\": {\n" +
                "    \"source\": \"HIS\",\n" +
                "    \"hospCode\": \"H001\",\n" +
                "    \"deptCode\": \"D002\",\n" +
                "    \"deptName\": \"内科\",\n" +
                "    \"doctor\": {\n" +
                "      \"code\": \"DR003\",\n" +
                "      \"name\": \"张三\",\n" +
                "      \"type\": \"1\",\n" +
                "      \"typeName\": \"主治医师\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"details\": {\n" +
                "    \"hospFlag\": \"op\",\n" +
                "    \"medicine\": {\n" +
                "      \"hisCode\": \"M001\",\n" +
                "      \"hisName\": \"硝苯地平片\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }
}

/*
 * ============================================================================
 * RMS API 调用演示程序使用说明
 * ============================================================================
 *
 * 1. 编译和运行
 *    编译：javac RmsApiDemo.java
 *    运行：java RmsApiDemo
 *
 * 2. 前置条件
 *    - 确保RMS系统已启动并运行在 http://localhost:8080
 *    - 确保网络连接正常
 *    - 确保Java环境已正确配置
 *
 * 3. 程序功能
 *    本程序演示了如何调用RMS系统的三个核心API接口：
 *
 *    a) 处方分析接口 (/api/v1/prescription/analyze)
 *       - 功能：分析处方合理性，检查配伍禁忌、用药剂量、过敏风险等
 *       - 输入：完整的处方信息，包括患者信息、诊断、药品等
 *       - 输出：问题级别、双签标志、问题详情等
 *
 *    b) 处方审核结果查询接口 (/api/v1/prescription/check-result)
 *       - 功能：查询指定处方的审核状态
 *       - 输入：医院编码、就诊号、处方号
 *       - 输出：审核状态（通过/待审核/不通过）
 *
 *    c) 药品信息查询接口 (/api/v1/medicine/info)
 *       - 功能：获取药品的要点提示信息
 *       - 输入：医院编码、药品代码
 *       - 输出：药品名称、拼音缩写等信息
 *
 * 4. 响应格式
 *    所有API都返回统一的JSON格式：
 *    {
 *      "code": 200,        // 状态码：200-成功，其他-失败
 *      "msg": "操作成功",   // 状态描述
 *      "data": {}          // 业务数据
 *    }
 *
 * 5. 错误处理
 *    程序包含完整的错误处理机制：
 *    - 网络连接错误
 *    - HTTP状态码错误
 *    - JSON格式错误
 *    - 业务逻辑错误
 *
 * 6. 扩展建议
 *    在实际项目中，建议进行以下改进：
 *    - 使用专业的JSON解析库（如Jackson、Gson）
 *    - 添加重试机制
 *    - 实现连接池
 *    - 添加日志框架
 *    - 实现配置文件管理
 *    - 添加单元测试
 *
 * 7. 故障排查
 *    如果程序运行失败，请检查：
 *    - RMS系统是否已启动
 *    - 端口8080是否被占用
 *    - 防火墙设置是否正确
 *    - 网络连接是否正常
 *    - Java版本是否兼容（建议Java 8+）
 *
 * 8. 联系方式
 *    如有问题，请联系RMS系统管理员或查看系统文档。
 *
 * ============================================================================
 */
