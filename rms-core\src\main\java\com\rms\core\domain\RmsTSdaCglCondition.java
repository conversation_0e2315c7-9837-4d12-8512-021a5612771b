package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 药品常规用量条件对象 rms_t_sda_cgl_condition
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public class RmsTSdaCglCondition extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 标准药品ID */
    @Excel(name = "标准药品ID")
    private Long sdaId;

    /** 最小年龄（天） */
    @Excel(name = "最小年龄", readConverterExp = "天=")
    private Long ageMin;

    /** 最大年龄（天） */
    @Excel(name = "最大年龄", readConverterExp = "天=")
    private Long ageMax;

    /** 性别（未用） */
    @Excel(name = "性别", readConverterExp = "未=用")
    private Long gender;

    /** 计算类型：0-不按体重计算；1-按体重计算 */
    @Excel(name = "计算类型：0-不按体重计算；1-按体重计算")
    private Long countType;

    /** 最小体重（未用） */
    @Excel(name = "最小体重", readConverterExp = "未=用")
    private Long weightMin;

    /** 最大体重（未用） */
    @Excel(name = "最大体重", readConverterExp = "未=用")
    private Long weightMax;

    /** 给药途径 */
    @Excel(name = "给药途径")
    private String adminRoutine;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setAgeMin(Long ageMin)
    {
        this.ageMin = ageMin;
    }

    public Long getAgeMin()
    {
        return ageMin;
    }

    public void setAgeMax(Long ageMax)
    {
        this.ageMax = ageMax;
    }

    public Long getAgeMax()
    {
        return ageMax;
    }

    public void setGender(Long gender)
    {
        this.gender = gender;
    }

    public Long getGender()
    {
        return gender;
    }

    public void setCountType(Long countType)
    {
        this.countType = countType;
    }

    public Long getCountType()
    {
        return countType;
    }

    public void setWeightMin(Long weightMin)
    {
        this.weightMin = weightMin;
    }

    public Long getWeightMin()
    {
        return weightMin;
    }

    public void setWeightMax(Long weightMax)
    {
        this.weightMax = weightMax;
    }

    public Long getWeightMax()
    {
        return weightMax;
    }

    public void setAdminRoutine(String adminRoutine)
    {
        this.adminRoutine = adminRoutine;
    }

    public String getAdminRoutine()
    {
        return adminRoutine;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sdaId", getSdaId())
            .append("ageMin", getAgeMin())
            .append("ageMax", getAgeMax())
            .append("gender", getGender())
            .append("countType", getCountType())
            .append("weightMin", getWeightMin())
            .append("weightMax", getWeightMax())
            .append("adminRoutine", getAdminRoutine())
            .toString();
    }
}
