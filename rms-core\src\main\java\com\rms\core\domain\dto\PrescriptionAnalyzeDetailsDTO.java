package com.rms.core.domain.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 处方分析详细信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class PrescriptionAnalyzeDetailsDTO {

    /**
     * 是否保存分析结果：0-不保存；1-保存
     */
    @NotNull(message = "是否保存分析结果不能为空")
    private Integer isUpload;

    /**
     * 唯一标识（仅武汉普爱医院卫计委平台用）
     */
    private String guid;

    /**
     * HIS系统时间（YYYY-MM-DD HH:mm:SS）
     */
    @NotBlank(message = "HIS系统时间不能为空")
    private String hisTime;

    /**
     * 门诊/住院标识：op-门诊；ip-住院
     */
    @NotBlank(message = "门诊/住院标识不能为空")
    private String hospFlag;

    /**
     * 就诊类型：100-普通门诊；101-专科门诊；102-专家门诊；200-急诊；300-急诊观察；400-普通住院；401-特需住院；500-家床；999-其他
     */
    @NotNull(message = "就诊类型不能为空")
    private Integer treatType;

    /**
     * 就诊号（医院内唯一）
     */
    @NotBlank(message = "就诊号不能为空")
    private String treatCode;

    /**
     * 检验就诊号
     */
    private String lisAdmNo;

    /**
     * 床位号（住院用）
     */
    private String bedNo;

    /**
     * 病区号（住院用）
     */
    private String areaCode;

    /**
     * 患者信息
     */
    @Valid
    @NotNull(message = "患者信息不能为空")
    private PatientDTO patient;

    /**
     * 处方信息
     */
    @Valid
    @NotEmpty(message = "处方信息不能为空")
    private List<PrescriptionDataDTO> prescriptionData;

    public Integer getIsUpload() {
        return isUpload;
    }

    public void setIsUpload(Integer isUpload) {
        this.isUpload = isUpload;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getHisTime() {
        return hisTime;
    }

    public void setHisTime(String hisTime) {
        this.hisTime = hisTime;
    }

    public String getHospFlag() {
        return hospFlag;
    }

    public void setHospFlag(String hospFlag) {
        this.hospFlag = hospFlag;
    }

    public Integer getTreatType() {
        return treatType;
    }

    public void setTreatType(Integer treatType) {
        this.treatType = treatType;
    }

    public String getTreatCode() {
        return treatCode;
    }

    public void setTreatCode(String treatCode) {
        this.treatCode = treatCode;
    }

    public String getLisAdmNo() {
        return lisAdmNo;
    }

    public void setLisAdmNo(String lisAdmNo) {
        this.lisAdmNo = lisAdmNo;
    }

    public String getBedNo() {
        return bedNo;
    }

    public void setBedNo(String bedNo) {
        this.bedNo = bedNo;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public PatientDTO getPatient() {
        return patient;
    }

    public void setPatient(PatientDTO patient) {
        this.patient = patient;
    }

    public List<PrescriptionDataDTO> getPrescriptionData() {
        return prescriptionData;
    }

    public void setPrescriptionData(List<PrescriptionDataDTO> prescriptionData) {
        this.prescriptionData = prescriptionData;
    }

    @Override
    public String toString() {
        return "PrescriptionAnalyzeDetailsDTO{" +
                "isUpload=" + isUpload +
                ", guid='" + guid + '\'' +
                ", hisTime='" + hisTime + '\'' +
                ", hospFlag='" + hospFlag + '\'' +
                ", treatType=" + treatType +
                ", treatCode='" + treatCode + '\'' +
                ", lisAdmNo='" + lisAdmNo + '\'' +
                ", bedNo='" + bedNo + '\'' +
                ", areaCode='" + areaCode + '\'' +
                ", patient=" + patient +
                ", prescriptionData=" + prescriptionData +
                '}';
    }
}
