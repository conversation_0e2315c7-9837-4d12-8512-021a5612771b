<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaGmMapper">
    
    <resultMap type="RmsTSdaGm" id="RmsTSdaGmResult">
        <result property="id"    column="id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="gmdm"    column="gmdm"    />
        <result property="dmlx"    column="dmlx"    />
        <result property="gmbs"    column="gmbs"    />
        <result property="ispb"    column="ispb"    />
    </resultMap>

    <sql id="selectRmsTSdaGmVo">
        select id, sda_id, gmdm, dmlx, gmbs, ispb from rms_t_sda_gm
    </sql>

    <select id="selectRmsTSdaGmList" parameterType="RmsTSdaGm" resultMap="RmsTSdaGmResult">
        <include refid="selectRmsTSdaGmVo"/>
        <where>  
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="gmdm != null  and gmdm != ''"> and gmdm = #{gmdm}</if>
            <if test="dmlx != null "> and dmlx = #{dmlx}</if>
            <if test="gmbs != null "> and gmbs = #{gmbs}</if>
            <if test="ispb != null  and ispb != ''"> and ispb = #{ispb}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaGmById" parameterType="Long" resultMap="RmsTSdaGmResult">
        <include refid="selectRmsTSdaGmVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaGm" parameterType="RmsTSdaGm">
        insert into rms_t_sda_gm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="sdaId != null">sda_id,</if>
            <if test="gmdm != null">gmdm,</if>
            <if test="dmlx != null">dmlx,</if>
            <if test="gmbs != null">gmbs,</if>
            <if test="ispb != null">ispb,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="sdaId != null">#{sdaId},</if>
            <if test="gmdm != null">#{gmdm},</if>
            <if test="dmlx != null">#{dmlx},</if>
            <if test="gmbs != null">#{gmbs},</if>
            <if test="ispb != null">#{ispb},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaGm" parameterType="RmsTSdaGm">
        update rms_t_sda_gm
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="gmdm != null">gmdm = #{gmdm},</if>
            <if test="dmlx != null">dmlx = #{dmlx},</if>
            <if test="gmbs != null">gmbs = #{gmbs},</if>
            <if test="ispb != null">ispb = #{ispb},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaGmById" parameterType="Long">
        delete from rms_t_sda_gm where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaGmByIds" parameterType="String">
        delete from rms_t_sda_gm where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据sdaId查询药品过敏信息（包含过敏名称） -->
    <select id="selectAllergyRulesWithNameBySdaId" parameterType="Long" resultType="map">
        SELECT 
            sg.id,
            sg.sda_id AS sdaId,
            sg.gmdm,
            sg.dmlx,
            sg.gmbs,
            sg.ispb,
            gb.name AS allergyName,
            gb.code AS allergyCode
        FROM rms_t_sda_gm sg
        LEFT JOIN rms_t_gmbase gb ON sg.gmdm COLLATE utf8mb4_general_ci = gb.code COLLATE utf8mb4_general_ci
        WHERE sg.sda_id = #{sdaId}
        ORDER BY sg.id DESC
    </select>

    <!-- 根据关键字搜索药品过敏信息 -->
    <select id="searchByKeyword" parameterType="String" resultType="map">
        SELECT 
            sg.id,
            sg.sda_id AS sdaId,
            sg.gmdm,
            sg.dmlx,
            sg.gmbs,
            sg.ispb,
            gb.name AS allergyName,
            gb.code AS allergyCode
        FROM rms_t_sda_gm sg
        LEFT JOIN rms_t_gmbase gb ON sg.gmdm COLLATE utf8mb4_general_ci = gb.code COLLATE utf8mb4_general_ci
        WHERE gb.name LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY sg.id DESC
    </select>
</mapper>