package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTTjBaseMapper;
import com.rms.core.domain.RmsTTjBase;
import com.rms.core.service.IRmsTTjBaseService;

/**
 * 给药途径基础表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class RmsTTjBaseServiceImpl implements IRmsTTjBaseService
{
    @Autowired
    private RmsTTjBaseMapper rmsTTjBaseMapper;

    /**
     * 查询给药途径基础表
     *
     * @param name 给药途径基础表主键
     * @return 给药途径基础表
     */
    @Override
    public RmsTTjBase selectRmsTTjBaseByName(String name)
    {
        return rmsTTjBaseMapper.selectRmsTTjBaseByName(name);
    }

    /**
     * 查询给药途径基础表列表
     *
     * @param rmsTTjBase 给药途径基础表
     * @return 给药途径基础表
     */
    @Override
    public List<RmsTTjBase> selectRmsTTjBaseList(RmsTTjBase rmsTTjBase)
    {
        return rmsTTjBaseMapper.selectRmsTTjBaseList(rmsTTjBase);
    }

    /**
     * 新增给药途径基础表
     *
     * @param rmsTTjBase 给药途径基础表
     * @return 结果
     */
    @Override
    public int insertRmsTTjBase(RmsTTjBase rmsTTjBase)
    {
        return rmsTTjBaseMapper.insertRmsTTjBase(rmsTTjBase);
    }

    /**
     * 修改给药途径基础表
     *
     * @param rmsTTjBase 给药途径基础表
     * @return 结果
     */
    @Override
    public int updateRmsTTjBase(RmsTTjBase rmsTTjBase)
    {
        return rmsTTjBaseMapper.updateRmsTTjBase(rmsTTjBase);
    }

    /**
     * 批量删除给药途径基础表
     *
     * @param names 需要删除的给药途径基础表主键
     * @return 结果
     */
    @Override
    public int deleteRmsTTjBaseByNames(String[] names)
    {
        return rmsTTjBaseMapper.deleteRmsTTjBaseByNames(names);
    }

    /**
     * 删除给药途径基础表信息
     *
     * @param name 给药途径基础表主键
     * @return 结果
     */
    @Override
    public int deleteRmsTTjBaseByName(String name)
    {
        return rmsTTjBaseMapper.deleteRmsTTjBaseByName(name);
    }
}
