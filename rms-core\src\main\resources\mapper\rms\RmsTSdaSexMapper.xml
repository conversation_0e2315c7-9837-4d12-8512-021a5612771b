<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaSexMapper">
    
    <resultMap type="RmsTSdaSex" id="RmsTSdaSexResult">
        <result property="id"    column="id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="sex"    column="sex"    />
        <result property="bs"    column="bs"    />
        <result property="ispb"    column="ispb"    />
    </resultMap>

    <sql id="selectRmsTSdaSexVo">
        select id, sda_id, sex, bs, ispb from rms_t_sda_sex
    </sql>

    <select id="selectRmsTSdaSexList" parameterType="RmsTSdaSex" resultMap="RmsTSdaSexResult">
        <include refid="selectRmsTSdaSexVo"/>
        <where>  
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="bs != null  and bs != ''"> and bs = #{bs}</if>
            <if test="ispb != null  and ispb != ''"> and ispb = #{ispb}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaSexById" parameterType="Long" resultMap="RmsTSdaSexResult">
        <include refid="selectRmsTSdaSexVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaSex" parameterType="RmsTSdaSex" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_sda_sex
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">sda_id,</if>
            <if test="sex != null">sex,</if>
            <if test="bs != null">bs,</if>
            <if test="ispb != null">ispb,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">#{sdaId},</if>
            <if test="sex != null">#{sex},</if>
            <if test="bs != null">#{bs},</if>
            <if test="ispb != null">#{ispb},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaSex" parameterType="RmsTSdaSex">
        update rms_t_sda_sex
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="bs != null">bs = #{bs},</if>
            <if test="ispb != null">ispb = #{ispb},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaSexById" parameterType="Long">
        delete from rms_t_sda_sex where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaSexByIds" parameterType="String">
        delete from rms_t_sda_sex where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>