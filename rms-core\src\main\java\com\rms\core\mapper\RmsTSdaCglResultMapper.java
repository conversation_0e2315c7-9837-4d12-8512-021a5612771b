package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSdaCglResult;

/**
 * 药品常规用量规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface RmsTSdaCglResultMapper 
{
    /**
     * 查询药品常规用量规则
     * 
     * @param conditionId 药品常规用量规则主键
     * @return 药品常规用量规则
     */
    public RmsTSdaCglResult selectRmsTSdaCglResultByConditionId(Long conditionId);

    /**
     * 查询药品常规用量规则列表
     * 
     * @param rmsTSdaCglResult 药品常规用量规则
     * @return 药品常规用量规则集合
     */
    public List<RmsTSdaCglResult> selectRmsTSdaCglResultList(RmsTSdaCglResult rmsTSdaCglResult);

    /**
     * 新增药品常规用量规则
     * 
     * @param rmsTSdaCglResult 药品常规用量规则
     * @return 结果
     */
    public int insertRmsTSdaCglResult(RmsTSdaCglResult rmsTSdaCglResult);

    /**
     * 修改药品常规用量规则
     * 
     * @param rmsTSdaCglResult 药品常规用量规则
     * @return 结果
     */
    public int updateRmsTSdaCglResult(RmsTSdaCglResult rmsTSdaCglResult);

    /**
     * 删除药品常规用量规则
     * 
     * @param conditionId 药品常规用量规则主键
     * @return 结果
     */
    public int deleteRmsTSdaCglResultByConditionId(Long conditionId);

    /**
     * 批量删除药品常规用量规则
     * 
     * @param conditionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaCglResultByConditionIds(Long[] conditionIds);
}
