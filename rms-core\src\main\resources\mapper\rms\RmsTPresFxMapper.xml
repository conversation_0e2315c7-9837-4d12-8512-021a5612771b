<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTPresFxMapper">
    
    <resultMap type="RmsTPresFx" id="RmsTPresFxResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="ywa"    column="ywa"    />
        <result property="ywb"    column="ywb"    />
        <result property="wtlvlcode"    column="wtlvlcode"    />
        <result property="wtlvl"    column="wtlvl"    />
        <result property="wtcode"    column="wtcode"    />
        <result property="wtsp"    column="wtsp"    />
        <result property="wtname"    column="wtname"    />
        <result property="title"    column="title"    />
        <result property="detail"    column="detail"    />
        <result property="flag"    column="flag"    />
        <result property="text"    column="text"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRmsTPresFxVo">
        select id, code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text, create_time from rms_t_pres_fx
    </sql>

    <select id="selectRmsTPresFxList" parameterType="RmsTPresFx" resultMap="RmsTPresFxResult">
        <include refid="selectRmsTPresFxVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="ywa != null  and ywa != ''"> and ywa = #{ywa}</if>
            <if test="ywb != null  and ywb != ''"> and ywb = #{ywb}</if>
            <if test="wtlvlcode != null  and wtlvlcode != ''"> and wtlvlcode = #{wtlvlcode}</if>
            <if test="wtlvl != null  and wtlvl != ''"> and wtlvl = #{wtlvl}</if>
            <if test="wtcode != null  and wtcode != ''"> and wtcode = #{wtcode}</if>
            <if test="wtsp != null  and wtsp != ''"> and wtsp = #{wtsp}</if>
            <if test="wtname != null  and wtname != ''"> and wtname like concat('%', #{wtname}, '%')</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="text != null  and text != ''"> and text = #{text}</if>
        </where>
    </select>
    
    <select id="selectRmsTPresFxById" parameterType="Long" resultMap="RmsTPresFxResult">
        <include refid="selectRmsTPresFxVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTPresFx" parameterType="RmsTPresFx" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_pres_fx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="ywa != null">ywa,</if>
            <if test="ywb != null">ywb,</if>
            <if test="wtlvlcode != null">wtlvlcode,</if>
            <if test="wtlvl != null">wtlvl,</if>
            <if test="wtcode != null">wtcode,</if>
            <if test="wtsp != null">wtsp,</if>
            <if test="wtname != null">wtname,</if>
            <if test="title != null">title,</if>
            <if test="detail != null">detail,</if>
            <if test="flag != null">flag,</if>
            <if test="text != null">text,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="ywa != null">#{ywa},</if>
            <if test="ywb != null">#{ywb},</if>
            <if test="wtlvlcode != null">#{wtlvlcode},</if>
            <if test="wtlvl != null">#{wtlvl},</if>
            <if test="wtcode != null">#{wtcode},</if>
            <if test="wtsp != null">#{wtsp},</if>
            <if test="wtname != null">#{wtname},</if>
            <if test="title != null">#{title},</if>
            <if test="detail != null">#{detail},</if>
            <if test="flag != null">#{flag},</if>
            <if test="text != null">#{text},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRmsTPresFx" parameterType="RmsTPresFx">
        update rms_t_pres_fx
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="ywa != null">ywa = #{ywa},</if>
            <if test="ywb != null">ywb = #{ywb},</if>
            <if test="wtlvlcode != null">wtlvlcode = #{wtlvlcode},</if>
            <if test="wtlvl != null">wtlvl = #{wtlvl},</if>
            <if test="wtcode != null">wtcode = #{wtcode},</if>
            <if test="wtsp != null">wtsp = #{wtsp},</if>
            <if test="wtname != null">wtname = #{wtname},</if>
            <if test="title != null">title = #{title},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="text != null">text = #{text},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTPresFxById" parameterType="Long">
        delete from rms_t_pres_fx where id = #{id}
    </delete>

    <delete id="deleteRmsTPresFxByIds" parameterType="String">
        delete from rms_t_pres_fx where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>