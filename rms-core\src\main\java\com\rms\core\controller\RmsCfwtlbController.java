package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsCfwtlb;
import com.rms.core.service.IRmsCfwtlbService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 处方问题类别Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/rms/cfwtlb")
public class RmsCfwtlbController extends BaseController
{
    @Autowired
    private IRmsCfwtlbService rmsCfwtlbService;

    /**
     * 查询处方问题类别列表
     */
    @PreAuthorize("@ss.hasPermi('rms:cfwtlb:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsCfwtlb rmsCfwtlb)
    {
        startPage();
        List<RmsCfwtlb> list = rmsCfwtlbService.selectRmsCfwtlbList(rmsCfwtlb);
        return getDataTable(list);
    }

    /**
     * 导出处方问题类别列表
     */
    @PreAuthorize("@ss.hasPermi('rms:cfwtlb:export')")
    @Log(title = "处方问题类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsCfwtlb rmsCfwtlb)
    {
        List<RmsCfwtlb> list = rmsCfwtlbService.selectRmsCfwtlbList(rmsCfwtlb);
        ExcelUtil<RmsCfwtlb> util = new ExcelUtil<RmsCfwtlb>(RmsCfwtlb.class);
        util.exportExcel(response, list, "处方问题类别数据");
    }

    /**
     * 获取处方问题类别详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:cfwtlb:query')")
    @GetMapping(value = "/{cfwtbh}")
    public AjaxResult getInfo(@PathVariable("cfwtbh") String cfwtbh)
    {
        return success(rmsCfwtlbService.selectRmsCfwtlbByCfwtbh(cfwtbh));
    }

    /**
     * 新增处方问题类别
     */
    @PreAuthorize("@ss.hasPermi('rms:cfwtlb:add')")
    @Log(title = "处方问题类别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsCfwtlb rmsCfwtlb)
    {
        return toAjax(rmsCfwtlbService.insertRmsCfwtlb(rmsCfwtlb));
    }

    /**
     * 修改处方问题类别
     */
    @PreAuthorize("@ss.hasPermi('rms:cfwtlb:edit')")
    @Log(title = "处方问题类别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsCfwtlb rmsCfwtlb)
    {
        return toAjax(rmsCfwtlbService.updateRmsCfwtlb(rmsCfwtlb));
    }

    /**
     * 删除处方问题类别
     */
    @PreAuthorize("@ss.hasPermi('rms:cfwtlb:remove')")
    @Log(title = "处方问题类别", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cfwtbhs}")
    public AjaxResult remove(@PathVariable String[] cfwtbhs)
    {
        return toAjax(rmsCfwtlbService.deleteRmsCfwtlbByCfwtbhs(cfwtbhs));
    }
}
