package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaCglConditionMapper;
import com.rms.core.domain.RmsTSdaCglCondition;
import com.rms.core.service.IRmsTSdaCglConditionService;

/**
 * 药品常规用量条件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class RmsTSdaCglConditionServiceImpl implements IRmsTSdaCglConditionService
{
    @Autowired
    private RmsTSdaCglConditionMapper rmsTSdaCglConditionMapper;

    /**
     * 查询药品常规用量条件
     *
     * @param id 药品常规用量条件主键
     * @return 药品常规用量条件
     */
    @Override
    public RmsTSdaCglCondition selectRmsTSdaCglConditionById(Long id)
    {
        return rmsTSdaCglConditionMapper.selectRmsTSdaCglConditionById(id);
    }

    /**
     * 查询药品常规用量条件列表
     *
     * @param rmsTSdaCglCondition 药品常规用量条件
     * @return 药品常规用量条件
     */
    @Override
    public List<RmsTSdaCglCondition> selectRmsTSdaCglConditionList(RmsTSdaCglCondition rmsTSdaCglCondition)
    {
        return rmsTSdaCglConditionMapper.selectRmsTSdaCglConditionList(rmsTSdaCglCondition);
    }

    /**
     * 新增药品常规用量条件
     *
     * @param rmsTSdaCglCondition 药品常规用量条件
     * @return 结果
     */
    @Override
    public int insertRmsTSdaCglCondition(RmsTSdaCglCondition rmsTSdaCglCondition)
    {
        return rmsTSdaCglConditionMapper.insertRmsTSdaCglCondition(rmsTSdaCglCondition);
    }

    /**
     * 修改药品常规用量条件
     *
     * @param rmsTSdaCglCondition 药品常规用量条件
     * @return 结果
     */
    @Override
    public int updateRmsTSdaCglCondition(RmsTSdaCglCondition rmsTSdaCglCondition)
    {
        return rmsTSdaCglConditionMapper.updateRmsTSdaCglCondition(rmsTSdaCglCondition);
    }

    /**
     * 批量删除药品常规用量条件
     *
     * @param ids 需要删除的药品常规用量条件主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaCglConditionByIds(Long[] ids)
    {
        return rmsTSdaCglConditionMapper.deleteRmsTSdaCglConditionByIds(ids);
    }

    /**
     * 删除药品常规用量条件信息
     *
     * @param id 药品常规用量条件主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaCglConditionById(Long id)
    {
        return rmsTSdaCglConditionMapper.deleteRmsTSdaCglConditionById(id);
    }
}
