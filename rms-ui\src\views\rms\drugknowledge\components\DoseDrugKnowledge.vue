<template>
  <div class="dose-drug-knowledge">
    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button
        type="primary"
        plain
        icon="el-icon-plus"
        size="mini"
        @click="handleAdd"
      >新增</el-button>
      <el-button
        type="danger"
        plain
        icon="el-icon-delete"
        size="mini"
        :disabled="selectedRules.length === 0"
        @click="handleBatchDelete"
      >批量删除</el-button>
    </div>

    <!-- 常规用量规则表格 -->
    <el-table
      v-loading="loading"
      :data="ruleList"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="最小年龄" prop="age_min" width="100" align="center">
        <template slot-scope="scope">
          {{ formatAge(scope.row.age_min) }}
        </template>
      </el-table-column>
      <el-table-column label="最大年龄" prop="age_max" width="100" align="center">
        <template slot-scope="scope">
          {{ formatAge(scope.row.age_max) }}
        </template>
      </el-table-column>
      <el-table-column label="给药途径" prop="admin_routine" width="120" align="center">
        <template slot-scope="scope">
          {{ getRouteText(scope.row.admin_routine) }}
        </template>
      </el-table-column>
      <el-table-column label="用法类型" prop="reco_type" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="getRecoTypeTagType(scope.row.reco_type)"
            size="mini"
          >
            {{ getRecoTypeText(scope.row.reco_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="用量最小值" prop="yl_min" width="100" align="center" />
      <el-table-column label="用量最大值" prop="yl_max" width="100" align="center" />
      <el-table-column label="用量单位" prop="yl_unit" width="100" align="center" />
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" center :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="年龄范围" prop="ageRange">
          <el-row :gutter="8">
            <el-col :span="7">
              <el-select v-model="form.ageMinValue" placeholder="最小年龄" style="width: 100%" @change="validateAgeRange">
                <el-option
                  v-for="item in getAgeOptions(form.ageUnit)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="1" style="text-align: center; line-height: 32px;">-</el-col>
            <el-col :span="7">
              <el-select v-model="form.ageMaxValue" placeholder="最大年龄" style="width: 100%" @change="validateAgeRange">
                <el-option
                  v-for="item in getAgeOptions(form.ageUnit)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="form.ageUnit" placeholder="单位" style="width: 100%" @change="handleUnitChange">
                <el-option label="年" value="year" />
                <el-option label="月" value="month" />
                <el-option label="天" value="day" />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="给药途径" prop="adminRoutine">
          <div class="route-checkbox-container">
            <el-checkbox-group v-model="form.adminRoutine">
              <el-row :gutter="10">
                <el-col :span="6" v-for="item in routeList" :key="item.dm">
                  <el-checkbox :label="item.dm" style="margin-bottom: 0;">
                    {{ item.ms }}
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-form-item label="用法类型" prop="recoType">
          <el-select v-model="form.recoType" placeholder="请选择用法类型" @change="handleRecoTypeChange">
            <el-option label="单日用量" :value="1" />
            <el-option label="每日频次" :value="2" />
            <el-option label="单次用量" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="用量范围" prop="ylRange">
          <el-row :gutter="8">
            <el-col :span="7">
              <el-input-number v-model="form.ylMin" :min="0" :precision="getDosePrecision()" placeholder="最小值" style="width: 100%" @change="validateYlRange" />
            </el-col>
            <el-col :span="1" style="text-align: center; line-height: 32px;">-</el-col>
            <el-col :span="7">
              <el-input-number v-model="form.ylMax" :min="0" :precision="getDosePrecision()" placeholder="最大值" style="width: 100%" @change="validateYlRange" />
            </el-col>
            <el-col :span="6">
              <el-input v-model="form.ylUnit" readonly style="width: 100%; text-align: center;" />
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDoseRules, addDoseRule, updateDoseRule, deleteDoseRule, deleteDoseRules, getRouteBaseList } from "@/api/rms/drugknowledge"

export default {
  name: "DoseDrugKnowledge",
  props: {
    drug: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 规则列表
      ruleList: [],
      // 选中的规则
      selectedRules: [],
      // 对话框标题
      title: "",
      // 是否显示对话框
      open: false,
      // 表单数据
      form: {},
      // 给药途径列表
      routeList: [],
      // 表单验证规则
      rules: {
        ageRange: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.form.ageUnit) {
                callback(new Error('请选择年龄单位'))
              } else if (this.form.ageMinValue === null || this.form.ageMinValue === undefined) {
                callback(new Error('请选择最小年龄'))
              } else if (this.form.ageMaxValue === null || this.form.ageMaxValue === undefined) {
                callback(new Error('请选择最大年龄'))
              } else if (this.form.ageMinValue > this.form.ageMaxValue) {
                callback(new Error('最小年龄不能大于最大年龄'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        adminRoutine: [
          { required: true, message: "给药途径不能为空", trigger: "change" }
        ],
        recoType: [
          { required: true, message: "用法类型不能为空", trigger: "change" }
        ],
        ylRange: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.form.ylMin === null || this.form.ylMin === undefined) {
                callback(new Error('请输入用量最小值'))
              } else if (this.form.ylMax === null || this.form.ylMax === undefined) {
                callback(new Error('请输入用量最大值'))
              } else if (this.form.ylMin > this.form.ylMax) {
                callback(new Error('用量最小值不能大于最大值'))
              } else if (this.form.recoType === 2 && (!Number.isInteger(this.form.ylMin) || !Number.isInteger(this.form.ylMax))) {
                callback(new Error('每日频次的用量必须为整数'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    drug: {
      handler(newVal) {
        if (newVal && newVal.sdaId) {
          this.loadRules()
          // 药品变化时，如果当前不是每日频次，更新用量单位
          if (this.form.recoType !== 2) {
            this.updateDoseUnit()
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.loadRouteList()
  },
  methods: {
    // 加载规则列表
    loadRules() {
      if (!this.drug || !this.drug.sdaId) return
      
      this.loading = true
      getDoseRules(this.drug.sdaId).then(response => {
        this.ruleList = response.data || []
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 加载给药途径列表
    loadRouteList() {
      getRouteBaseList().then(response => {
        this.routeList = response.data || []
      })
    },
    // 获取给药途径文本
    getRouteText(routeCodes) {
      if (!routeCodes) return '-'
      const codes = routeCodes.split('、')
      const names = codes.map(code => {
        const route = this.routeList.find(item => item.dm === code)
        return route ? route.ms : code
      })
      return names.join('/').replace(/^\/+|\/+$/g, '')
    },
    // 获取用法类型文本
    getRecoTypeText(recoType) {
      const typeMap = {
        0: '单次用量',
        1: '单日用量',
        2: '每日频次'
      }
      return typeMap[recoType] || '未知'
    },
    // 获取用法类型标签类型
    getRecoTypeTagType(recoType) {
      const typeMap = {
        0: 'primary',
        1: 'success',
        2: 'warning'
      }
      return typeMap[recoType] || 'info'
    },
    // 获取年龄选项
    getAgeOptions(unit) {
      const options = []
      if (unit === 'year') {
        // 年龄选项：0-150岁
        for (let i = 0; i <= 150; i++) {
          options.push({
            value: i,
            label: i + '岁'
          })
        }
      } else if (unit === 'month') {
        // 月龄选项：0-35月
        for (let i = 0; i <= 35; i++) {
          options.push({
            value: i,
            label: i + '月'
          })
        }
      } else if (unit === 'day') {
        // 天数选项：0-365天
        const dayOptions = [
          0, 1, 2, 3, 4, 5, 6, 7, 14, 21, 28, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330, 365
        ]
        dayOptions.forEach(day => {
          options.push({
            value: day,
            label: day + '天'
          })
        })
      }
      return options
    },
    // 转换为天数
    convertToDays(value, unit) {
      if (unit === 'year') {
        return value * 365
      } else if (unit === 'month') {
        return value * 30
      } else {
        return value
      }
    },
    // 从天数转换为合理的单位
    convertFromDays(days) {
      if (days === 0) {
        return { value: 0, unit: 'day' }
      } else if (days < 60) {
        return { value: days, unit: 'day' }
      } else if (days < 365) {
        const months = Math.round(days / 30)
        return { value: months, unit: 'month' }
      } else {
        const years = Math.round(days / 365)
        return { value: years, unit: 'year' }
      }
    },
    // 格式化年龄显示
    formatAge(days) {
      if (days === 0) {
        return '0天'
      } else if (days < 60) {
        return days + '天'
      } else if (days < 365) {
        const months = Math.round(days / 30)
        return months + '月'
      } else {
        const years = Math.round(days / 365)
        return years + '岁'
      }
    },
    // 为年龄范围选择最合适的单位
    getBestUnitForRange(minDays, maxDays) {
      // 如果最大年龄超过365天（1年），优先使用年
      if (maxDays >= 365) {
        return 'year'
      }
      // 如果最大年龄超过60天（2个月），使用月
      else if (maxDays >= 60) {
        return 'month'
      }
      // 否则使用天
      else {
        return 'day'
      }
    },
    // 将天数转换为指定单位的值
    convertDaysToUnit(days, unit) {
      if (unit === 'year') {
        return Math.round(days / 365)
      } else if (unit === 'month') {
        return Math.round(days / 30)
      } else {
        return days
      }
    },
    // 验证年龄范围
    validateAgeRange() {
      this.$refs.form.validateField('ageRange')
    },
    // 验证用量范围
    validateYlRange() {
      this.$refs.form.validateField('ylRange')
    },
    // 获取用量精度
    getDosePrecision() {
      // 用法类型为每日频次时，用量为整数（精度为0）
      if (this.form.recoType === 2) {
        return 0
      }
      // 其他情况保持2位小数
      return 2
    },
    // 处理单位变化
    handleUnitChange() {
      // 单位变化时清空年龄值
      this.form.ageMinValue = null
      this.form.ageMaxValue = null
      this.validateAgeRange()
    },
    // 处理用法类型变化
    handleRecoTypeChange() {
      this.updateDoseUnit()
      // 如果切换到每日频次，需要将用量值转换为整数
      if (this.form.recoType === 2) {
        if (this.form.ylMin !== null && this.form.ylMin !== undefined) {
          this.form.ylMin = Math.round(this.form.ylMin)
        }
        if (this.form.ylMax !== null && this.form.ylMax !== undefined) {
          this.form.ylMax = Math.round(this.form.ylMax)
        }
      }
    },
    // 更新用量单位
    updateDoseUnit() {
      if (this.form.recoType === 2) {
        // 每日频次
        this.form.ylUnit = '次'
      } else {
        // 单次用量或单日用量，使用药品单位
        this.form.ylUnit = this.getDrugUnit()
      }
    },
    // 获取药品单位
    getDrugUnit() {
      // 优先使用 UnitRem 字段作为单位
      if (this.drug && this.drug.unitRem) {
        return this.drug.unitRem
      } else {
        return ''
      }
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRules = selection
    },
    // 新增
    handleAdd() {
      this.reset()
      this.updateDoseUnit() // 设置默认用量单位
      this.open = true
      this.title = "添加常规用量规则"
    },
    // 修改
    handleUpdate(row) {
      this.reset()
      // 选择最合适的单位来显示年龄范围
      const bestUnit = this.getBestUnitForRange(row.age_min, row.age_max)
      
      this.form = {
        id: row.id,
        ageUnit: bestUnit,
        ageMinValue: this.convertDaysToUnit(row.age_min, bestUnit),
        ageMaxValue: this.convertDaysToUnit(row.age_max, bestUnit),
        adminRoutine: row.admin_routine ? row.admin_routine.split('、') : [],
        recoType: row.reco_type,
        ylMin: row.yl_min,
        ylMax: row.yl_max,
        ylRange: null, // 用于验证
        ylUnit: row.yl_unit
      }
      // 编辑时保持原有的用量单位，不自动更新
      this.open = true
      this.title = "修改常规用量规则"
    },
    // 删除
    handleDelete(row) {
      this.$modal.confirm('是否确认删除该条常规用量规则？').then(() => {
        return deleteDoseRule(row.id)
      }).then(() => {
        this.loadRules()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    // 批量删除
    handleBatchDelete() {
      const ids = this.selectedRules.map(item => item.id)
      this.$modal.confirm('是否确认删除选中的常规用量规则？').then(() => {
        return deleteDoseRules(ids)
      }).then(() => {
        this.loadRules()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 将年龄转换为天数并转换字段名为后端期望的格式
          const ageMinDays = this.convertToDays(this.form.ageMinValue, this.form.ageUnit)
          const ageMaxDays = this.convertToDays(this.form.ageMaxValue, this.form.ageUnit)
          
          const formData = {
            id: this.form.id,
            sdaId: this.drug.sdaId,
            ageMin: ageMinDays,
            ageMax: ageMaxDays,
            adminRoutine: Array.isArray(this.form.adminRoutine) ? this.form.adminRoutine.join('、') : this.form.adminRoutine,
            recoType: this.form.recoType,
            ylMin: this.form.ylMin,
            ylMax: this.form.ylMax,
            ylUnit: this.form.ylUnit
          }
          
          if (this.form.id != null) {
            updateDoseRule(formData).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.loadRules()
            })
          } else {
            addDoseRule(formData).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.loadRules()
            })
          }
        }
      })
    },
    // 取消
    cancel() {
      this.open = false
      this.reset()
    },
    // 重置表单
    reset() {
      this.form = {
        id: null,
        ageUnit: 'day',
        ageMinValue: null,
        ageMaxValue: null,
        ageRange: null, // 用于验证
        adminRoutine: [],
        recoType: null,
        ylMin: null,
        ylMax: null,
        ylRange: null, // 用于验证
        ylUnit: this.getDrugUnit() // 默认使用药品单位
      }
      this.resetForm("form")
    },
    // 重置表单
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    }
  }
}
</script>

<style scoped>
.dose-drug-knowledge {
  padding: 20px 0;
}

.toolbar {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: center;
}

.route-checkbox-container {
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  padding: 15px;
  background-color: #FAFAFA;
  min-height: 120px;
  max-height: 200px;
  overflow-y: auto;
}

.route-checkbox-container .el-checkbox {
  width: 100%;
  margin-right: 0;
}

.route-checkbox-container .el-checkbox__label {
  padding-left: 8px;
}

.el-input.is-disabled .el-input__inner, 
.el-input[readonly] .el-input__inner {
  background-color: #F5F7FA;
  border-color: #E4E7ED;
  color: #606266;
  cursor: not-allowed;
}
</style> 