import request from '@/utils/request'

// 查询药品过敏信息列表
export function listTsdagm(query) {
  return request({
    url: '/rms/tsdagm/list',
    method: 'get',
    params: query
  })
}

// 查询药品过敏信息详细
export function getTsdagm(id) {
  return request({
    url: '/rms/tsdagm/' + id,
    method: 'get'
  })
}

// 新增药品过敏信息
export function addTsdagm(data) {
  return request({
    url: '/rms/tsdagm',
    method: 'post',
    data: data
  })
}

// 修改药品过敏信息
export function updateTsdagm(data) {
  return request({
    url: '/rms/tsdagm',
    method: 'put',
    data: data
  })
}

// 删除药品过敏信息
export function delTsdagm(id) {
  return request({
    url: '/rms/tsdagm/' + id,
    method: 'delete'
  })
}
