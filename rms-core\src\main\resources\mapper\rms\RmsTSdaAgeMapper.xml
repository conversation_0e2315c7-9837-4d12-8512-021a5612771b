<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaAgeMapper">
    
    <resultMap type="RmsTSdaAge" id="RmsTSdaAgeResult">
        <result property="id"    column="id"    />
        <result property="agetype"    column="agetype"    />
        <result property="age"    column="age"    />
    </resultMap>

    <sql id="selectRmsTSdaAgeVo">
        select id, agetype, age from rms_t_sda_age
    </sql>

    <select id="selectRmsTSdaAgeList" parameterType="RmsTSdaAge" resultMap="RmsTSdaAgeResult">
        <include refid="selectRmsTSdaAgeVo"/>
        <where>  
            <if test="agetype != null  and agetype != ''"> and agetype = #{agetype}</if>
            <if test="age != null "> and age = #{age}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaAgeById" parameterType="Long" resultMap="RmsTSdaAgeResult">
        <include refid="selectRmsTSdaAgeVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaAge" parameterType="RmsTSdaAge">
        insert into rms_t_sda_age
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="agetype != null">agetype,</if>
            <if test="age != null">age,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="agetype != null">#{agetype},</if>
            <if test="age != null">#{age},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaAge" parameterType="RmsTSdaAge">
        update rms_t_sda_age
        <trim prefix="SET" suffixOverrides=",">
            <if test="agetype != null">agetype = #{agetype},</if>
            <if test="age != null">age = #{age},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaAgeById" parameterType="Long">
        delete from rms_t_sda_age where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaAgeByIds" parameterType="String">
        delete from rms_t_sda_age where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>