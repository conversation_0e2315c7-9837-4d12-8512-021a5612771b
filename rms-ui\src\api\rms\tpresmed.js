import request from '@/utils/request'

// 查询处方明细信息列表
export function listTpresmed(query) {
  return request({
    url: '/rms/tpresmed/list',
    method: 'get',
    params: query
  })
}

// 查询处方明细信息详细
export function getTpresmed(code) {
  return request({
    url: '/rms/tpresmed/' + code,
    method: 'get'
  })
}

// 新增处方明细信息
export function addTpresmed(data) {
  return request({
    url: '/rms/tpresmed',
    method: 'post',
    data: data
  })
}

// 修改处方明细信息
export function updateTpresmed(data) {
  return request({
    url: '/rms/tpresmed',
    method: 'put',
    data: data
  })
}

// 删除处方明细信息
export function delTpresmed(code) {
  return request({
    url: '/rms/tpresmed/' + code,
    method: 'delete'
  })
}
