package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.RmsTPresMed;

/**
 * 处方明细信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface IRmsTPresMedService 
{
    /**
     * 查询处方明细信息
     * 
     * @param code 处方明细信息主键
     * @return 处方明细信息
     */
    public RmsTPresMed selectRmsTPresMedByCode(String code);

    /**
     * 查询处方明细信息列表
     * 
     * @param rmsTPresMed 处方明细信息
     * @return 处方明细信息集合
     */
    public List<RmsTPresMed> selectRmsTPresMedList(RmsTPresMed rmsTPresMed);

    /**
     * 新增处方明细信息
     * 
     * @param rmsTPresMed 处方明细信息
     * @return 结果
     */
    public int insertRmsTPresMed(RmsTPresMed rmsTPresMed);

    /**
     * 修改处方明细信息
     * 
     * @param rmsTPresMed 处方明细信息
     * @return 结果
     */
    public int updateRmsTPresMed(RmsTPresMed rmsTPresMed);

    /**
     * 批量删除处方明细信息
     * 
     * @param codes 需要删除的处方明细信息主键集合
     * @return 结果
     */
    public int deleteRmsTPresMedByCodes(String[] codes);

    /**
     * 删除处方明细信息信息
     * 
     * @param code 处方明细信息主键
     * @return 结果
     */
    public int deleteRmsTPresMedByCode(String code);
}
