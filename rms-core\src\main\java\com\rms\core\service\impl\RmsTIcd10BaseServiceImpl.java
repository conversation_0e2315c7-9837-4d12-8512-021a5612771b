package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTIcd10BaseMapper;
import com.rms.core.domain.RmsTIcd10Base;
import com.rms.core.service.IRmsTIcd10BaseService;

/**
 * 诊断基础信息表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class RmsTIcd10BaseServiceImpl implements IRmsTIcd10BaseService
{
    @Autowired
    private RmsTIcd10BaseMapper rmsTIcd10BaseMapper;

    /**
     * 查询诊断基础信息表
     *
     * @param id 诊断基础信息表主键
     * @return 诊断基础信息表
     */
    @Override
    public RmsTIcd10Base selectRmsTIcd10BaseById(Long id)
    {
        return rmsTIcd10BaseMapper.selectRmsTIcd10BaseById(id);
    }

    /**
     * 查询诊断基础信息表列表
     *
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 诊断基础信息表
     */
    @Override
    public List<RmsTIcd10Base> selectRmsTIcd10BaseList(RmsTIcd10Base rmsTIcd10Base)
    {
        return rmsTIcd10BaseMapper.selectRmsTIcd10BaseList(rmsTIcd10Base);
    }

    /**
     * 新增诊断基础信息表
     *
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 结果
     */
    @Override
    public int insertRmsTIcd10Base(RmsTIcd10Base rmsTIcd10Base)
    {
        return rmsTIcd10BaseMapper.insertRmsTIcd10Base(rmsTIcd10Base);
    }

    /**
     * 修改诊断基础信息表
     *
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 结果
     */
    @Override
    public int updateRmsTIcd10Base(RmsTIcd10Base rmsTIcd10Base)
    {
        return rmsTIcd10BaseMapper.updateRmsTIcd10Base(rmsTIcd10Base);
    }

    /**
     * 批量删除诊断基础信息表
     *
     * @param ids 需要删除的诊断基础信息表主键
     * @return 结果
     */
    @Override
    public int deleteRmsTIcd10BaseByIds(Long[] ids)
    {
        return rmsTIcd10BaseMapper.deleteRmsTIcd10BaseByIds(ids);
    }

    /**
     * 删除诊断基础信息表信息
     *
     * @param id 诊断基础信息表主键
     * @return 结果
     */
    @Override
    public int deleteRmsTIcd10BaseById(Long id)
    {
        return rmsTIcd10BaseMapper.deleteRmsTIcd10BaseById(id);
    }
}
