<template>
  <div class="allergy-drug-knowledge">
    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button
        type="primary"
        plain
        icon="el-icon-plus"
        size="mini"
        @click="handleAdd"
      >添加过敏信息</el-button>
      <el-button
        type="danger"
        plain
        icon="el-icon-delete"
        size="mini"
        :disabled="selectedRules.length === 0"
        @click="handleBatchDelete"
      >批量删除</el-button>
    </div>

    <!-- 过敏信息表格 -->
    <el-table
      v-loading="loading"
      :data="allergyList"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="过敏代码" prop="gmdm" width="140" align="center" />
      <el-table-column label="过敏名称" prop="allergyName" show-overflow-tooltip />
      <el-table-column label="过敏标识" prop="gmbs" width="120" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.gmbs === 0 ? 'danger' : 'warning'"
            size="mini"
          >
            {{ scope.row.gmbs === 0 ? '过敏禁用' : '过敏慎用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否屏蔽" prop="ispb" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.ispb === '1' ? 'danger' : 'success'"
            size="mini"
          >
            {{ scope.row.ispb === '1' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
      <template slot="empty">
        <div class="empty-content">
          <i class="el-icon-info" style="font-size: 48px; color: #ddd;"></i>
          <p style="color: #999; margin-top: 20px;">暂无过敏信息</p>
        </div>
      </template>
    </el-table>

    <!-- 添加/修改过敏信息对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="small"
      >
        <el-form-item label="过敏信息" prop="gmdm">
          <el-input
            v-model="allergyKeyword"
            placeholder="请输入过敏名称进行搜索"
            clearable
            @input="handleAllergySearch"
          />
          <el-select
            v-model="form.gmdm"
            placeholder="请选择过敏信息"
            style="width: 100%; margin-top: 5px;"
            clearable
            filterable
            @change="handleAllergyChange"
          >
            <el-option
              v-for="item in allergyOptions"
              :key="item.code"
              :label="item.name + (item.sp ? ' (' + item.sp + ')' : '')"
              :value="item.code"
            >
              <span>{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 12px;">{{ item.sp }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="过敏标识" prop="gmbs">
          <el-select v-model="form.gmbs" placeholder="请选择过敏标识">
            <el-option label="过敏禁用" :value="0" />
            <el-option label="过敏慎用" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否屏蔽" prop="ispb">
          <el-radio-group v-model="form.ispb">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllergyRules, addAllergyRule, updateAllergyRule, deleteAllergyRule, deleteAllergyRules, getAllergyBaseList, searchAllergy, searchAllergyBase } from "@/api/rms/drugknowledge"

export default {
  name: "AllergyDrugKnowledge",
  props: {
    drug: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 过敏信息列表
      allergyList: [],
      // 选中的过敏规则
      selectedRules: [],
      // 对话框显示状态
      dialogVisible: false,
      // 对话框标题
      dialogTitle: '添加过敏信息',
      // 表单数据
      form: {
        id: null,
        sdaId: null,
        gmdm: '',
        gmbs: 0,
        ispb: '0',
        remark: ''
      },
      // 表单验证规则
      rules: {
        gmdm: [
          { required: true, message: '请选择过敏信息', trigger: 'change' }
        ],
        gmbs: [
          { required: true, message: '请选择过敏标识', trigger: 'change' }
        ],
        ispb: [
          { required: true, message: '请选择是否屏蔽', trigger: 'change' }
        ]
      },
      // 过敏信息搜索关键字
      allergyKeyword: '',
      // 过敏信息选项
      allergyOptions: [],
      // 所有过敏基础数据
      allAllergyBase: []
    }
  },
  watch: {
    drug: {
      handler(newVal) {
        if (newVal && newVal.sdaId) {
          this.loadAllergyRules()
        }
      },
      immediate: true
    }
  },
  created() {
    this.loadAllergyBaseList()
  },
  methods: {
    // 加载过敏规则列表
    loadAllergyRules() {
      if (!this.drug || !this.drug.sdaId) return
      
      this.loading = true
      getAllergyRules(this.drug.sdaId).then(response => {
        this.allergyList = response.data || []
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 加载过敏基础数据列表
    loadAllergyBaseList() {
      getAllergyBaseList().then(response => {
        this.allAllergyBase = response.data || []
        this.allergyOptions = this.allAllergyBase
      }).catch(() => {
        this.allAllergyBase = []
        this.allergyOptions = []
      })
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedRules = selection
    },

    // 添加过敏信息
    handleAdd() {
      this.dialogTitle = '添加过敏信息'
      this.form = {
        id: null,
        sdaId: this.drug.sdaId,
        gmdm: '',
        gmbs: 0,
        ispb: '0',
        remark: ''
      }
      this.allergyKeyword = ''
      this.allergyOptions = this.allAllergyBase
      this.dialogVisible = true
    },

    // 修改过敏信息
    handleEdit(row) {
      this.dialogTitle = '修改过敏信息'
      this.form = { ...row }
      this.allergyKeyword = row.allergyName || ''
      this.allergyOptions = this.allAllergyBase
      this.dialogVisible = true
    },

    // 删除过敏信息
    handleDelete(row) {
      const allergyName = row.allergyName || row.gmdm
      this.$modal.confirm('是否确认删除过敏信息"' + allergyName + '"？').then(() => {
        return deleteAllergyRule(row.id)
      }).then(() => {
        this.loadAllergyRules()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },

    // 批量删除过敏信息
    handleBatchDelete() {
      const ids = this.selectedRules.map(item => item.id)
      this.$modal.confirm('是否确认删除选中的' + ids.length + '条过敏信息？').then(() => {
        return deleteAllergyRules(ids)
      }).then(() => {
        this.loadAllergyRules()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },

    // 搜索过敏信息
    handleAllergySearch() {
      if (!this.allergyKeyword.trim()) {
        this.allergyOptions = this.allAllergyBase
        return
      }
      
      if (this.allergyKeyword.length < 2) {
        return
      }
      
      searchAllergyBase(this.allergyKeyword).then(response => {
        this.allergyOptions = response.data || []
      }).catch(() => {
        this.allergyOptions = []
      })
    },

    // 过敏信息选择变化
    handleAllergyChange(value) {
      const selectedAllergy = this.allergyOptions.find(item => item.code === value)
      if (selectedAllergy) {
        this.allergyKeyword = selectedAllergy.name
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const submitData = { ...this.form }
          
          if (submitData.id) {
            // 修改
            updateAllergyRule(submitData).then(() => {
              this.$modal.msgSuccess("修改成功")
              this.dialogVisible = false
              this.loadAllergyRules()
            })
          } else {
            // 新增
            addAllergyRule(submitData).then(() => {
              this.$modal.msgSuccess("新增成功")
              this.dialogVisible = false
              this.loadAllergyRules()
            })
          }
        }
      })
    },

    // 关闭对话框
    handleDialogClose() {
      this.dialogVisible = false
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
      this.form = {
        id: null,
        sdaId: this.drug.sdaId,
        gmdm: '',
        gmbs: 0,
        ispb: '0',
        remark: ''
      }
      this.allergyKeyword = ''
      this.allergyOptions = this.allAllergyBase
    }
  }
}
</script>

<style scoped>
.allergy-drug-knowledge {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.empty-content {
  text-align: center;
  padding: 60px 0;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style> 