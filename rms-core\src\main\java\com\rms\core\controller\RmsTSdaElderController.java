package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaElder;
import com.rms.core.service.IRmsTSdaElderService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 老年人用药规则库Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/rms/tsdaelder")
public class RmsTSdaElderController extends BaseController
{
    @Autowired
    private IRmsTSdaElderService rmsTSdaElderService;

    /**
     * 查询老年人用药规则库列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaelder:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaElder rmsTSdaElder)
    {
        startPage();
        List<RmsTSdaElder> list = rmsTSdaElderService.selectRmsTSdaElderList(rmsTSdaElder);
        return getDataTable(list);
    }

    /**
     * 导出老年人用药规则库列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaelder:export')")
    @Log(title = "老年人用药规则库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaElder rmsTSdaElder)
    {
        List<RmsTSdaElder> list = rmsTSdaElderService.selectRmsTSdaElderList(rmsTSdaElder);
        ExcelUtil<RmsTSdaElder> util = new ExcelUtil<RmsTSdaElder>(RmsTSdaElder.class);
        util.exportExcel(response, list, "老年人用药规则库数据");
    }

    /**
     * 获取老年人用药规则库详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaelder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaElderService.selectRmsTSdaElderById(id));
    }

    /**
     * 新增老年人用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaelder:add')")
    @Log(title = "老年人用药规则库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaElder rmsTSdaElder)
    {
        return toAjax(rmsTSdaElderService.insertRmsTSdaElder(rmsTSdaElder));
    }

    /**
     * 修改老年人用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaelder:edit')")
    @Log(title = "老年人用药规则库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaElder rmsTSdaElder)
    {
        return toAjax(rmsTSdaElderService.updateRmsTSdaElder(rmsTSdaElder));
    }

    /**
     * 删除老年人用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaelder:remove')")
    @Log(title = "老年人用药规则库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaElderService.deleteRmsTSdaElderByIds(ids));
    }
}
