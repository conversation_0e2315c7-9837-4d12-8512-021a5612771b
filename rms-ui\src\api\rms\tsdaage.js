import request from '@/utils/request'

// 查询年龄代码列表
export function listTsdaage(query) {
  return request({
    url: '/rms/tsdaage/list',
    method: 'get',
    params: query
  })
}

// 查询年龄代码详细
export function getTsdaage(id) {
  return request({
    url: '/rms/tsdaage/' + id,
    method: 'get'
  })
}

// 新增年龄代码
export function addTsdaage(data) {
  return request({
    url: '/rms/tsdaage',
    method: 'post',
    data: data
  })
}

// 修改年龄代码
export function updateTsdaage(data) {
  return request({
    url: '/rms/tsdaage',
    method: 'put',
    data: data
  })
}

// 删除年龄代码
export function delTsdaage(id) {
  return request({
    url: '/rms/tsdaage/' + id,
    method: 'delete'
  })
}
