package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTXhzyEdi;
import com.rms.core.service.IRmsTXhzyEdiService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 药品相互作用Controller
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/rms/txhzyedi")
public class RmsTXhzyEdiController extends BaseController
{
    @Autowired
    private IRmsTXhzyEdiService rmsTXhzyEdiService;

    /**
     * 查询药品相互作用列表
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedi:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTXhzyEdi rmsTXhzyEdi)
    {
        startPage();
        List<RmsTXhzyEdi> list = rmsTXhzyEdiService.selectRmsTXhzyEdiList(rmsTXhzyEdi);
        return getDataTable(list);
    }

    /**
     * 导出药品相互作用列表
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedi:export')")
    @Log(title = "药品相互作用", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTXhzyEdi rmsTXhzyEdi)
    {
        List<RmsTXhzyEdi> list = rmsTXhzyEdiService.selectRmsTXhzyEdiList(rmsTXhzyEdi);
        ExcelUtil<RmsTXhzyEdi> util = new ExcelUtil<RmsTXhzyEdi>(RmsTXhzyEdi.class);
        util.exportExcel(response, list, "药品相互作用数据");
    }

    /**
     * 获取药品相互作用详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedi:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTXhzyEdiService.selectRmsTXhzyEdiById(id));
    }

    /**
     * 新增药品相互作用
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedi:add')")
    @Log(title = "药品相互作用", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTXhzyEdi rmsTXhzyEdi)
    {
        return toAjax(rmsTXhzyEdiService.insertRmsTXhzyEdi(rmsTXhzyEdi));
    }

    /**
     * 修改药品相互作用
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedi:edit')")
    @Log(title = "药品相互作用", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTXhzyEdi rmsTXhzyEdi)
    {
        return toAjax(rmsTXhzyEdiService.updateRmsTXhzyEdi(rmsTXhzyEdi));
    }

    /**
     * 删除药品相互作用
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedi:remove')")
    @Log(title = "药品相互作用", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTXhzyEdiService.deleteRmsTXhzyEdiByIds(ids));
    }
}
