package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.RmsTXhzyEdi;

/**
 * 药品相互作用Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IRmsTXhzyEdiService 
{
    /**
     * 查询药品相互作用
     * 
     * @param id 药品相互作用主键
     * @return 药品相互作用
     */
    public RmsTXhzyEdi selectRmsTXhzyEdiById(Long id);

    /**
     * 查询药品相互作用列表
     * 
     * @param rmsTXhzyEdi 药品相互作用
     * @return 药品相互作用集合
     */
    public List<RmsTXhzyEdi> selectRmsTXhzyEdiList(RmsTXhzyEdi rmsTXhzyEdi);

    /**
     * 新增药品相互作用
     * 
     * @param rmsTXhzyEdi 药品相互作用
     * @return 结果
     */
    public int insertRmsTXhzyEdi(RmsTXhzyEdi rmsTXhzyEdi);

    /**
     * 修改药品相互作用
     * 
     * @param rmsTXhzyEdi 药品相互作用
     * @return 结果
     */
    public int updateRmsTXhzyEdi(RmsTXhzyEdi rmsTXhzyEdi);

    /**
     * 批量删除药品相互作用
     * 
     * @param ids 需要删除的药品相互作用主键集合
     * @return 结果
     */
    public int deleteRmsTXhzyEdiByIds(Long[] ids);

    /**
     * 删除药品相互作用信息
     * 
     * @param id 药品相互作用主键
     * @return 结果
     */
    public int deleteRmsTXhzyEdiById(Long id);
}
