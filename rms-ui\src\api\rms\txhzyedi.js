import request from '@/utils/request'

// 查询药品相互作用列表
export function listTxhzyedi(query) {
  return request({
    url: '/rms/txhzyedi/list',
    method: 'get',
    params: query
  })
}

// 查询药品相互作用详细
export function getTxhzyedi(id) {
  return request({
    url: '/rms/txhzyedi/' + id,
    method: 'get'
  })
}

// 新增药品相互作用
export function addTxhzyedi(data) {
  return request({
    url: '/rms/txhzyedi',
    method: 'post',
    data: data
  })
}

// 修改药品相互作用
export function updateTxhzyedi(data) {
  return request({
    url: '/rms/txhzyedi',
    method: 'put',
    data: data
  })
}

// 删除药品相互作用
export function delTxhzyedi(id) {
  return request({
    url: '/rms/txhzyedi/' + id,
    method: 'delete'
  })
}
