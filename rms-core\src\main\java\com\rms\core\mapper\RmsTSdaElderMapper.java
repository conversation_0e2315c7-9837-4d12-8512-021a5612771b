package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSdaElder;

/**
 * 老年人用药规则库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface RmsTSdaElderMapper 
{
    /**
     * 查询老年人用药规则库
     * 
     * @param id 老年人用药规则库主键
     * @return 老年人用药规则库
     */
    public RmsTSdaElder selectRmsTSdaElderById(Long id);

    /**
     * 查询老年人用药规则库列表
     * 
     * @param rmsTSdaElder 老年人用药规则库
     * @return 老年人用药规则库集合
     */
    public List<RmsTSdaElder> selectRmsTSdaElderList(RmsTSdaElder rmsTSdaElder);

    /**
     * 新增老年人用药规则库
     * 
     * @param rmsTSdaElder 老年人用药规则库
     * @return 结果
     */
    public int insertRmsTSdaElder(RmsTSdaElder rmsTSdaElder);

    /**
     * 修改老年人用药规则库
     * 
     * @param rmsTSdaElder 老年人用药规则库
     * @return 结果
     */
    public int updateRmsTSdaElder(RmsTSdaElder rmsTSdaElder);

    /**
     * 删除老年人用药规则库
     * 
     * @param id 老年人用药规则库主键
     * @return 结果
     */
    public int deleteRmsTSdaElderById(Long id);

    /**
     * 批量删除老年人用药规则库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaElderByIds(Long[] ids);
}
