import request from '@/utils/request'

// 查询标准药品性别库列表
export function listTsdasex(query) {
  return request({
    url: '/rms/tsdasex/list',
    method: 'get',
    params: query
  })
}

// 查询标准药品性别库详细
export function getTsdasex(id) {
  return request({
    url: '/rms/tsdasex/' + id,
    method: 'get'
  })
}

// 新增标准药品性别库
export function addTsdasex(data) {
  return request({
    url: '/rms/tsdasex',
    method: 'post',
    data: data
  })
}

// 修改标准药品性别库
export function updateTsdasex(data) {
  return request({
    url: '/rms/tsdasex',
    method: 'put',
    data: data
  })
}

// 删除标准药品性别库
export function delTsdasex(id) {
  return request({
    url: '/rms/tsdasex/' + id,
    method: 'delete'
  })
}

// 根据标准数据ID查询性别用药规则
export function getSexDrugRules(sdaId) {
  return request({
    url: '/rms/drugknowledge/sexRules/' + sdaId,
    method: 'get'
  })
}

// 新增性别用药规则
export function addSexDrugRule(data) {
  return request({
    url: '/rms/drugknowledge/sexRules',
    method: 'post',
    data: data
  })
}

// 修改性别用药规则
export function updateSexDrugRule(data) {
  return request({
    url: '/rms/drugknowledge/sexRules',
    method: 'put',
    data: data
  })
}

// 删除性别用药规则
export function deleteSexDrugRule(id) {
  return request({
    url: '/rms/drugknowledge/sexRules/' + id,
    method: 'delete'
  })
}

// 批量删除性别用药规则
export function deleteSexDrugRules(ids) {
  return request({
    url: '/rms/drugknowledge/sexRules',
    method: 'delete',
    data: ids
  })
}
