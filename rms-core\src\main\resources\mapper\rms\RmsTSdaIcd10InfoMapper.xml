<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaIcd10InfoMapper">
    
    <resultMap type="RmsTSdaIcd10Info" id="RmsTSdaIcd10InfoResult">
        <result property="id"    column="id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="diagnoses"    column="diagnoses"    />
        <result property="expand"    column="expand"    />
        <result property="bs"    column="bs"    />
        <result property="gzd"    column="gzd"    />
        <result property="pri"    column="pri"    />
        <result property="remark"    column="remark"    />
        <result property="illhis"    column="illhis"    />
        <result property="sdaIcd10StencilExpand"    column="sda_icd10_stencil_expand"    />
        <result property="ispb"    column="ispb"    />
        <result property="editTime"    column="edit_time"    />
        <result property="editUser"    column="edit_user"    />
    </resultMap>

    <sql id="selectRmsTSdaIcd10InfoVo">
        select id, sda_id, diagnoses, expand, bs, gzd, pri, remark, illhis, sda_icd10_stencil_expand, ispb, edit_time, edit_user from rms_t_sda_icd10_info
    </sql>

    <select id="selectRmsTSdaIcd10InfoList" parameterType="RmsTSdaIcd10Info" resultMap="RmsTSdaIcd10InfoResult">
        <include refid="selectRmsTSdaIcd10InfoVo"/>
        <where>  
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="diagnoses != null  and diagnoses != ''"> and diagnoses = #{diagnoses}</if>
            <if test="expand != null "> and expand = #{expand}</if>
            <if test="bs != null "> and bs = #{bs}</if>
            <if test="gzd != null "> and gzd = #{gzd}</if>
            <if test="pri != null  and pri != ''"> and pri = #{pri}</if>
            <if test="illhis != null  and illhis != ''"> and illhis = #{illhis}</if>
            <if test="sdaIcd10StencilExpand != null "> and sda_icd10_stencil_expand = #{sdaIcd10StencilExpand}</if>
            <if test="ispb != null  and ispb != ''"> and ispb = #{ispb}</if>
            <if test="editTime != null  and editTime != ''"> and edit_time = #{editTime}</if>
            <if test="editUser != null  and editUser != ''"> and edit_user = #{editUser}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaIcd10InfoById" parameterType="Long" resultMap="RmsTSdaIcd10InfoResult">
        <include refid="selectRmsTSdaIcd10InfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaIcd10Info" parameterType="RmsTSdaIcd10Info" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_sda_icd10_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">sda_id,</if>
            <if test="diagnoses != null">diagnoses,</if>
            <if test="expand != null">expand,</if>
            <if test="bs != null">bs,</if>
            <if test="gzd != null">gzd,</if>
            <if test="pri != null">pri,</if>
            <if test="remark != null">remark,</if>
            <if test="illhis != null">illhis,</if>
            <if test="sdaIcd10StencilExpand != null">sda_icd10_stencil_expand,</if>
            <if test="ispb != null">ispb,</if>
            <if test="editTime != null">edit_time,</if>
            <if test="editUser != null">edit_user,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">#{sdaId},</if>
            <if test="diagnoses != null">#{diagnoses},</if>
            <if test="expand != null">#{expand},</if>
            <if test="bs != null">#{bs},</if>
            <if test="gzd != null">#{gzd},</if>
            <if test="pri != null">#{pri},</if>
            <if test="remark != null">#{remark},</if>
            <if test="illhis != null">#{illhis},</if>
            <if test="sdaIcd10StencilExpand != null">#{sdaIcd10StencilExpand},</if>
            <if test="ispb != null">#{ispb},</if>
            <if test="editTime != null">#{editTime},</if>
            <if test="editUser != null">#{editUser},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaIcd10Info" parameterType="RmsTSdaIcd10Info">
        update rms_t_sda_icd10_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="diagnoses != null">diagnoses = #{diagnoses},</if>
            <if test="expand != null">expand = #{expand},</if>
            <if test="bs != null">bs = #{bs},</if>
            <if test="gzd != null">gzd = #{gzd},</if>
            <if test="pri != null">pri = #{pri},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="illhis != null">illhis = #{illhis},</if>
            <if test="sdaIcd10StencilExpand != null">sda_icd10_stencil_expand = #{sdaIcd10StencilExpand},</if>
            <if test="ispb != null">ispb = #{ispb},</if>
            <if test="editTime != null">edit_time = #{editTime},</if>
            <if test="editUser != null">edit_user = #{editUser},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaIcd10InfoById" parameterType="Long">
        delete from rms_t_sda_icd10_info where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaIcd10InfoByIds" parameterType="String">
        delete from rms_t_sda_icd10_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 诊断规则结果映射 -->
    <resultMap type="com.rms.core.domain.DiagnosisRuleResult" id="DiagnosisRuleResult">
        <result property="id" column="id" />
        <result property="sdaId" column="sda_id" />
        <result property="diagnoses" column="diagnoses" />
        <result property="bs" column="bs" />
        <result property="ispb" column="ispb" />
        <result property="remark" column="remark" />
        <result property="icdCode" column="icd_code" />
        <result property="icdName" column="icd_name" />
        <result property="sp" column="sp" />
    </resultMap>

    <!-- 根据sdaId查询药物与诊断信息（包含ICD信息） -->
    <select id="selectDiagnosisRulesWithIcdBySdaId" parameterType="Long" resultMap="DiagnosisRuleResult">
        select 
            i.id,
            i.sda_id,
            i.diagnoses,
            i.bs,
            i.ispb,
            i.remark,
            b.icd_code,
            b.icd_name,
            b.sp
        from rms_t_sda_icd10_info i
        left join rms_t_icd10_base b on i.diagnoses COLLATE utf8mb4_general_ci = b.code COLLATE utf8mb4_general_ci
        where i.sda_id = #{sdaId} and i.bs = 5
        order by b.icd_code
    </select>

    <!-- 根据sdaId查询药物禁忌症信息（包含ICD信息） -->
    <select id="selectContraindicationRulesWithIcdBySdaId" parameterType="Long" resultMap="DiagnosisRuleResult">
        select 
            i.id,
            i.sda_id,
            i.diagnoses,
            i.bs,
            i.ispb,
            i.remark,
            b.icd_code,
            b.icd_name,
            b.sp
        from rms_t_sda_icd10_info i
        left join rms_t_icd10_base b on i.diagnoses COLLATE utf8mb4_general_ci = b.code COLLATE utf8mb4_general_ci
        where i.sda_id = #{sdaId} and i.bs = 1
        order by b.icd_code
    </select>
</mapper>