package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.DrugQueryResult;
import com.rms.core.domain.RmsItfHosDrug;
import org.apache.ibatis.annotations.Param;

/**
 * 药品知识库Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface RmsDrugKnowledgeMapper
{
    /**
     * 根据关键字查询药品
     *
     * @param keyword 检索关键字
     * @return 药品查询结果集合
     */
    public List<DrugQueryResult> searchDrugs(@Param("keyword") String keyword);

    /**
     * 根据drugCode查询药品标识信息
     *
     * @param drugCode 药品编码
     * @return 药品标识信息
     */
    public RmsItfHosDrug getDrugIdentityByDrugCode(@Param("drugCode") String drugCode);

    /**
     * 更新药品标识信息
     *
     * @param rmsItfHosDrug 药品标识信息
     * @return 结果
     */
    public int updateDrugIdentity(RmsItfHosDrug rmsItfHosDrug);

    /**
     * 根据sdaId查询常规用量规则库信息
     *
     * @param sdaId 标准数据ID
     * @return 常规用量规则库集合
     */
    public List<java.util.Map<String, Object>> getDoseRulesBySdaId(@Param("sdaId") Long sdaId);

    /**
     * 新增常规用量条件表
     *
     * @param params 常规用量条件参数
     * @return 结果
     */
    public int insertDoseCondition(java.util.Map<String, Object> params);

    /**
     * 新增常规用量结果表
     *
     * @param params 常规用量结果参数
     * @return 结果
     */
    public int insertDoseResult(java.util.Map<String, Object> params);

    /**
     * 更新常规用量条件表
     *
     * @param params 常规用量条件参数
     * @return 结果
     */
    public int updateDoseCondition(java.util.Map<String, Object> params);

    /**
     * 更新常规用量结果表
     *
     * @param params 常规用量结果参数
     * @return 结果
     */
    public int updateDoseResult(java.util.Map<String, Object> params);

    /**
     * 修改常规用量规则库
     *
     * @param params 常规用量规则参数
     * @return 结果
     */
    public int updateDoseRule(java.util.Map<String, Object> params);

    /**
     * 删除常规用量结果表
     *
     * @param conditionId 条件ID
     * @return 结果
     */
    public int deleteDoseResult(@Param("conditionId") Long conditionId);

    /**
     * 删除常规用量条件表
     *
     * @param id 条件ID
     * @return 结果
     */
    public int deleteDoseCondition(@Param("id") Long id);

    /**
     * 删除常规用量规则库
     *
     * @param id 常规用量规则库主键
     * @return 结果
     */
    public int deleteDoseRule(@Param("id") Long id);

    /**
     * 批量删除常规用量结果表
     *
     * @param ids 条件ID数组
     * @return 结果
     */
    public int deleteDoseResults(@Param("ids") Long[] ids);

    /**
     * 批量删除常规用量条件表
     *
     * @param ids 条件ID数组
     * @return 结果
     */
    public int deleteDoseConditions(@Param("ids") Long[] ids);

    /**
     * 批量删除常规用量规则库
     *
     * @param ids 常规用量规则库主键数组
     * @return 结果
     */
    public int deleteDoseRules(@Param("ids") Long[] ids);
}
