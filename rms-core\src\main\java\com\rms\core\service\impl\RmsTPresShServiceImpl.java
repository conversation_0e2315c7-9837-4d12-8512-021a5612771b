package com.rms.core.service.impl;

import java.util.List;
import com.rms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTPresShMapper;
import com.rms.core.domain.RmsTPresSh;
import com.rms.core.service.IRmsTPresShService;

/**
 * 处方审核意见Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class RmsTPresShServiceImpl implements IRmsTPresShService
{
    @Autowired
    private RmsTPresShMapper rmsTPresShMapper;

    /**
     * 查询处方审核意见
     *
     * @param id 处方审核意见主键
     * @return 处方审核意见
     */
    @Override
    public RmsTPresSh selectRmsTPresShById(Long id)
    {
        return rmsTPresShMapper.selectRmsTPresShById(id);
    }

    /**
     * 查询处方审核意见列表
     *
     * @param rmsTPresSh 处方审核意见
     * @return 处方审核意见
     */
    @Override
    public List<RmsTPresSh> selectRmsTPresShList(RmsTPresSh rmsTPresSh)
    {
        return rmsTPresShMapper.selectRmsTPresShList(rmsTPresSh);
    }

    /**
     * 新增处方审核意见
     *
     * @param rmsTPresSh 处方审核意见
     * @return 结果
     */
    @Override
    public int insertRmsTPresSh(RmsTPresSh rmsTPresSh)
    {
        rmsTPresSh.setCreateTime(DateUtils.getNowDate());
        return rmsTPresShMapper.insertRmsTPresSh(rmsTPresSh);
    }

    /**
     * 修改处方审核意见
     *
     * @param rmsTPresSh 处方审核意见
     * @return 结果
     */
    @Override
    public int updateRmsTPresSh(RmsTPresSh rmsTPresSh)
    {
        return rmsTPresShMapper.updateRmsTPresSh(rmsTPresSh);
    }

    /**
     * 批量删除处方审核意见
     *
     * @param ids 需要删除的处方审核意见主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresShByIds(Long[] ids)
    {
        return rmsTPresShMapper.deleteRmsTPresShByIds(ids);
    }

    /**
     * 删除处方审核意见信息
     *
     * @param id 处方审核意见主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresShById(Long id)
    {
        return rmsTPresShMapper.deleteRmsTPresShById(id);
    }
}
