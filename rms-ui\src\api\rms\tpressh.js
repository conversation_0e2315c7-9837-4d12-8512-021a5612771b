import request from '@/utils/request'

// 查询处方审核意见列表
export function listTpressh(query) {
  return request({
    url: '/rms/tpressh/list',
    method: 'get',
    params: query
  })
}

// 查询处方审核意见详细
export function getTpressh(id) {
  return request({
    url: '/rms/tpressh/' + id,
    method: 'get'
  })
}

// 新增处方审核意见
export function addTpressh(data) {
  return request({
    url: '/rms/tpressh',
    method: 'post',
    data: data
  })
}

// 修改处方审核意见
export function updateTpressh(data) {
  return request({
    url: '/rms/tpressh',
    method: 'put',
    data: data
  })
}

// 删除处方审核意见
export function delTpressh(id) {
  return request({
    url: '/rms/tpressh/' + id,
    method: 'delete'
  })
}
