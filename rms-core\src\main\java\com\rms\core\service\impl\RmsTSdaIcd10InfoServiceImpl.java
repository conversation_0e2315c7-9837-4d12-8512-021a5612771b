package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaIcd10InfoMapper;
import com.rms.core.domain.RmsTSdaIcd10Info;
import com.rms.core.service.IRmsTSdaIcd10InfoService;

/**
 * 药物与诊断信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class RmsTSdaIcd10InfoServiceImpl implements IRmsTSdaIcd10InfoService
{
    @Autowired
    private RmsTSdaIcd10InfoMapper rmsTSdaIcd10InfoMapper;

    /**
     * 查询药物与诊断信息
     *
     * @param id 药物与诊断信息主键
     * @return 药物与诊断信息
     */
    @Override
    public RmsTSdaIcd10Info selectRmsTSdaIcd10InfoById(Long id)
    {
        return rmsTSdaIcd10InfoMapper.selectRmsTSdaIcd10InfoById(id);
    }

    /**
     * 查询药物与诊断信息列表
     *
     * @param rmsTSdaIcd10Info 药物与诊断信息
     * @return 药物与诊断信息
     */
    @Override
    public List<RmsTSdaIcd10Info> selectRmsTSdaIcd10InfoList(RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return rmsTSdaIcd10InfoMapper.selectRmsTSdaIcd10InfoList(rmsTSdaIcd10Info);
    }

    /**
     * 新增药物与诊断信息
     *
     * @param rmsTSdaIcd10Info 药物与诊断信息
     * @return 结果
     */
    @Override
    public int insertRmsTSdaIcd10Info(RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return rmsTSdaIcd10InfoMapper.insertRmsTSdaIcd10Info(rmsTSdaIcd10Info);
    }

    /**
     * 修改药物与诊断信息
     *
     * @param rmsTSdaIcd10Info 药物与诊断信息
     * @return 结果
     */
    @Override
    public int updateRmsTSdaIcd10Info(RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return rmsTSdaIcd10InfoMapper.updateRmsTSdaIcd10Info(rmsTSdaIcd10Info);
    }

    /**
     * 批量删除药物与诊断信息
     *
     * @param ids 需要删除的药物与诊断信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaIcd10InfoByIds(Long[] ids)
    {
        return rmsTSdaIcd10InfoMapper.deleteRmsTSdaIcd10InfoByIds(ids);
    }

    /**
     * 删除药物与诊断信息信息
     *
     * @param id 药物与诊断信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaIcd10InfoById(Long id)
    {
        return rmsTSdaIcd10InfoMapper.deleteRmsTSdaIcd10InfoById(id);
    }
}
