package com.rms.core.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 诊断信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class DiagnoseDataDTO {

    /**
     * 诊断类型：0-其他；1-病生理状态；2-IDC10代码
     */
    @NotNull(message = "诊断类型不能为空")
    private Integer type;

    /**
     * 诊断名称
     */
    @NotBlank(message = "诊断名称不能为空")
    private String name;

    /**
     * 诊断代码
     */
    @NotBlank(message = "诊断代码不能为空")
    private String code;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "DiagnoseDataDTO{" +
                "type=" + type +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
