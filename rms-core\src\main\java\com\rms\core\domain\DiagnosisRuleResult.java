package com.rms.core.domain;

/**
 * 药物与诊断规则查询结果
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public class DiagnosisRuleResult
{
    /** 主键ID */
    private Long id;

    /** 药品说明书ID */
    private Long sdaId;

    /** 诊断编码 */
    private String diagnoses;

    /** 标识 */
    private Long bs;

    /** 是否屏蔽 */
    private String ispb;

    /** 备注 */
    private String remark;

    /** ICD-10编码 */
    private String icdCode;

    /** ICD-10诊断名称 */
    private String icdName;

    /** 拼音缩写 */
    private String sp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSdaId() {
        return sdaId;
    }

    public void setSdaId(Long sdaId) {
        this.sdaId = sdaId;
    }

    public String getDiagnoses() {
        return diagnoses;
    }

    public void setDiagnoses(String diagnoses) {
        this.diagnoses = diagnoses;
    }

    public Long getBs() {
        return bs;
    }

    public void setBs(Long bs) {
        this.bs = bs;
    }

    public String getIspb() {
        return ispb;
    }

    public void setIspb(String ispb) {
        this.ispb = ispb;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIcdCode() {
        return icdCode;
    }

    public void setIcdCode(String icdCode) {
        this.icdCode = icdCode;
    }

    public String getIcdName() {
        return icdName;
    }

    public void setIcdName(String icdName) {
        this.icdName = icdName;
    }

    public String getSp() {
        return sp;
    }

    public void setSp(String sp) {
        this.sp = sp;
    }

    @Override
    public String toString() {
        return "DiagnosisRuleResult{" +
                "id=" + id +
                ", sdaId=" + sdaId +
                ", diagnoses='" + diagnoses + '\'' +
                ", bs=" + bs +
                ", ispb='" + ispb + '\'' +
                ", remark='" + remark + '\'' +
                ", icdCode='" + icdCode + '\'' +
                ", icdName='" + icdName + '\'' +
                ", sp='" + sp + '\'' +
                '}';
    }
} 