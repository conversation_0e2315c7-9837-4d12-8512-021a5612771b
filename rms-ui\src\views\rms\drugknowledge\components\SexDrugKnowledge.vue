<template>
  <div class="sex-drug-knowledge">
    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button
        type="primary"
        plain
        icon="el-icon-plus"
        size="mini"
        @click="handleAdd"
      >新增</el-button>
      <el-button
        type="danger"
        plain
        icon="el-icon-delete"
        size="mini"
        :disabled="selectedRules.length === 0"
        @click="handleBatchDelete"
      >批量删除</el-button>
    </div>

    <!-- 性别用药规则表格 -->
    <el-table
      v-loading="loading"
      :data="ruleList"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="性别" prop="sex" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.sex === '1' ? 'primary' : 'danger'"
            size="mini"
          >
            {{ scope.row.sex === '1' ? '男' : '女' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否屏蔽" prop="ispb" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.ispb === '1' ? 'danger' : 'success'"
            size="mini"
          >
            {{ scope.row.ispb === '1' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" show-overflow-tooltip />
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="性别" prop="sex">
          <el-select v-model="form.sex" placeholder="请选择性别">
            <el-option label="男" value="1" />
            <el-option label="女" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否屏蔽" prop="ispb">
          <el-radio-group v-model="form.ispb">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSexDrugRules, addSexDrugRule, updateSexDrugRule, deleteSexDrugRule, deleteSexDrugRules } from "@/api/rms/tsdasex"

export default {
  name: "SexDrugKnowledge",
  props: {
    drug: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 规则列表
      ruleList: [],
      // 选中的规则
      selectedRules: [],
      // 对话框标题
      title: "",
      // 是否显示对话框
      open: false,
      // 表单数据
      form: {},
      // 表单验证规则
      rules: {
        sex: [
          { required: true, message: "性别不能为空", trigger: "change" }
        ],
        ispb: [
          { required: true, message: "是否屏蔽不能为空", trigger: "change" }
        ]
      }
    }
  },
  watch: {
    drug: {
      handler(newVal) {
        if (newVal && newVal.sdaId) {
          this.loadRules()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载规则列表
    loadRules() {
      if (!this.drug || !this.drug.sdaId) return
      
      this.loading = true
      getSexDrugRules(this.drug.sdaId).then(response => {
        this.ruleList = response.data || []
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedRules = selection
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加性别用药规则"
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      this.form = { ...row }
      this.open = true
      this.title = "修改性别用药规则"
    },
    // 删除按钮操作
    handleDelete(row) {
      const that = this
      this.$modal.confirm('是否确认删除该性别用药规则？').then(function() {
        return deleteSexDrugRule(row.id)
      }).then(() => {
        that.loadRules()
        that.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    // 批量删除按钮操作
    handleBatchDelete() {
      const that = this
      if (this.selectedRules.length === 0) {
        this.$modal.msgWarning("请选择要删除的性别用药规则")
        return
      }
      this.$modal.confirm(`是否确认批量删除选中的 ${this.selectedRules.length} 条性别用药规则？`).then(function() {
        const ids = that.selectedRules.map(item => item.id)
        return deleteSexDrugRules(ids)
      }).then(() => {
        that.loadRules()
        that.$modal.msgSuccess("批量删除成功")
      }).catch(() => {})
    },
    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 设置标准数据ID
          this.form.sdaId = this.drug.sdaId
          
          if (this.form.id != null) {
            updateSexDrugRule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.loadRules()
            })
          } else {
            addSexDrugRule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.loadRules()
            })
          }
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sdaId: null,
        sex: null,
        ispb: "0",
        remark: null
      }
      this.resetForm("form")
    },
    // 重置表单
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    }
  }
}
</script>

<style scoped>
.sex-drug-knowledge {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: center;
}
</style> 