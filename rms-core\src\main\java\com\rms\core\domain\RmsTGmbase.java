package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 过敏基础信息对象 rms_t_gmbase
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public class RmsTGmbase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 过敏代码 */
    private String code;

    /** 过敏名称 */
    @Excel(name = "过敏名称")
    private String name;

    /** 拼音缩写 */
    @Excel(name = "拼音缩写")
    private String sp;

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }

    public void setSp(String sp)
    {
        this.sp = sp;
    }

    public String getSp()
    {
        return sp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("name", getName())
            .append("sp", getSp())
            .toString();
    }
}
