package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaSexMapper;
import com.rms.core.domain.RmsTSdaSex;
import com.rms.core.service.IRmsTSdaSexService;

/**
 * 标准药品性别库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class RmsTSdaSexServiceImpl implements IRmsTSdaSexService
{
    @Autowired
    private RmsTSdaSexMapper rmsTSdaSexMapper;

    /**
     * 查询标准药品性别库
     *
     * @param id 标准药品性别库主键
     * @return 标准药品性别库
     */
    @Override
    public RmsTSdaSex selectRmsTSdaSexById(Long id)
    {
        return rmsTSdaSexMapper.selectRmsTSdaSexById(id);
    }

    /**
     * 查询标准药品性别库列表
     *
     * @param rmsTSdaSex 标准药品性别库
     * @return 标准药品性别库
     */
    @Override
    public List<RmsTSdaSex> selectRmsTSdaSexList(RmsTSdaSex rmsTSdaSex)
    {
        return rmsTSdaSexMapper.selectRmsTSdaSexList(rmsTSdaSex);
    }

    /**
     * 新增标准药品性别库
     *
     * @param rmsTSdaSex 标准药品性别库
     * @return 结果
     */
    @Override
    public int insertRmsTSdaSex(RmsTSdaSex rmsTSdaSex)
    {
        return rmsTSdaSexMapper.insertRmsTSdaSex(rmsTSdaSex);
    }

    /**
     * 修改标准药品性别库
     *
     * @param rmsTSdaSex 标准药品性别库
     * @return 结果
     */
    @Override
    public int updateRmsTSdaSex(RmsTSdaSex rmsTSdaSex)
    {
        return rmsTSdaSexMapper.updateRmsTSdaSex(rmsTSdaSex);
    }

    /**
     * 批量删除标准药品性别库
     *
     * @param ids 需要删除的标准药品性别库主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaSexByIds(Long[] ids)
    {
        return rmsTSdaSexMapper.deleteRmsTSdaSexByIds(ids);
    }

    /**
     * 删除标准药品性别库信息
     *
     * @param id 标准药品性别库主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaSexById(Long id)
    {
        return rmsTSdaSexMapper.deleteRmsTSdaSexById(id);
    }
}
