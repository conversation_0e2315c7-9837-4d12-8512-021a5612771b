import request from '@/utils/request'

// 查询老年人用药规则库列表
export function listTsdaelder(query) {
  return request({
    url: '/rms/tsdaelder/list',
    method: 'get',
    params: query
  })
}

// 查询老年人用药规则库详细
export function getTsdaelder(id) {
  return request({
    url: '/rms/tsdaelder/' + id,
    method: 'get'
  })
}

// 新增老年人用药规则库
export function addTsdaelder(data) {
  return request({
    url: '/rms/tsdaelder',
    method: 'post',
    data: data
  })
}

// 修改老年人用药规则库
export function updateTsdaelder(data) {
  return request({
    url: '/rms/tsdaelder',
    method: 'put',
    data: data
  })
}

// 删除老年人用药规则库
export function delTsdaelder(id) {
  return request({
    url: '/rms/tsdaelder/' + id,
    method: 'delete'
  })
}
