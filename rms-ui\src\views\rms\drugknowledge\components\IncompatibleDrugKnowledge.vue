<template>
  <div class="incompatible-drug-knowledge">
    <!-- 切换西药/中药 -->
    <div class="tab-switch">
      <el-radio-group v-model="drugType" @change="handleDrugTypeChange">
        <el-radio-button label="western">西药配伍禁忌</el-radio-button>
        <el-radio-button label="chinese">中药配伍禁忌</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 操作按钮 -->
    <div class="operation-bar">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleAdd"
      >
        新增配伍禁忌
      </el-button>
      <el-button
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="selectedIds.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="incompatibleList"
      border
      @selection-change="handleSelectionChange"
      style="width: 100%; margin-top: 15px;"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="药物A" prop="yaowuaName" width="120" show-overflow-tooltip />
      <el-table-column label="药物B" prop="yaowubName" width="120" show-overflow-tooltip />
      <el-table-column label="相互作用效果" prop="effect" show-overflow-tooltip />
      <el-table-column label="作用机制" prop="mechanism" show-overflow-tooltip />
      <el-table-column label="相关药物" prop="relatedrug" width="100" show-overflow-tooltip />
      <el-table-column label="重要性" prop="impBs" width="70" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.impBs === '0' ? 'danger' : scope.row.impBs === '1' ? 'warning' : 'info'"
            size="small"
          >
            {{ scope.row.impBs === '0' ? '严重' : scope.row.impBs === '1' ? '一般' : '其它' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="结果等级" prop="result" width="70" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.result === '0' ? 'danger' : scope.row.result === '1' ? 'warning' : 'info'"
            size="small"
          >
            {{ scope.row.result === '0' ? '禁忌' : scope.row.result === '1' ? '问题' : '提示' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="建议标识" prop="sugflag" width="80" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.sugflag === 'pwjj' ? 'danger' : 'warning'"
            size="small"
          >
            {{ scope.row.sugflag === 'pwjj' ? '配伍禁忌' : '相互作用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否屏蔽" prop="ispb" width="80" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.ispb === '1' ? 'info' : 'success'"
            size="small"
          >
            {{ scope.row.ispb === '1' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" align="center">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="form.id ? '编辑配伍禁忌' : '新增配伍禁忌'"
      :visible.sync="dialogVisible"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="small"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="药物A" prop="yaowua">
              <el-input
                v-model="yaowuaKeyword"
                placeholder="请输入药名或拼音缩写"
                clearable
                @input="handleYaowuaSearch"
              />
              <el-select
                v-model="form.yaowua"
                placeholder="请选择药物A"
                style="width: 100%; margin-top: 5px;"
                clearable
                filterable
                @change="handleYaowuaChange"
              >
                <el-option
                  v-for="item in yaowuaOptions"
                  :key="item.sdaId"
                  :label="item.drugName"
                  :value="item.sdaId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药物B" prop="yaowub">
              <el-input
                v-model="yaowubKeyword"
                placeholder="请输入药名或拼音缩写"
                clearable
                @input="handleYaowubSearch"
              />
              <el-select
                v-model="form.yaowub"
                placeholder="请选择药物B"
                style="width: 100%; margin-top: 5px;"
                clearable
                filterable
                @change="handleYaowubChange"
              >
                <el-option
                  v-for="item in yaowubOptions"
                  :key="item.sdaId"
                  :label="item.drugName"
                  :value="item.sdaId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="相互作用效果" prop="effect">
          <el-input
            v-model="form.effect"
            type="textarea"
            :rows="3"
            placeholder="请输入相互作用效果"
          />
        </el-form-item>

        <el-form-item label="作用机制" prop="mechanism">
          <el-input
            v-model="form.mechanism"
            type="textarea"
            :rows="3"
            placeholder="请输入作用机制"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重要性标识" prop="impBs">
              <el-select v-model="form.impBs" placeholder="请选择重要性">
                <el-option label="严重" value="0" />
                <el-option label="一般" value="1" />
                <el-option label="其它" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结果等级" prop="result">
              <el-select v-model="form.result" placeholder="请选择结果等级">
                <el-option label="禁忌" value="0" />
                <el-option label="问题" value="1" />
                <el-option label="提示" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="建议" prop="recommandations">
          <el-input
            v-model="form.recommandations"
            type="textarea"
            :rows="3"
            placeholder="请输入建议"
          />
        </el-form-item>

        <el-form-item label="相关药物" prop="relatedrug">
          <el-input
            v-model="form.relatedrug"
            type="textarea"
            :rows="2"
            placeholder="请输入相关药物"
          />
        </el-form-item>

        <el-form-item label="参考文献" prop="reference">
          <el-input
            v-model="form.reference"
            type="textarea"
            :rows="2"
            placeholder="请输入参考文献"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="建议标识" prop="sugflag">
              <el-select v-model="form.sugflag" placeholder="请选择建议标识">
                <el-option label="配伍禁忌" value="pwjj" />
                <el-option label="相互作用" value="xhzy" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否屏蔽" prop="ispb">
              <el-select v-model="form.ispb" placeholder="请选择是否屏蔽">
                <el-option label="否" value="0" />
                <el-option label="是" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getIncompatibleRules,
  addIncompatibleRule,
  updateIncompatibleRule,
  deleteIncompatibleRule,
  deleteIncompatibleRules,
  getIncompatibleRulesZy,
  addIncompatibleRuleZy,
  updateIncompatibleRuleZy,
  deleteIncompatibleRuleZy,
  deleteIncompatibleRulesZy,
  searchDrugs
} from "@/api/rms/drugknowledge"

export default {
  name: "IncompatibleDrugKnowledge",
  props: {
    drug: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      drugType: 'western', // 药物类型：western-西药，chinese-中药
      loading: false,
      incompatibleList: [],
      selectedIds: [],
      dialogVisible: false,
      form: {
        id: null,
        yaowua: null,
        yaowub: null,
        effect: '',
        mechanism: '',
        relatedrug: '',
        reference: '',
        impBs: '',
        result: '',
        recommandations: '',
        sugflag: '',
        ispb: '',
        remark: ''
      },
      rules: {
        yaowua: [
          { required: true, message: '请选择药物A', trigger: 'change' }
        ],
        yaowub: [
          { required: true, message: '请选择药物B', trigger: 'change' }
        ],
        effect: [
          { required: true, message: '请输入相互作用效果', trigger: 'blur' }
        ],
        impBs: [
          { required: true, message: '请选择重要性标识', trigger: 'change' }
        ],
        result: [
          { required: true, message: '请选择结果等级', trigger: 'change' }
        ],
        sugflag: [
          { required: true, message: '请选择建议标识', trigger: 'change' }
        ],
        ispb: [
          { required: true, message: '请选择是否屏蔽', trigger: 'change' }
        ]
      },
      yaowuaKeyword: '',
      yaowubKeyword: '',
      yaowuaOptions: [],
      yaowubOptions: []
    }
  },
  watch: {
    drug: {
      handler(newVal) {
        if (newVal && newVal.sdaId) {
          this.loadIncompatibleList()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载配伍禁忌列表
    loadIncompatibleList() {
      if (!this.drug.sdaId) return

      this.loading = true
      const apiMethod = this.drugType === 'western' ? getIncompatibleRules : getIncompatibleRulesZy
      apiMethod(this.drug.sdaId).then(response => {
        this.incompatibleList = response.data || []
        this.loading = false
      }).catch(error => {
        console.error('加载配伍禁忌列表失败：', error) // 调试信息
        this.loading = false
      })
    },

    // 处理药物类型切换
    handleDrugTypeChange() {
      this.selectedIds = []
      this.loadIncompatibleList()
    },

    // 处理表格选择
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },

    // 添加配伍禁忌
    handleAdd() {
      this.form = {
        id: null,
        yaowua: this.drug.sdaId, // 默认设置当前药品为药物A
        yaowub: null,
        effect: '',
        mechanism: '',
        relatedrug: '',
        reference: '',
        impBs: '',
        result: '',
        recommandations: '',
        sugflag: 'pwjj', // 默认为配伍禁忌
        ispb: '0', // 默认不屏蔽
        remark: ''
      }
      this.yaowuaKeyword = this.drug.drugName || '' // 显示当前药品名称
      this.yaowubKeyword = ''
      // 设置药物A的选项为当前药品
      this.yaowuaOptions = [{
        sdaId: this.drug.sdaId,
        drugName: this.drug.drugName
      }]
      this.yaowubOptions = []
      this.dialogVisible = true
    },

    // 编辑配伍禁忌
    handleEdit(row) {
      this.form = { ...row }
      this.yaowuaKeyword = ''
      this.yaowubKeyword = ''
      this.yaowuaOptions = []
      this.yaowubOptions = []
      this.dialogVisible = true
    },

    // 删除配伍禁忌
    handleDelete(row) {
      this.$confirm('确定删除该配伍禁忌信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const apiMethod = this.drugType === 'western' ? deleteIncompatibleRule : deleteIncompatibleRuleZy
        apiMethod(row.id).then(() => {
          this.$modal.msgSuccess('删除成功')
          this.loadIncompatibleList()
        })
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedIds.length === 0) {
        this.$modal.msgError('请先选择要删除的数据')
        return
      }

      this.$confirm('确定删除选中的配伍禁忌信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const apiMethod = this.drugType === 'western' ? deleteIncompatibleRules : deleteIncompatibleRulesZy
        apiMethod(this.selectedIds).then(() => {
          this.$modal.msgSuccess('删除成功')
          this.loadIncompatibleList()
        })
      })
    },

    // 搜索药物A
    handleYaowuaSearch() {
      if (this.yaowuaKeyword && this.yaowuaKeyword.trim()) {
        searchDrugs(this.yaowuaKeyword.trim()).then(response => {
          this.yaowuaOptions = response.data || []
        })
      } else {
        this.yaowuaOptions = []
      }
    },

    // 搜索药物B
    handleYaowubSearch() {
      if (this.yaowubKeyword && this.yaowubKeyword.trim()) {
        searchDrugs(this.yaowubKeyword.trim()).then(response => {
          this.yaowubOptions = response.data || []
        })
      } else {
        this.yaowubOptions = []
      }
    },

    // 药物A选择变化
    handleYaowuaChange(sdaId) {
      const selected = this.yaowuaOptions.find(item => item.sdaId === sdaId)
      if (selected) {
        this.yaowuaKeyword = selected.drugName
      }
    },

    // 药物B选择变化
    handleYaowubChange(sdaId) {
      const selected = this.yaowubOptions.find(item => item.sdaId === sdaId)
      if (selected) {
        this.yaowubKeyword = selected.drugName
      }
    },

    // 表单提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const apiMethod = this.form.id
            ? (this.drugType === 'western' ? updateIncompatibleRule : updateIncompatibleRuleZy)
            : (this.drugType === 'western' ? addIncompatibleRule : addIncompatibleRuleZy)

          apiMethod(this.form).then(() => {
            this.$modal.msgSuccess(this.form.id ? '修改成功' : '新增成功')
            this.dialogVisible = false
            this.loadIncompatibleList()
          })
        }
      })
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style scoped>
.incompatible-drug-knowledge {
  padding: 20px;
}

.tab-switch {
  margin-bottom: 20px;
}

.operation-bar {
  margin-bottom: 15px;
}

.dialog-footer {
  text-align: right;
}
</style>
