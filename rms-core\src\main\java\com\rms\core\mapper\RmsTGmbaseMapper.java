package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTGmbase;

/**
 * 过敏基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface RmsTGmbaseMapper 
{
    /**
     * 查询过敏基础信息
     * 
     * @param code 过敏基础信息主键
     * @return 过敏基础信息
     */
    public RmsTGmbase selectRmsTGmbaseByCode(String code);

    /**
     * 查询过敏基础信息列表
     * 
     * @param rmsTGmbase 过敏基础信息
     * @return 过敏基础信息集合
     */
    public List<RmsTGmbase> selectRmsTGmbaseList(RmsTGmbase rmsTGmbase);

    /**
     * 新增过敏基础信息
     * 
     * @param rmsTGmbase 过敏基础信息
     * @return 结果
     */
    public int insertRmsTGmbase(RmsTGmbase rmsTGmbase);

    /**
     * 修改过敏基础信息
     * 
     * @param rmsTGmbase 过敏基础信息
     * @return 结果
     */
    public int updateRmsTGmbase(RmsTGmbase rmsTGmbase);

    /**
     * 删除过敏基础信息
     * 
     * @param code 过敏基础信息主键
     * @return 结果
     */
    public int deleteRmsTGmbaseByCode(String code);

    /**
     * 批量删除过敏基础信息
     * 
     * @param codes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTGmbaseByCodes(String[] codes);

    /**
     * 根据关键字搜索过敏基础数据
     * 
     * @param keyword 搜索关键字
     * @return 过敏基础数据集合
     */
    public List<RmsTGmbase> searchAllergyBaseByKeyword(String keyword);
}
