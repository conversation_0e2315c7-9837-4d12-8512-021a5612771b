<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTIcd10BaseMapper">
    
    <resultMap type="RmsTIcd10Base" id="RmsTIcd10BaseResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="icdCode"    column="icd_code"    />
        <result property="fjm"    column="fjm"    />
        <result property="icdName"    column="icd_name"    />
        <result property="sp"    column="sp"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRmsTIcd10BaseVo">
        select id, code, icd_code, fjm, icd_name, sp, remark from rms_t_icd10_base
    </sql>

    <select id="selectRmsTIcd10BaseList" parameterType="RmsTIcd10Base" resultMap="RmsTIcd10BaseResult">
        <include refid="selectRmsTIcd10BaseVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="icdCode != null  and icdCode != ''"> and icd_code = #{icdCode}</if>
            <if test="fjm != null  and fjm != ''"> and fjm = #{fjm}</if>
            <if test="icdName != null  and icdName != ''"> and icd_name like concat('%', #{icdName}, '%')</if>
            <if test="sp != null  and sp != ''"> and sp = #{sp}</if>
        </where>
    </select>
    
    <select id="selectRmsTIcd10BaseById" parameterType="Long" resultMap="RmsTIcd10BaseResult">
        <include refid="selectRmsTIcd10BaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTIcd10Base" parameterType="RmsTIcd10Base" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_icd10_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="icdCode != null">icd_code,</if>
            <if test="fjm != null">fjm,</if>
            <if test="icdName != null">icd_name,</if>
            <if test="sp != null">sp,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="icdCode != null">#{icdCode},</if>
            <if test="fjm != null">#{fjm},</if>
            <if test="icdName != null">#{icdName},</if>
            <if test="sp != null">#{sp},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRmsTIcd10Base" parameterType="RmsTIcd10Base">
        update rms_t_icd10_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="icdCode != null">icd_code = #{icdCode},</if>
            <if test="fjm != null">fjm = #{fjm},</if>
            <if test="icdName != null">icd_name = #{icdName},</if>
            <if test="sp != null">sp = #{sp},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTIcd10BaseById" parameterType="Long">
        delete from rms_t_icd10_base where id = #{id}
    </delete>

    <delete id="deleteRmsTIcd10BaseByIds" parameterType="String">
        delete from rms_t_icd10_base where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="searchByKeyword" parameterType="String" resultMap="RmsTIcd10BaseResult">
        <include refid="selectRmsTIcd10BaseVo"/>
        <where>
            <if test="keyword != null and keyword != ''">
                (icd_code like concat('%', #{keyword}, '%') 
                or icd_name like concat('%', #{keyword}, '%') 
                or sp like concat('%', #{keyword}, '%'))
            </if>
        </where>
        order by icd_code
    </select>
</mapper>