package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTPresMedMapper;
import com.rms.core.domain.RmsTPresMed;
import com.rms.core.service.IRmsTPresMedService;

/**
 * 处方明细信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
public class RmsTPresMedServiceImpl implements IRmsTPresMedService
{
    @Autowired
    private RmsTPresMedMapper rmsTPresMedMapper;

    /**
     * 查询处方明细信息
     *
     * @param code 处方明细信息主键
     * @return 处方明细信息
     */
    @Override
    public RmsTPresMed selectRmsTPresMedByCode(String code)
    {
        return rmsTPresMedMapper.selectRmsTPresMedByCode(code);
    }

    /**
     * 查询处方明细信息列表
     *
     * @param rmsTPresMed 处方明细信息
     * @return 处方明细信息
     */
    @Override
    public List<RmsTPresMed> selectRmsTPresMedList(RmsTPresMed rmsTPresMed)
    {
        return rmsTPresMedMapper.selectRmsTPresMedList(rmsTPresMed);
    }

    /**
     * 新增处方明细信息
     *
     * @param rmsTPresMed 处方明细信息
     * @return 结果
     */
    @Override
    public int insertRmsTPresMed(RmsTPresMed rmsTPresMed)
    {
        return rmsTPresMedMapper.insertRmsTPresMed(rmsTPresMed);
    }

    /**
     * 修改处方明细信息
     *
     * @param rmsTPresMed 处方明细信息
     * @return 结果
     */
    @Override
    public int updateRmsTPresMed(RmsTPresMed rmsTPresMed)
    {
        return rmsTPresMedMapper.updateRmsTPresMed(rmsTPresMed);
    }

    /**
     * 批量删除处方明细信息
     *
     * @param codes 需要删除的处方明细信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresMedByCodes(String[] codes)
    {
        return rmsTPresMedMapper.deleteRmsTPresMedByCodes(codes);
    }

    /**
     * 删除处方明细信息信息
     *
     * @param code 处方明细信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresMedByCode(String code)
    {
        return rmsTPresMedMapper.deleteRmsTPresMedByCode(code);
    }
}
