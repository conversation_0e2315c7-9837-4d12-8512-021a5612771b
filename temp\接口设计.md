# 合理用药与事前审方系统接口文档
## 概述
本文档描述了处方分析、获取处方审核结果、药品说明书三个核心接口，适用于医院信息系统（HIS）与审方系统的标准化交互。


## 接口通用规范
### 通信协议
- 采用HTTP/HTTPS协议，目前暂用HTTP协议
- 所有接口均使用`POST`方法
- 字符编码：UTF-8
- 数据格式：请求体与响应体均为JSON


### 通用响应格式
所有接口返回统一结构，包含业务状态、消息及数据：
```json
{
  "code": 0, // 业务状态码：0-成功；非0-失败（具体见各接口说明）
  "message": "操作成功", // 状态描述
  "data": {} // 业务数据（各接口自定义结构）
}
```


### 特殊字符处理
JSON中特殊字符需按标准转义：
- 双引号（"）→ \"
- 反斜杠（\）→ \\
- 换行符 → \n
- 制表符 → \t


## 接口详细说明

### 1. 处方分析接口
#### 功能描述
医生开具处方后调用，用于分析处方合理性（如配伍禁忌、用药剂量、过敏风险等），返回问题级别及双签需求信息。

#### 请求信息
- URL：`/api/v1/prescription/analyze`
- 请求体结构：
```json
{
  "baseInfo": { // 基本信息
    "source": "HIS", // 固定值：调用方标识（必填）
    "hospCode": "H001", // 医院编码，即医疗机构代码（必填）
    "deptCode": "D002", // 科室代码（必填）
    "deptName": "内科", // 科室名称（必填）
    "doctor": {
      "code": "DR003", // 医生代码（必填）
      "name": "张三", // 医生姓名（必填）
      "type": "1", // 医生级别代码（必填）
      "typeName": "主治医师" // 医生级别名称（必填）
    }
  },
  "details": { // 详细信息
    "isUpload": 0, // 是否保存分析结果：0-不保存；1-保存（必填）
    "guid": "G123456", // 唯一标识（仅武汉普爱医院卫计委平台用）
    "hisTime": "2025-07-20 10:30:00", // HIS系统时间（YYYY-MM-DD HH:mm:SS，必填）
    "hospFlag": "op", // 门诊/住院标识：op-门诊；ip-住院（必填）
    "treatType": 100, // 就诊类型：100-普通门诊；101-专科门诊；102-专家门诊；200-急诊；300-急诊观察；400-普通住院；401-特需住院；500-家床；999-其他（必填）
    "treatCode": "T000123", // 就诊号（医院内唯一，必填）
    "lisAdmNo": "L123", // 检验就诊号
    "bedNo": "302", // 床位号（住院用）
    "areaCode": "A01", // 病区号（住院用）
    "patient": { // 患者信息（必填）
      "name": "李四", // 姓名（必填）
      "isInfant": 0, // 是否婴幼儿：0-否；1-是
      "birth": "1990-01-15", // 出生日期（YYYY-MM-DD，必填）
      "sex": "男", // 性别：男/女/未知（必填）
      "weight": 65, // 体重（kg）
      "height": 175, // 身高（cm）
      "idCard": "65xxxxxx", // 身份证号
      "cardType": 0, // 卡类型：0-社保卡；1-医保卡；2-医联卡；9-其他
      "cardCode": "C123456", // 卡号
      "pregnantUnit": "周", // 怀孕时间单位：天/周/月
      "pregnant": 12, // 怀孕时间（数字）
      "allergicData": [ // 过敏源信息（数组，可多个）
        {
          "type": 1, // 过敏类型：0-其他；1-联科药品大类；2=联科药品成份；3=医联过敏源；4=HIS过敏源；5= HIS药品代码；6=过敏体质。（目前仅支持1/2/5/6，必填）
          "name": "青霉素", // 过敏源名称（必填）
          "code": "ALG001" // 过敏源代码（必填）
        }
      ],
      "diagnoseData": [ // 诊断信息（数组，可多个）
        {
          "type": 2, // 诊断类型：0-其他；1-病生理状态；2-IDC10代码（必填）
          "name": "高血压", // 诊断名称（必填）
          "code": "I10" // 诊断代码（必填）
        }
      ]
    },
    "prescriptionData": [ // 处方信息（数组，可多个）
      {
        "id": "RX001", // 处方号（必填）
        "presDiagnoseData": [ // 处方诊断节点（数组，可多个）
          "type": 1, // 诊断类型
          "name": "", // 诊断名称
          "code": "", // 诊断代码
        ],
        "reason": "常规治疗", // 处方理由
        "isUrgent": 0, // 是否紧急处方：0-否；1-是（IPRC专用）
        "isNew": 1, // 是否新开处方：0-否；1-是（IPRC专用）
        "isCurrent": 1, // 是否当前处方：0-历史；1-当前（必填）
        "doctCode": "DR003", // 开嘱医生代码
        "doctName": "张三", // 开嘱医生姓名
        "deptCode": "D002", // 开嘱科室代码
        "deptName": "内科", // 开嘱科室名称
        "presType": "L", // 医嘱类型：L-长期；T-临时（住院处方有效，必填）
        "presTime": "2025-07-20 10:30:00", // 处方时间（YYYY-MM-DD HH:mm:SS，必填）
        "dischargeDrug": 0, // 出院带药标识：0-否；1-是（IPRC专用）
        "prescriptionType": 1, // 处方类型：1-西药；2-中/草药；0-未定（必填）
        "presSm": "每日一次", // 处方说明
        "admType": 1, // 中药处方/医嘱服用类型：1-内服；2-外敷；0-未定（中药用）
        "requir": "饭后服", // 要求（中药用）
        "cs1": 1, // 次数（中药用）
        "ts": 7, // 天数（中药用）
        "solvent": "水", // 煎煮溶剂（中药用）
        "jl": "200ml", // 剂量（中药用）
        "cs2": 2, // 次数（中药用）
        "lb": "常规煎", // 煎药类别（中药用）
        "fs1": "回家自煎", // 方式1（中药用）
        "fs2": "按医嘱", // 方式2（中药用）
        "isXd": 0, // 协定处方标志：0-否；1-是
        "medicineData": [ // 药品信息（数组，可多个，必填）
          {
            "name": "硝苯地平片", // 商品名（必填）
            "hisCode": "M001", // 医院药品代码（必填）
            "insurCode": "Y001", // 医保代码
            "approval": "国药准字H123456", // 批准文号
            "pydCode": "P001", // 配液单号
            "linkGroup": "G1", // 配液单组号，组号不能只传1，应该传同组第一个药品的医嘱号
            "spec": "10mg/片", // 规格
            "group": "1", // 组号（必填）
            "reason": "降压", // 用药理由
            "doseUnit": "mg", // 单次量单位（必填）
            "dose": 10, // 单次量（必填）
            "ordQty": 30, // 开药数量（IPRC专用）
            "ordUom": "片", // 开药数量单位（IPRC专用）
            "freq": "QD", // 频次代码（必填）
            "administer": "PO", // 给药途径代码（必填）
            "beginTime": "2025-07-20 10:30:00", // 用药开始时间（住院用，必填）
            "endTime": "2025-07-27 10:30:00", // 用药结束时间（住院用，必填）
            "days": 7, // 服药天数（门诊用）
            "money": 30.5, // 金额
            "yysm": "每日一次", // 用药说明
            "bz": "无", // 备注
            "qydd": "院内取药", // 取药地点
            "preventiveflag": 0 // 是否预防用药：1-是；0-否（住院用）
          }
        ]
      }
    ]
  }
}
```


#### 响应数据
```json
{
  "code": 0,
  "message": "分析成功",
  "data": {
    "problemLevel": 0, // 问题级别：0-无问题；1-其他问题；2-一般问题；3-严重问题（必填）
    "problemDetail": "", // 问题详情（问题级别>0时有值）
    "doubleSignFlag": 0, // 双签标志：0-不需要；1-需要（必填）
    "doubleSignDrugs": "M001;M002" // 需双签药品代码（分号分隔，doubleSignFlag=1时有效）
  }
}
```


### 2. 获取处方的审核结果
#### 功能描述
用于查询指定处方的审核状态（如通过、待审核、不通过）。

#### 请求信息
- URL：`/api/v1/prescription/check-result`
- 请求体结构：
```json
{
  "baseInfo": { // 基本信息
    "source": "HIS", // 固定值：调用方标识（必填）
    "hospCode": "H001", // 医院编码，即医疗机构代码（必填）
    "deptCode": "D002", // 科室代码（必填）
    "deptName": "内科", // 科室名称（必填）
    "doctor": {
      "code": "DR003", // 医生代码（必填）
      "name": "张三", // 医生姓名（必填）
      "type": "1", // 医生级别代码（必填）
      "typeName": "主治医师" // 医生级别名称（必填）
    }
  },
  "details": {
    "hospFlag": "op", // 门诊/住院标识：op-门诊；ip-住院（必填）
    "treatCode": "T000123", // 就诊号（必填）
    "prescriptionId": "RX001" // 处方号（必填）
  }
}
```


#### 响应数据
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "checkStatus": 0 // 审核状态：0-无需人工干预或审核通过；1-待审核；2-审核不通过（必填）
  }
}
```


### 3. 要点提示
#### 功能描述
医生双击药品时调用，返回该药品的要点提示（如医保提示、抗菌提示、说明书等）。

#### 请求信息
- URL：`/api/v1/medicine/info`
- 请求体结构：
```json
{
  "baseInfo": { // 基本信息
    "source": "HIS", // 固定值：调用方标识（必填）
    "hospCode": "H001", // 医院编码，即医疗机构代码（必填）
    "deptCode": "D002", // 科室代码（必填）
    "deptName": "内科", // 科室名称（必填）
    "doctor": {
      "code": "DR003", // 医生代码（必填）
      "name": "张三", // 医生姓名（必填）
      "type": "1", // 医生级别代码（必填）
      "typeName": "主治医师" // 医生级别名称（必填）
    }
  },
  "details": {
    "hospFlag": "op", // 门诊/住院标识：op-门诊；ip-住院（必填）
    "medicine": {
      "hisCode": "M001", // 医院药品代码（必填）
      "hisName": "硝苯地平片" // 药品名称
    }
  }
}
```


#### 响应数据
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "info": {
      "ym": "硝苯地平片", // 药名
      "sp": "XBDP", // 拼音缩写
    }
  }
}
```


## 接口调用说明
1. **处方分析**：医生开具处方后调用，建议在“保存处方前”触发，用于校验处方合理性。若返回严重问题（problemLevel=3），HIS可限制保存或要求医生填写理由。
2. **获取审核结果**：适用于审方中心流程，HIS可定期轮询该接口获取处方的人工审核状态，指导后续缴费、发药流程。
3. **要点提示**：医生双击药品时实时调用，为医生提供药品相关参考信息，辅助合理用药决策。


## 错误码说明
| 通用错误码 | 描述 | 处理建议 |
|------------|------|----------|
| 0 | 成功 | - |
| 400 | 请求参数错误 | 检查参数格式及必填项 |
| 401 | 未授权 | 确认baseInfo中的医生信息是否已登录 |
| 500 | 服务器内部错误 | 联系系统管理员排查 |

各接口业务错误码见对应响应说明。