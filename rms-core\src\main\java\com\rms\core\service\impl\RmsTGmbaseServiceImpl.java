package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTGmbaseMapper;
import com.rms.core.domain.RmsTGmbase;
import com.rms.core.service.IRmsTGmbaseService;

/**
 * 过敏基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class RmsTGmbaseServiceImpl implements IRmsTGmbaseService
{
    @Autowired
    private RmsTGmbaseMapper rmsTGmbaseMapper;

    /**
     * 查询过敏基础信息
     *
     * @param code 过敏基础信息主键
     * @return 过敏基础信息
     */
    @Override
    public RmsTGmbase selectRmsTGmbaseByCode(String code)
    {
        return rmsTGmbaseMapper.selectRmsTGmbaseByCode(code);
    }

    /**
     * 查询过敏基础信息列表
     *
     * @param rmsTGmbase 过敏基础信息
     * @return 过敏基础信息
     */
    @Override
    public List<RmsTGmbase> selectRmsTGmbaseList(RmsTGmbase rmsTGmbase)
    {
        return rmsTGmbaseMapper.selectRmsTGmbaseList(rmsTGmbase);
    }

    /**
     * 新增过敏基础信息
     *
     * @param rmsTGmbase 过敏基础信息
     * @return 结果
     */
    @Override
    public int insertRmsTGmbase(RmsTGmbase rmsTGmbase)
    {
        return rmsTGmbaseMapper.insertRmsTGmbase(rmsTGmbase);
    }

    /**
     * 修改过敏基础信息
     *
     * @param rmsTGmbase 过敏基础信息
     * @return 结果
     */
    @Override
    public int updateRmsTGmbase(RmsTGmbase rmsTGmbase)
    {
        return rmsTGmbaseMapper.updateRmsTGmbase(rmsTGmbase);
    }

    /**
     * 批量删除过敏基础信息
     *
     * @param codes 需要删除的过敏基础信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTGmbaseByCodes(String[] codes)
    {
        return rmsTGmbaseMapper.deleteRmsTGmbaseByCodes(codes);
    }

    /**
     * 删除过敏基础信息信息
     *
     * @param code 过敏基础信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTGmbaseByCode(String code)
    {
        return rmsTGmbaseMapper.deleteRmsTGmbaseByCode(code);
    }
}
