package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 处方审核意见对象 rms_t_pres_sh
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class RmsTPresSh extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 处方编码 */
    @Excel(name = "处方编码")
    private String code;

    /** 审核标志：11-审核不通过；12-审核通过； */
    @Excel(name = "审核标志：11-审核不通过；12-审核通过；")
    private Long flag;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String text;

    /** 医师ID */
    @Excel(name = "医师ID")
    private Long userId;

    /** 医师姓名 */
    @Excel(name = "医师姓名")
    private String nickName;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public void setFlag(Long flag)
    {
        this.flag = flag;
    }

    public Long getFlag()
    {
        return flag;
    }

    public void setText(String text)
    {
        this.text = text;
    }

    public String getText()
    {
        return text;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getNickName()
    {
        return nickName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("code", getCode())
            .append("flag", getFlag())
            .append("text", getText())
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("createTime", getCreateTime())
            .toString();
    }
}
