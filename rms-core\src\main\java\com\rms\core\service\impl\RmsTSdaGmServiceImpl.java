package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaGmMapper;
import com.rms.core.domain.RmsTSdaGm;
import com.rms.core.service.IRmsTSdaGmService;

/**
 * 药品过敏信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class RmsTSdaGmServiceImpl implements IRmsTSdaGmService
{
    @Autowired
    private RmsTSdaGmMapper rmsTSdaGmMapper;

    /**
     * 查询药品过敏信息
     *
     * @param id 药品过敏信息主键
     * @return 药品过敏信息
     */
    @Override
    public RmsTSdaGm selectRmsTSdaGmById(Long id)
    {
        return rmsTSdaGmMapper.selectRmsTSdaGmById(id);
    }

    /**
     * 查询药品过敏信息列表
     *
     * @param rmsTSdaGm 药品过敏信息
     * @return 药品过敏信息
     */
    @Override
    public List<RmsTSdaGm> selectRmsTSdaGmList(RmsTSdaGm rmsTSdaGm)
    {
        return rmsTSdaGmMapper.selectRmsTSdaGmList(rmsTSdaGm);
    }

    /**
     * 新增药品过敏信息
     *
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    @Override
    public int insertRmsTSdaGm(RmsTSdaGm rmsTSdaGm)
    {
        return rmsTSdaGmMapper.insertRmsTSdaGm(rmsTSdaGm);
    }

    /**
     * 修改药品过敏信息
     *
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    @Override
    public int updateRmsTSdaGm(RmsTSdaGm rmsTSdaGm)
    {
        return rmsTSdaGmMapper.updateRmsTSdaGm(rmsTSdaGm);
    }

    /**
     * 批量删除药品过敏信息
     *
     * @param ids 需要删除的药品过敏信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaGmByIds(Long[] ids)
    {
        return rmsTSdaGmMapper.deleteRmsTSdaGmByIds(ids);
    }

    /**
     * 删除药品过敏信息信息
     *
     * @param id 药品过敏信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaGmById(Long id)
    {
        return rmsTSdaGmMapper.deleteRmsTSdaGmById(id);
    }
}
