package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSdaGytj;

/**
 * 标准药品给药途径表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface RmsTSdaGytjMapper 
{
    /**
     * 查询标准药品给药途径表
     * 
     * @param id 标准药品给药途径表主键
     * @return 标准药品给药途径表
     */
    public RmsTSdaGytj selectRmsTSdaGytjById(Long id);

    /**
     * 查询标准药品给药途径表列表
     * 
     * @param rmsTSdaGytj 标准药品给药途径表
     * @return 标准药品给药途径表集合
     */
    public List<RmsTSdaGytj> selectRmsTSdaGytjList(RmsTSdaGytj rmsTSdaGytj);

    /**
     * 新增标准药品给药途径表
     * 
     * @param rmsTSdaGytj 标准药品给药途径表
     * @return 结果
     */
    public int insertRmsTSdaGytj(RmsTSdaGytj rmsTSdaGytj);

    /**
     * 修改标准药品给药途径表
     * 
     * @param rmsTSdaGytj 标准药品给药途径表
     * @return 结果
     */
    public int updateRmsTSdaGytj(RmsTSdaGytj rmsTSdaGytj);

    /**
     * 删除标准药品给药途径表
     * 
     * @param id 标准药品给药途径表主键
     * @return 结果
     */
    public int deleteRmsTSdaGytjById(Long id);

    /**
     * 批量删除标准药品给药途径表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaGytjByIds(Long[] ids);
}
