package com.rms.core.domain.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 患者信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class PatientDTO {

    /**
     * 患者姓名
     */
    @NotBlank(message = "患者姓名不能为空")
    private String name;

    /**
     * 是否婴幼儿：0-否；1-是
     */
    private Integer isInfant;

    /**
     * 出生日期（YYYY-MM-DD）
     */
    @NotBlank(message = "出生日期不能为空")
    private String birth;

    /**
     * 性别：男/女/未知
     */
    @NotBlank(message = "性别不能为空")
    private String sex;

    /**
     * 体重（kg）
     */
    private Double weight;

    /**
     * 身高（cm）
     */
    private Double height;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 卡类型：0-社保卡；1-医保卡；2-医联卡；9-其他
     */
    private Integer cardType;

    /**
     * 卡号
     */
    private String cardCode;

    /**
     * 怀孕时间单位：天/周/月
     */
    private String pregnantUnit;

    /**
     * 怀孕时间（数字）
     */
    private Integer pregnant;

    /**
     * 过敏源信息
     */
    @Valid
    private List<AllergicDataDTO> allergicData;

    /**
     * 诊断信息
     */
    @Valid
    private List<DiagnoseDataDTO> diagnoseData;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getIsInfant() {
        return isInfant;
    }

    public void setIsInfant(Integer isInfant) {
        this.isInfant = isInfant;
    }

    public String getBirth() {
        return birth;
    }

    public void setBirth(String birth) {
        this.birth = birth;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getCardCode() {
        return cardCode;
    }

    public void setCardCode(String cardCode) {
        this.cardCode = cardCode;
    }

    public String getPregnantUnit() {
        return pregnantUnit;
    }

    public void setPregnantUnit(String pregnantUnit) {
        this.pregnantUnit = pregnantUnit;
    }

    public Integer getPregnant() {
        return pregnant;
    }

    public void setPregnant(Integer pregnant) {
        this.pregnant = pregnant;
    }

    public List<AllergicDataDTO> getAllergicData() {
        return allergicData;
    }

    public void setAllergicData(List<AllergicDataDTO> allergicData) {
        this.allergicData = allergicData;
    }

    public List<DiagnoseDataDTO> getDiagnoseData() {
        return diagnoseData;
    }

    public void setDiagnoseData(List<DiagnoseDataDTO> diagnoseData) {
        this.diagnoseData = diagnoseData;
    }

    @Override
    public String toString() {
        return "PatientDTO{" +
                "name='" + name + '\'' +
                ", isInfant=" + isInfant +
                ", birth='" + birth + '\'' +
                ", sex='" + sex + '\'' +
                ", weight=" + weight +
                ", height=" + height +
                ", idCard='" + idCard + '\'' +
                ", cardType=" + cardType +
                ", cardCode='" + cardCode + '\'' +
                ", pregnantUnit='" + pregnantUnit + '\'' +
                ", pregnant=" + pregnant +
                ", allergicData=" + allergicData +
                ", diagnoseData=" + diagnoseData +
                '}';
    }
}
