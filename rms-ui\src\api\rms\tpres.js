import request from '@/utils/request'

// 查询处方信息列表
export function listTpres(query) {
  return request({
    url: '/rms/tpres/list',
    method: 'get',
    params: query
  })
}

// 查询处方信息详细
export function getTpres(code) {
  return request({
    url: '/rms/tpres/' + code,
    method: 'get'
  })
}

// 新增处方信息
export function addTpres(data) {
  return request({
    url: '/rms/tpres',
    method: 'post',
    data: data
  })
}

// 修改处方信息
export function updateTpres(data) {
  return request({
    url: '/rms/tpres',
    method: 'put',
    data: data
  })
}

// 删除处方信息
export function delTpres(code) {
  return request({
    url: '/rms/tpres/' + code,
    method: 'delete'
  })
}
