package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaGm;
import com.rms.core.service.IRmsTSdaGmService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 药品过敏信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/rms/tsdagm")
public class RmsTSdaGmController extends BaseController
{
    @Autowired
    private IRmsTSdaGmService rmsTSdaGmService;

    /**
     * 查询药品过敏信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagm:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaGm rmsTSdaGm)
    {
        startPage();
        List<RmsTSdaGm> list = rmsTSdaGmService.selectRmsTSdaGmList(rmsTSdaGm);
        return getDataTable(list);
    }

    /**
     * 导出药品过敏信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagm:export')")
    @Log(title = "药品过敏信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaGm rmsTSdaGm)
    {
        List<RmsTSdaGm> list = rmsTSdaGmService.selectRmsTSdaGmList(rmsTSdaGm);
        ExcelUtil<RmsTSdaGm> util = new ExcelUtil<RmsTSdaGm>(RmsTSdaGm.class);
        util.exportExcel(response, list, "药品过敏信息数据");
    }

    /**
     * 获取药品过敏信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagm:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaGmService.selectRmsTSdaGmById(id));
    }

    /**
     * 新增药品过敏信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagm:add')")
    @Log(title = "药品过敏信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaGm rmsTSdaGm)
    {
        return toAjax(rmsTSdaGmService.insertRmsTSdaGm(rmsTSdaGm));
    }

    /**
     * 修改药品过敏信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagm:edit')")
    @Log(title = "药品过敏信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaGm rmsTSdaGm)
    {
        return toAjax(rmsTSdaGmService.updateRmsTSdaGm(rmsTSdaGm));
    }

    /**
     * 删除药品过敏信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagm:remove')")
    @Log(title = "药品过敏信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaGmService.deleteRmsTSdaGmByIds(ids));
    }
}
