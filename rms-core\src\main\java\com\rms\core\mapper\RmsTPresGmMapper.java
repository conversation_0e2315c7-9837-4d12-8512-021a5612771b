package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTPresGm;

/**
 * 处方过敏信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface RmsTPresGmMapper 
{
    /**
     * 查询处方过敏信息
     * 
     * @param code 处方过敏信息主键
     * @return 处方过敏信息
     */
    public RmsTPresGm selectRmsTPresGmByCode(String code);

    /**
     * 查询处方过敏信息列表
     * 
     * @param rmsTPresGm 处方过敏信息
     * @return 处方过敏信息集合
     */
    public List<RmsTPresGm> selectRmsTPresGmList(RmsTPresGm rmsTPresGm);

    /**
     * 新增处方过敏信息
     * 
     * @param rmsTPresGm 处方过敏信息
     * @return 结果
     */
    public int insertRmsTPresGm(RmsTPresGm rmsTPresGm);

    /**
     * 修改处方过敏信息
     * 
     * @param rmsTPresGm 处方过敏信息
     * @return 结果
     */
    public int updateRmsTPresGm(RmsTPresGm rmsTPresGm);

    /**
     * 删除处方过敏信息
     * 
     * @param code 处方过敏信息主键
     * @return 结果
     */
    public int deleteRmsTPresGmByCode(String code);

    /**
     * 批量删除处方过敏信息
     * 
     * @param codes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTPresGmByCodes(String[] codes);
}
