<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTTjBaseMapper">
    
    <resultMap type="RmsTTjBase" id="RmsTTjBaseResult">
        <result property="name"    column="name"    />
        <result property="ms"    column="ms"    />
        <result property="dm"    column="dm"    />
    </resultMap>

    <sql id="selectRmsTTjBaseVo">
        select name, ms, dm from rms_t_tj_base
    </sql>

    <select id="selectRmsTTjBaseList" parameterType="RmsTTjBase" resultMap="RmsTTjBaseResult">
        <include refid="selectRmsTTjBaseVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="ms != null  and ms != ''"> and ms = #{ms}</if>
            <if test="dm != null  and dm != ''"> and dm = #{dm}</if>
        </where>
    </select>
    
    <select id="selectRmsTTjBaseByName" parameterType="String" resultMap="RmsTTjBaseResult">
        <include refid="selectRmsTTjBaseVo"/>
        where name = #{name}
    </select>

    <insert id="insertRmsTTjBase" parameterType="RmsTTjBase">
        insert into rms_t_tj_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="ms != null">ms,</if>
            <if test="dm != null">dm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="ms != null">#{ms},</if>
            <if test="dm != null">#{dm},</if>
         </trim>
    </insert>

    <update id="updateRmsTTjBase" parameterType="RmsTTjBase">
        update rms_t_tj_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="ms != null">ms = #{ms},</if>
            <if test="dm != null">dm = #{dm},</if>
        </trim>
        where name = #{name}
    </update>

    <delete id="deleteRmsTTjBaseByName" parameterType="String">
        delete from rms_t_tj_base where name = #{name}
    </delete>

    <delete id="deleteRmsTTjBaseByNames" parameterType="String">
        delete from rms_t_tj_base where name in 
        <foreach item="name" collection="array" open="(" separator="," close=")">
            #{name}
        </foreach>
    </delete>
</mapper>