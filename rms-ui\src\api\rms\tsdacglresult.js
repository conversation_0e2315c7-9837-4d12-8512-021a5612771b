import request from '@/utils/request'

// 查询药品常规用量规则列表
export function listTsdacglresult(query) {
  return request({
    url: '/rms/tsdacglresult/list',
    method: 'get',
    params: query
  })
}

// 查询药品常规用量规则详细
export function getTsdacglresult(conditionId) {
  return request({
    url: '/rms/tsdacglresult/' + conditionId,
    method: 'get'
  })
}

// 新增药品常规用量规则
export function addTsdacglresult(data) {
  return request({
    url: '/rms/tsdacglresult',
    method: 'post',
    data: data
  })
}

// 修改药品常规用量规则
export function updateTsdacglresult(data) {
  return request({
    url: '/rms/tsdacglresult',
    method: 'put',
    data: data
  })
}

// 删除药品常规用量规则
export function delTsdacglresult(conditionId) {
  return request({
    url: '/rms/tsdacglresult/' + conditionId,
    method: 'delete'
  })
}
