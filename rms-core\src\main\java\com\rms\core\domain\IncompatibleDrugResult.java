package com.rms.core.domain;

import com.rms.common.core.domain.BaseEntity;

/**
 * 配伍禁忌查询结果对象
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class IncompatibleDrugResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** A药物ID */
    private Long yaowua;

    /** B药物ID */
    private Long yaowub;

    /** A药物名称 */
    private String yaowuaName;

    /** B药物名称 */
    private String yaowubName;

    /** 相互作用效果 */
    private String effect;

    /** 作用机制 */
    private String mechanism;

    /** 相关药物 */
    private String relatedrug;

    /** 参考文献 */
    private String reference;

    /** 重要性标识：0-严重；1-一般；2-其它 */
    private String impBs;

    /** 建议 */
    private String recommandations;

    /** 建议标识：pwjj-配伍禁忌；xhzy-相互作用 */
    private String sugflag;

    /** 结果等级：0-禁忌，1-问题，2-提示 */
    private String result;

    /** 是否屏蔽 */
    private String ispb;

    /** 备注 */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getYaowua() {
        return yaowua;
    }

    public void setYaowua(Long yaowua) {
        this.yaowua = yaowua;
    }

    public Long getYaowub() {
        return yaowub;
    }

    public void setYaowub(Long yaowub) {
        this.yaowub = yaowub;
    }

    public String getYaowuaName() {
        return yaowuaName;
    }

    public void setYaowuaName(String yaowuaName) {
        this.yaowuaName = yaowuaName;
    }

    public String getYaowubName() {
        return yaowubName;
    }

    public void setYaowubName(String yaowubName) {
        this.yaowubName = yaowubName;
    }

    public String getEffect() {
        return effect;
    }

    public void setEffect(String effect) {
        this.effect = effect;
    }

    public String getMechanism() {
        return mechanism;
    }

    public void setMechanism(String mechanism) {
        this.mechanism = mechanism;
    }

    public String getRelatedrug() {
        return relatedrug;
    }

    public void setRelatedrug(String relatedrug) {
        this.relatedrug = relatedrug;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getImpBs() {
        return impBs;
    }

    public void setImpBs(String impBs) {
        this.impBs = impBs;
    }

    public String getRecommandations() {
        return recommandations;
    }

    public void setRecommandations(String recommandations) {
        this.recommandations = recommandations;
    }

    public String getSugflag() {
        return sugflag;
    }

    public void setSugflag(String sugflag) {
        this.sugflag = sugflag;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getIspb() {
        return ispb;
    }

    public void setIspb(String ispb) {
        this.ispb = ispb;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "IncompatibleDrugResult{" +
                "id=" + id +
                ", yaowua=" + yaowua +
                ", yaowub=" + yaowub +
                ", yaowuaName='" + yaowuaName + '\'' +
                ", yaowubName='" + yaowubName + '\'' +
                ", effect='" + effect + '\'' +
                ", mechanism='" + mechanism + '\'' +
                ", relatedrug='" + relatedrug + '\'' +
                ", reference='" + reference + '\'' +
                ", impBs='" + impBs + '\'' +
                ", recommandations='" + recommandations + '\'' +
                ", sugflag='" + sugflag + '\'' +
                ", result='" + result + '\'' +
                ", ispb='" + ispb + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
} 