# RMS API 调用演示程序

## 概述

本演示程序展示了如何使用Java调用合理用药与事前审方系统（RMS）的三个核心API接口：

1. **处方分析接口** - 分析处方合理性，检查配伍禁忌、用药剂量、过敏风险等
2. **处方审核结果查询接口** - 查询指定处方的审核状态
3. **药品信息查询接口** - 获取药品的要点提示信息

## 文件说明

- `RmsApiDemo.java` - 主要的演示程序，包含完整的API调用逻辑
- `run_demo.bat` - Windows运行脚本
- `run_demo.sh` - Linux/Mac运行脚本
- `README.md` - 本说明文件

## 前置条件

1. **Java环境**：确保已安装Java 8或更高版本
2. **RMS系统**：确保RMS系统已启动并运行在 `http://localhost:8080`
3. **网络连接**：确保网络连接正常

## 快速开始

### Windows用户

1. 双击运行 `run_demo.bat` 脚本
2. 或在命令行中执行：
   ```cmd
   run_demo.bat
   ```

### Linux/Mac用户

1. 给脚本执行权限：
   ```bash
   chmod +x run_demo.sh
   ```

2. 运行脚本：
   ```bash
   ./run_demo.sh
   ```

### 手动运行

如果脚本无法运行，可以手动执行以下命令：

```bash
# 编译
javac RmsApiDemo.java

# 运行
java RmsApiDemo

# 清理
rm RmsApiDemo.class  # Linux/Mac
del RmsApiDemo.class # Windows
```

## 程序输出示例

```
================================================================================
RMS API 调用演示程序
================================================================================
API服务地址: http://localhost:8080/api/v1
请确保RMS系统已启动并运行在 http://localhost:8080
================================================================================
✅ API服务连接正常

==================================================
【步骤1】调用处方分析接口
==================================================
请求URL: http://localhost:8080/api/v1/prescription/analyze
🔗 建立连接...
📤 请求数据已发送 (2156 字节)
📥 HTTP状态: 200 OK
✅ 请求成功
📄 响应长度: 156 字符
🔌 连接已关闭
响应结果: {"code":200,"msg":"分析成功","data":{"problemLevel":0,"problemDetail":"","doubleSignFlag":0,"doubleSignDrugs":""}}
📊 处方分析结果解析：
✅ 分析成功
   问题级别：无问题
   双签要求：不需要双签

==================================================
【步骤2】查询处方审核结果
==================================================
...

🎉 所有API调用演示完成！
```

## API接口详情

### 1. 处方分析接口

- **URL**: `POST /api/v1/prescription/analyze`
- **功能**: 分析处方合理性
- **输入**: 完整的处方信息（患者、诊断、药品等）
- **输出**: 问题级别、双签标志、问题详情

### 2. 处方审核结果查询接口

- **URL**: `POST /api/v1/prescription/check-result`
- **功能**: 查询处方审核状态
- **输入**: 医院编码、就诊号、处方号
- **输出**: 审核状态（0-通过，1-待审核，2-不通过）

### 3. 药品信息查询接口

- **URL**: `POST /api/v1/medicine/info`
- **功能**: 获取药品要点提示
- **输入**: 医院编码、药品代码
- **输出**: 药品名称、拼音缩写等信息

## 响应格式

所有API都返回统一的JSON格式：

```json
{
  "code": 200,        // 状态码：200-成功，其他-失败
  "msg": "操作成功",   // 状态描述
  "data": {}          // 业务数据
}
```

## 故障排查

如果程序运行失败，请按以下步骤排查：

1. **检查Java环境**
   ```bash
   java -version
   javac -version
   ```

2. **检查RMS系统状态**
   - 确认系统已启动
   - 访问 `http://localhost:8080` 检查服务是否可用

3. **检查网络连接**
   ```bash
   ping localhost
   telnet localhost 8080
   ```

4. **查看详细错误信息**
   - 程序会输出详细的错误信息和建议
   - 查看RMS系统日志获取更多信息

## 常见错误

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|---------|
| `API服务不可用` | RMS系统未启动 | 启动RMS系统 |
| `Connection refused` | 端口8080未开放 | 检查端口和防火墙设置 |
| `HTTP错误 400` | 请求参数错误 | 检查请求数据格式 |
| `HTTP错误 500` | 服务器内部错误 | 查看服务端日志 |

## 扩展开发

在实际项目中集成RMS API时，建议进行以下改进：

1. **使用专业JSON库**：如Jackson、Gson等
2. **添加重试机制**：处理网络不稳定情况
3. **实现连接池**：提高性能
4. **添加日志框架**：如Logback、Log4j
5. **配置文件管理**：外部化配置
6. **单元测试**：确保代码质量
7. **异步处理**：提高并发性能

## 技术支持

如有问题，请联系：
- RMS系统管理员
- 查看RMS系统文档
- 检查系统日志文件

## 版本信息

- 版本：1.0
- 更新日期：2025-07-19
- 兼容性：Java 8+
- RMS API版本：v1
