<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaCglResultMapper">
    
    <resultMap type="RmsTSdaCglResult" id="RmsTSdaCglResultResult">
        <result property="conditionId"    column="condition_id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="recoType"    column="reco_type"    />
        <result property="ylMin"    column="yl_min"    />
        <result property="ylMax"    column="yl_max"    />
        <result property="ylUnit"    column="yl_unit"    />
    </resultMap>

    <sql id="selectRmsTSdaCglResultVo">
        select condition_id, sda_id, reco_type, yl_min, yl_max, yl_unit from rms_t_sda_cgl_result
    </sql>

    <select id="selectRmsTSdaCglResultList" parameterType="RmsTSdaCglResult" resultMap="RmsTSdaCglResultResult">
        <include refid="selectRmsTSdaCglResultVo"/>
        <where>  
            <if test="conditionId != null "> and condition_id = #{conditionId}</if>
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="recoType != null "> and reco_type = #{recoType}</if>
            <if test="ylMin != null "> and yl_min = #{ylMin}</if>
            <if test="ylMax != null "> and yl_max = #{ylMax}</if>
            <if test="ylUnit != null  and ylUnit != ''"> and yl_unit = #{ylUnit}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaCglResultByConditionId" parameterType="Long" resultMap="RmsTSdaCglResultResult">
        <include refid="selectRmsTSdaCglResultVo"/>
        where condition_id = #{conditionId}
    </select>

    <insert id="insertRmsTSdaCglResult" parameterType="RmsTSdaCglResult">
        insert into rms_t_sda_cgl_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="conditionId != null">condition_id,</if>
            <if test="sdaId != null">sda_id,</if>
            <if test="recoType != null">reco_type,</if>
            <if test="ylMin != null">yl_min,</if>
            <if test="ylMax != null">yl_max,</if>
            <if test="ylUnit != null">yl_unit,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="conditionId != null">#{conditionId},</if>
            <if test="sdaId != null">#{sdaId},</if>
            <if test="recoType != null">#{recoType},</if>
            <if test="ylMin != null">#{ylMin},</if>
            <if test="ylMax != null">#{ylMax},</if>
            <if test="ylUnit != null">#{ylUnit},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaCglResult" parameterType="RmsTSdaCglResult">
        update rms_t_sda_cgl_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="recoType != null">reco_type = #{recoType},</if>
            <if test="ylMin != null">yl_min = #{ylMin},</if>
            <if test="ylMax != null">yl_max = #{ylMax},</if>
            <if test="ylUnit != null">yl_unit = #{ylUnit},</if>
        </trim>
        where condition_id = #{conditionId}
    </update>

    <delete id="deleteRmsTSdaCglResultByConditionId" parameterType="Long">
        delete from rms_t_sda_cgl_result where condition_id = #{conditionId}
    </delete>

    <delete id="deleteRmsTSdaCglResultByConditionIds" parameterType="String">
        delete from rms_t_sda_cgl_result where condition_id in 
        <foreach item="conditionId" collection="array" open="(" separator="," close=")">
            #{conditionId}
        </foreach>
    </delete>
</mapper>