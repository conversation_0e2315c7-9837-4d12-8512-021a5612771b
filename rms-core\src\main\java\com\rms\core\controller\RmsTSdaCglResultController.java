package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaCglResult;
import com.rms.core.service.IRmsTSdaCglResultService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 药品常规用量规则Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/rms/tsdacglresult")
public class RmsTSdaCglResultController extends BaseController
{
    @Autowired
    private IRmsTSdaCglResultService rmsTSdaCglResultService;

    /**
     * 查询药品常规用量规则列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglresult:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaCglResult rmsTSdaCglResult)
    {
        startPage();
        List<RmsTSdaCglResult> list = rmsTSdaCglResultService.selectRmsTSdaCglResultList(rmsTSdaCglResult);
        return getDataTable(list);
    }

    /**
     * 导出药品常规用量规则列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglresult:export')")
    @Log(title = "药品常规用量规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaCglResult rmsTSdaCglResult)
    {
        List<RmsTSdaCglResult> list = rmsTSdaCglResultService.selectRmsTSdaCglResultList(rmsTSdaCglResult);
        ExcelUtil<RmsTSdaCglResult> util = new ExcelUtil<RmsTSdaCglResult>(RmsTSdaCglResult.class);
        util.exportExcel(response, list, "药品常规用量规则数据");
    }

    /**
     * 获取药品常规用量规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglresult:query')")
    @GetMapping(value = "/{conditionId}")
    public AjaxResult getInfo(@PathVariable("conditionId") Long conditionId)
    {
        return success(rmsTSdaCglResultService.selectRmsTSdaCglResultByConditionId(conditionId));
    }

    /**
     * 新增药品常规用量规则
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglresult:add')")
    @Log(title = "药品常规用量规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaCglResult rmsTSdaCglResult)
    {
        return toAjax(rmsTSdaCglResultService.insertRmsTSdaCglResult(rmsTSdaCglResult));
    }

    /**
     * 修改药品常规用量规则
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglresult:edit')")
    @Log(title = "药品常规用量规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaCglResult rmsTSdaCglResult)
    {
        return toAjax(rmsTSdaCglResultService.updateRmsTSdaCglResult(rmsTSdaCglResult));
    }

    /**
     * 删除药品常规用量规则
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglresult:remove')")
    @Log(title = "药品常规用量规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{conditionIds}")
    public AjaxResult remove(@PathVariable Long[] conditionIds)
    {
        return toAjax(rmsTSdaCglResultService.deleteRmsTSdaCglResultByConditionIds(conditionIds));
    }
}
