<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsCfwtlbMapper">
    
    <resultMap type="RmsCfwtlb" id="RmsCfwtlbResult">
        <result property="cfwtbh"    column="cfwtbh"    />
        <result property="cfwtname"    column="cfwtname"    />
        <result property="cfwtxq"    column="cfwtxq"    />
        <result property="cfwtlb"    column="cfwtlb"    />
        <result property="cfwtlbname"    column="cfwtlbname"    />
        <result property="wtcode"    column="wtcode"    />
        <result property="bz"    column="bz"    />
    </resultMap>

    <sql id="selectRmsCfwtlbVo">
        select cfwtbh, cfwtname, cfwtxq, cfwtlb, cfwtlbname, wtcode, bz from rms_cfwtlb
    </sql>

    <select id="selectRmsCfwtlbList" parameterType="RmsCfwtlb" resultMap="RmsCfwtlbResult">
        <include refid="selectRmsCfwtlbVo"/>
        <where>  
            <if test="cfwtname != null  and cfwtname != ''"> and cfwtname like concat('%', #{cfwtname}, '%')</if>
            <if test="cfwtxq != null  and cfwtxq != ''"> and cfwtxq = #{cfwtxq}</if>
            <if test="cfwtlb != null  and cfwtlb != ''"> and cfwtlb = #{cfwtlb}</if>
            <if test="cfwtlbname != null  and cfwtlbname != ''"> and cfwtlbname like concat('%', #{cfwtlbname}, '%')</if>
            <if test="wtcode != null  and wtcode != ''"> and wtcode = #{wtcode}</if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
        </where>
    </select>
    
    <select id="selectRmsCfwtlbByCfwtbh" parameterType="String" resultMap="RmsCfwtlbResult">
        <include refid="selectRmsCfwtlbVo"/>
        where cfwtbh = #{cfwtbh}
    </select>

    <insert id="insertRmsCfwtlb" parameterType="RmsCfwtlb">
        insert into rms_cfwtlb
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cfwtbh != null">cfwtbh,</if>
            <if test="cfwtname != null">cfwtname,</if>
            <if test="cfwtxq != null">cfwtxq,</if>
            <if test="cfwtlb != null">cfwtlb,</if>
            <if test="cfwtlbname != null">cfwtlbname,</if>
            <if test="wtcode != null">wtcode,</if>
            <if test="bz != null">bz,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cfwtbh != null">#{cfwtbh},</if>
            <if test="cfwtname != null">#{cfwtname},</if>
            <if test="cfwtxq != null">#{cfwtxq},</if>
            <if test="cfwtlb != null">#{cfwtlb},</if>
            <if test="cfwtlbname != null">#{cfwtlbname},</if>
            <if test="wtcode != null">#{wtcode},</if>
            <if test="bz != null">#{bz},</if>
         </trim>
    </insert>

    <update id="updateRmsCfwtlb" parameterType="RmsCfwtlb">
        update rms_cfwtlb
        <trim prefix="SET" suffixOverrides=",">
            <if test="cfwtname != null">cfwtname = #{cfwtname},</if>
            <if test="cfwtxq != null">cfwtxq = #{cfwtxq},</if>
            <if test="cfwtlb != null">cfwtlb = #{cfwtlb},</if>
            <if test="cfwtlbname != null">cfwtlbname = #{cfwtlbname},</if>
            <if test="wtcode != null">wtcode = #{wtcode},</if>
            <if test="bz != null">bz = #{bz},</if>
        </trim>
        where cfwtbh = #{cfwtbh}
    </update>

    <delete id="deleteRmsCfwtlbByCfwtbh" parameterType="String">
        delete from rms_cfwtlb where cfwtbh = #{cfwtbh}
    </delete>

    <delete id="deleteRmsCfwtlbByCfwtbhs" parameterType="String">
        delete from rms_cfwtlb where cfwtbh in 
        <foreach item="cfwtbh" collection="array" open="(" separator="," close=")">
            #{cfwtbh}
        </foreach>
    </delete>
</mapper>