import request from '@/utils/request'

// 查询诊断基础信息表列表
export function listTicd10base(query) {
  return request({
    url: '/rms/ticd10base/list',
    method: 'get',
    params: query
  })
}

// 查询诊断基础信息表详细
export function getTicd10base(id) {
  return request({
    url: '/rms/ticd10base/' + id,
    method: 'get'
  })
}

// 新增诊断基础信息表
export function addTicd10base(data) {
  return request({
    url: '/rms/ticd10base',
    method: 'post',
    data: data
  })
}

// 修改诊断基础信息表
export function updateTicd10base(data) {
  return request({
    url: '/rms/ticd10base',
    method: 'put',
    data: data
  })
}

// 删除诊断基础信息表
export function delTicd10base(id) {
  return request({
    url: '/rms/ticd10base/' + id,
    method: 'delete'
  })
}
