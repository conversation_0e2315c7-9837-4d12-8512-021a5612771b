package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTPresGmMapper;
import com.rms.core.domain.RmsTPresGm;
import com.rms.core.service.IRmsTPresGmService;

/**
 * 处方过敏信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class RmsTPresGmServiceImpl implements IRmsTPresGmService
{
    @Autowired
    private RmsTPresGmMapper rmsTPresGmMapper;

    /**
     * 查询处方过敏信息
     *
     * @param code 处方过敏信息主键
     * @return 处方过敏信息
     */
    @Override
    public RmsTPresGm selectRmsTPresGmByCode(String code)
    {
        return rmsTPresGmMapper.selectRmsTPresGmByCode(code);
    }

    /**
     * 查询处方过敏信息列表
     *
     * @param rmsTPresGm 处方过敏信息
     * @return 处方过敏信息
     */
    @Override
    public List<RmsTPresGm> selectRmsTPresGmList(RmsTPresGm rmsTPresGm)
    {
        return rmsTPresGmMapper.selectRmsTPresGmList(rmsTPresGm);
    }

    /**
     * 新增处方过敏信息
     *
     * @param rmsTPresGm 处方过敏信息
     * @return 结果
     */
    @Override
    public int insertRmsTPresGm(RmsTPresGm rmsTPresGm)
    {
        return rmsTPresGmMapper.insertRmsTPresGm(rmsTPresGm);
    }

    /**
     * 修改处方过敏信息
     *
     * @param rmsTPresGm 处方过敏信息
     * @return 结果
     */
    @Override
    public int updateRmsTPresGm(RmsTPresGm rmsTPresGm)
    {
        return rmsTPresGmMapper.updateRmsTPresGm(rmsTPresGm);
    }

    /**
     * 批量删除处方过敏信息
     *
     * @param codes 需要删除的处方过敏信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresGmByCodes(String[] codes)
    {
        return rmsTPresGmMapper.deleteRmsTPresGmByCodes(codes);
    }

    /**
     * 删除处方过敏信息信息
     *
     * @param code 处方过敏信息主键
     * @return 结果
     */
    @Override
    public int deleteRmsTPresGmByCode(String code)
    {
        return rmsTPresGmMapper.deleteRmsTPresGmByCode(code);
    }
}
