<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaMapper">
    
    <resultMap type="RmsTSda" id="RmsTSdaResult">
        <result property="ID"    column="ID"    />
        <result property="Ym"    column="Ym"    />
        <result property="Sp"    column="Sp"    />
        <result property="hypy"    column="hypy"    />
        <result property="UnitRem"    column="UnitRem"    />
        <result property="tymc"    column="tymc"    />
        <result property="yymc"    column="yymc"    />
        <result property="hxmc"    column="hxmc"    />
        <result property="zycf"    column="zycf"    />
        <result property="fzs"    column="fzs"    />
        <result property="fzl"    column="fzl"    />
        <result property="xz"    column="xz"    />
        <result property="yldl"    column="yldl"    />
        <result property="yddlx"    column="yddlx"    />
        <result property="syz"    column="syz"    />
        <result property="yfyl"    column="yfyl"    />
        <result property="blfy"    column="blfy"    />
        <result property="jjz"    column="jjz"    />
        <result property="zysx"    column="zysx"    />
        <result property="yfyy"    column="yfyy"    />
        <result property="etyy"    column="etyy"    />
        <result property="lnryy"    column="lnryy"    />
        <result property="xhzy"    column="xhzy"    />
        <result property="ywgl"    column="ywgl"    />
        <result property="ywgg"    column="ywgg"    />
        <result property="cctj"    column="cctj"    />
        <result property="ydts"    column="ydts"    />
        <result property="ylgzd"    column="ylgzd"    />
        <result property="fygzd"    column="fygzd"    />
        <result property="ydgzd"    column="ydgzd"    />
        <result property="jxbs2"    column="jxbs_2"    />
        <result property="zxybs"    column="zxybs"    />
        <result property="ly"    column="ly"    />
        <result property="lyBs"    column="ly_bs"    />
        <result property="scqy"    column="scqy"    />
        <result property="bs"    column="bs"    />
        <result property="pzwh"    column="pzwh"    />
        <result property="cpym"    column="cpym"    />
        <result property="cpsp"    column="cpsp"    />
        <result property="lnrBs"    column="lnr_bs"    />
        <result property="yfBs"    column="yf_bs"    />
        <result property="yfqsyBs"    column="yfqsy_bs"    />
        <result property="yfsyhBs"    column="yfsyh_bs"    />
        <result property="brBs"    column="br_bs"    />
        <result property="ggBs"    column="gg_bs"    />
        <result property="sgBs"    column="sg_bs"    />
        <result property="hggBs"    column="hgg_bs"    />
        <result property="hsgBs"    column="hsg_bs"    />
        <result property="agemin"    column="agemin"    />
        <result property="agemax"    column="agemax"    />
        <result property="gytjBs"    column="gytj_bs"    />
        <result property="gytjBs1"    column="gytj_bs1"    />
        <result property="gmdm"    column="gmdm"    />
        <result property="dc1"    column="Dc_1"    />
        <result property="dr1"    column="Dr_1"    />
        <result property="mid"    column="mid"    />
        <result property="dx"    column="dx"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRmsTSdaVo">
        select ID, Ym, Sp, hypy, UnitRem, tymc, yymc, hxmc, zycf, fzs, fzl, xz, yldl, yddlx, syz, yfyl, blfy, jjz, zysx, yfyy, etyy, lnryy, xhzy, ywgl, ywgg, cctj, ydts, ylgzd, fygzd, ydgzd, jxbs_2, zxybs, ly, ly_bs, scqy, bs, pzwh, cpym, cpsp, lnr_bs, yf_bs, yfqsy_bs, yfsyh_bs, br_bs, gg_bs, sg_bs, hgg_bs, hsg_bs, agemin, agemax, gytj_bs, gytj_bs1, gmdm, Dc_1, Dr_1, mid, dx, create_time, update_time from rms_t_sda
    </sql>

    <select id="selectRmsTSdaList" parameterType="RmsTSda" resultMap="RmsTSdaResult">
        <include refid="selectRmsTSdaVo"/>
        <where>  
            <if test="Ym != null  and Ym != ''"> and Ym = #{Ym}</if>
            <if test="Sp != null  and Sp != ''"> and Sp = #{Sp}</if>
            <if test="hypy != null  and hypy != ''"> and hypy = #{hypy}</if>
            <if test="UnitRem != null  and UnitRem != ''"> and UnitRem = #{UnitRem}</if>
            <if test="tymc != null  and tymc != ''"> and tymc = #{tymc}</if>
            <if test="yymc != null  and yymc != ''"> and yymc = #{yymc}</if>
            <if test="hxmc != null  and hxmc != ''"> and hxmc = #{hxmc}</if>
            <if test="zycf != null  and zycf != ''"> and zycf = #{zycf}</if>
            <if test="fzs != null  and fzs != ''"> and fzs = #{fzs}</if>
            <if test="fzl != null  and fzl != ''"> and fzl = #{fzl}</if>
            <if test="xz != null  and xz != ''"> and xz = #{xz}</if>
            <if test="yldl != null  and yldl != ''"> and yldl = #{yldl}</if>
            <if test="yddlx != null  and yddlx != ''"> and yddlx = #{yddlx}</if>
            <if test="syz != null  and syz != ''"> and syz = #{syz}</if>
            <if test="yfyl != null  and yfyl != ''"> and yfyl = #{yfyl}</if>
            <if test="blfy != null  and blfy != ''"> and blfy = #{blfy}</if>
            <if test="jjz != null  and jjz != ''"> and jjz = #{jjz}</if>
            <if test="zysx != null  and zysx != ''"> and zysx = #{zysx}</if>
            <if test="yfyy != null  and yfyy != ''"> and yfyy = #{yfyy}</if>
            <if test="etyy != null  and etyy != ''"> and etyy = #{etyy}</if>
            <if test="lnryy != null  and lnryy != ''"> and lnryy = #{lnryy}</if>
            <if test="xhzy != null  and xhzy != ''"> and xhzy = #{xhzy}</if>
            <if test="ywgl != null  and ywgl != ''"> and ywgl = #{ywgl}</if>
            <if test="ywgg != null  and ywgg != ''"> and ywgg = #{ywgg}</if>
            <if test="cctj != null  and cctj != ''"> and cctj = #{cctj}</if>
            <if test="ydts != null  and ydts != ''"> and ydts = #{ydts}</if>
            <if test="ylgzd != null "> and ylgzd = #{ylgzd}</if>
            <if test="fygzd != null "> and fygzd = #{fygzd}</if>
            <if test="ydgzd != null "> and ydgzd = #{ydgzd}</if>
            <if test="jxbs2 != null "> and jxbs_2 = #{jxbs2}</if>
            <if test="zxybs != null "> and zxybs = #{zxybs}</if>
            <if test="ly != null  and ly != ''"> and ly = #{ly}</if>
            <if test="lyBs != null  and lyBs != ''"> and ly_bs = #{lyBs}</if>
            <if test="scqy != null  and scqy != ''"> and scqy = #{scqy}</if>
            <if test="bs != null  and bs != ''"> and bs = #{bs}</if>
            <if test="pzwh != null  and pzwh != ''"> and pzwh = #{pzwh}</if>
            <if test="cpym != null  and cpym != ''"> and cpym = #{cpym}</if>
            <if test="cpsp != null  and cpsp != ''"> and cpsp = #{cpsp}</if>
            <if test="lnrBs != null  and lnrBs != ''"> and lnr_bs = #{lnrBs}</if>
            <if test="yfBs != null  and yfBs != ''"> and yf_bs = #{yfBs}</if>
            <if test="yfqsyBs != null  and yfqsyBs != ''"> and yfqsy_bs = #{yfqsyBs}</if>
            <if test="yfsyhBs != null  and yfsyhBs != ''"> and yfsyh_bs = #{yfsyhBs}</if>
            <if test="brBs != null  and brBs != ''"> and br_bs = #{brBs}</if>
            <if test="ggBs != null  and ggBs != ''"> and gg_bs = #{ggBs}</if>
            <if test="sgBs != null  and sgBs != ''"> and sg_bs = #{sgBs}</if>
            <if test="hggBs != null  and hggBs != ''"> and hgg_bs = #{hggBs}</if>
            <if test="hsgBs != null  and hsgBs != ''"> and hsg_bs = #{hsgBs}</if>
            <if test="agemin != null "> and agemin = #{agemin}</if>
            <if test="agemax != null "> and agemax = #{agemax}</if>
            <if test="gytjBs != null  and gytjBs != ''"> and gytj_bs = #{gytjBs}</if>
            <if test="gytjBs1 != null  and gytjBs1 != ''"> and gytj_bs1 = #{gytjBs1}</if>
            <if test="gmdm != null  and gmdm != ''"> and gmdm = #{gmdm}</if>
            <if test="dc1 != null  and dc1 != ''"> and Dc_1 = #{dc1}</if>
            <if test="dr1 != null "> and Dr_1 = #{dr1}</if>
            <if test="mid != null  and mid != ''"> and mid = #{mid}</if>
            <if test="dx != null  and dx != ''"> and dx = #{dx}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaByID" parameterType="Long" resultMap="RmsTSdaResult">
        <include refid="selectRmsTSdaVo"/>
        where ID = #{ID}
    </select>

    <insert id="insertRmsTSda" parameterType="RmsTSda">
        insert into rms_t_sda
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ID != null">ID,</if>
            <if test="Ym != null">Ym,</if>
            <if test="Sp != null">Sp,</if>
            <if test="hypy != null">hypy,</if>
            <if test="UnitRem != null">UnitRem,</if>
            <if test="tymc != null">tymc,</if>
            <if test="yymc != null">yymc,</if>
            <if test="hxmc != null">hxmc,</if>
            <if test="zycf != null">zycf,</if>
            <if test="fzs != null">fzs,</if>
            <if test="fzl != null">fzl,</if>
            <if test="xz != null">xz,</if>
            <if test="yldl != null">yldl,</if>
            <if test="yddlx != null">yddlx,</if>
            <if test="syz != null">syz,</if>
            <if test="yfyl != null">yfyl,</if>
            <if test="blfy != null">blfy,</if>
            <if test="jjz != null">jjz,</if>
            <if test="zysx != null">zysx,</if>
            <if test="yfyy != null">yfyy,</if>
            <if test="etyy != null">etyy,</if>
            <if test="lnryy != null">lnryy,</if>
            <if test="xhzy != null">xhzy,</if>
            <if test="ywgl != null">ywgl,</if>
            <if test="ywgg != null">ywgg,</if>
            <if test="cctj != null">cctj,</if>
            <if test="ydts != null">ydts,</if>
            <if test="ylgzd != null">ylgzd,</if>
            <if test="fygzd != null">fygzd,</if>
            <if test="ydgzd != null">ydgzd,</if>
            <if test="jxbs2 != null">jxbs_2,</if>
            <if test="zxybs != null">zxybs,</if>
            <if test="ly != null">ly,</if>
            <if test="lyBs != null">ly_bs,</if>
            <if test="scqy != null">scqy,</if>
            <if test="bs != null">bs,</if>
            <if test="pzwh != null">pzwh,</if>
            <if test="cpym != null">cpym,</if>
            <if test="cpsp != null">cpsp,</if>
            <if test="lnrBs != null">lnr_bs,</if>
            <if test="yfBs != null">yf_bs,</if>
            <if test="yfqsyBs != null">yfqsy_bs,</if>
            <if test="yfsyhBs != null">yfsyh_bs,</if>
            <if test="brBs != null">br_bs,</if>
            <if test="ggBs != null">gg_bs,</if>
            <if test="sgBs != null">sg_bs,</if>
            <if test="hggBs != null">hgg_bs,</if>
            <if test="hsgBs != null">hsg_bs,</if>
            <if test="agemin != null">agemin,</if>
            <if test="agemax != null">agemax,</if>
            <if test="gytjBs != null">gytj_bs,</if>
            <if test="gytjBs1 != null">gytj_bs1,</if>
            <if test="gmdm != null">gmdm,</if>
            <if test="dc1 != null">Dc_1,</if>
            <if test="dr1 != null">Dr_1,</if>
            <if test="mid != null">mid,</if>
            <if test="dx != null">dx,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ID != null">#{ID},</if>
            <if test="Ym != null">#{Ym},</if>
            <if test="Sp != null">#{Sp},</if>
            <if test="hypy != null">#{hypy},</if>
            <if test="UnitRem != null">#{UnitRem},</if>
            <if test="tymc != null">#{tymc},</if>
            <if test="yymc != null">#{yymc},</if>
            <if test="hxmc != null">#{hxmc},</if>
            <if test="zycf != null">#{zycf},</if>
            <if test="fzs != null">#{fzs},</if>
            <if test="fzl != null">#{fzl},</if>
            <if test="xz != null">#{xz},</if>
            <if test="yldl != null">#{yldl},</if>
            <if test="yddlx != null">#{yddlx},</if>
            <if test="syz != null">#{syz},</if>
            <if test="yfyl != null">#{yfyl},</if>
            <if test="blfy != null">#{blfy},</if>
            <if test="jjz != null">#{jjz},</if>
            <if test="zysx != null">#{zysx},</if>
            <if test="yfyy != null">#{yfyy},</if>
            <if test="etyy != null">#{etyy},</if>
            <if test="lnryy != null">#{lnryy},</if>
            <if test="xhzy != null">#{xhzy},</if>
            <if test="ywgl != null">#{ywgl},</if>
            <if test="ywgg != null">#{ywgg},</if>
            <if test="cctj != null">#{cctj},</if>
            <if test="ydts != null">#{ydts},</if>
            <if test="ylgzd != null">#{ylgzd},</if>
            <if test="fygzd != null">#{fygzd},</if>
            <if test="ydgzd != null">#{ydgzd},</if>
            <if test="jxbs2 != null">#{jxbs2},</if>
            <if test="zxybs != null">#{zxybs},</if>
            <if test="ly != null">#{ly},</if>
            <if test="lyBs != null">#{lyBs},</if>
            <if test="scqy != null">#{scqy},</if>
            <if test="bs != null">#{bs},</if>
            <if test="pzwh != null">#{pzwh},</if>
            <if test="cpym != null">#{cpym},</if>
            <if test="cpsp != null">#{cpsp},</if>
            <if test="lnrBs != null">#{lnrBs},</if>
            <if test="yfBs != null">#{yfBs},</if>
            <if test="yfqsyBs != null">#{yfqsyBs},</if>
            <if test="yfsyhBs != null">#{yfsyhBs},</if>
            <if test="brBs != null">#{brBs},</if>
            <if test="ggBs != null">#{ggBs},</if>
            <if test="sgBs != null">#{sgBs},</if>
            <if test="hggBs != null">#{hggBs},</if>
            <if test="hsgBs != null">#{hsgBs},</if>
            <if test="agemin != null">#{agemin},</if>
            <if test="agemax != null">#{agemax},</if>
            <if test="gytjBs != null">#{gytjBs},</if>
            <if test="gytjBs1 != null">#{gytjBs1},</if>
            <if test="gmdm != null">#{gmdm},</if>
            <if test="dc1 != null">#{dc1},</if>
            <if test="dr1 != null">#{dr1},</if>
            <if test="mid != null">#{mid},</if>
            <if test="dx != null">#{dx},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRmsTSda" parameterType="RmsTSda">
        update rms_t_sda
        <trim prefix="SET" suffixOverrides=",">
            <if test="Ym != null">Ym = #{Ym},</if>
            <if test="Sp != null">Sp = #{Sp},</if>
            <if test="hypy != null">hypy = #{hypy},</if>
            <if test="UnitRem != null">UnitRem = #{UnitRem},</if>
            <if test="tymc != null">tymc = #{tymc},</if>
            <if test="yymc != null">yymc = #{yymc},</if>
            <if test="hxmc != null">hxmc = #{hxmc},</if>
            <if test="zycf != null">zycf = #{zycf},</if>
            <if test="fzs != null">fzs = #{fzs},</if>
            <if test="fzl != null">fzl = #{fzl},</if>
            <if test="xz != null">xz = #{xz},</if>
            <if test="yldl != null">yldl = #{yldl},</if>
            <if test="yddlx != null">yddlx = #{yddlx},</if>
            <if test="syz != null">syz = #{syz},</if>
            <if test="yfyl != null">yfyl = #{yfyl},</if>
            <if test="blfy != null">blfy = #{blfy},</if>
            <if test="jjz != null">jjz = #{jjz},</if>
            <if test="zysx != null">zysx = #{zysx},</if>
            <if test="yfyy != null">yfyy = #{yfyy},</if>
            <if test="etyy != null">etyy = #{etyy},</if>
            <if test="lnryy != null">lnryy = #{lnryy},</if>
            <if test="xhzy != null">xhzy = #{xhzy},</if>
            <if test="ywgl != null">ywgl = #{ywgl},</if>
            <if test="ywgg != null">ywgg = #{ywgg},</if>
            <if test="cctj != null">cctj = #{cctj},</if>
            <if test="ydts != null">ydts = #{ydts},</if>
            <if test="ylgzd != null">ylgzd = #{ylgzd},</if>
            <if test="fygzd != null">fygzd = #{fygzd},</if>
            <if test="ydgzd != null">ydgzd = #{ydgzd},</if>
            <if test="jxbs2 != null">jxbs_2 = #{jxbs2},</if>
            <if test="zxybs != null">zxybs = #{zxybs},</if>
            <if test="ly != null">ly = #{ly},</if>
            <if test="lyBs != null">ly_bs = #{lyBs},</if>
            <if test="scqy != null">scqy = #{scqy},</if>
            <if test="bs != null">bs = #{bs},</if>
            <if test="pzwh != null">pzwh = #{pzwh},</if>
            <if test="cpym != null">cpym = #{cpym},</if>
            <if test="cpsp != null">cpsp = #{cpsp},</if>
            <if test="lnrBs != null">lnr_bs = #{lnrBs},</if>
            <if test="yfBs != null">yf_bs = #{yfBs},</if>
            <if test="yfqsyBs != null">yfqsy_bs = #{yfqsyBs},</if>
            <if test="yfsyhBs != null">yfsyh_bs = #{yfsyhBs},</if>
            <if test="brBs != null">br_bs = #{brBs},</if>
            <if test="ggBs != null">gg_bs = #{ggBs},</if>
            <if test="sgBs != null">sg_bs = #{sgBs},</if>
            <if test="hggBs != null">hgg_bs = #{hggBs},</if>
            <if test="hsgBs != null">hsg_bs = #{hsgBs},</if>
            <if test="agemin != null">agemin = #{agemin},</if>
            <if test="agemax != null">agemax = #{agemax},</if>
            <if test="gytjBs != null">gytj_bs = #{gytjBs},</if>
            <if test="gytjBs1 != null">gytj_bs1 = #{gytjBs1},</if>
            <if test="gmdm != null">gmdm = #{gmdm},</if>
            <if test="dc1 != null">Dc_1 = #{dc1},</if>
            <if test="dr1 != null">Dr_1 = #{dr1},</if>
            <if test="mid != null">mid = #{mid},</if>
            <if test="dx != null">dx = #{dx},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where ID = #{ID}
    </update>

    <delete id="deleteRmsTSdaByID" parameterType="Long">
        delete from rms_t_sda where ID = #{ID}
    </delete>

    <delete id="deleteRmsTSdaByIDs" parameterType="String">
        delete from rms_t_sda where ID in 
        <foreach item="ID" collection="array" open="(" separator="," close=")">
            #{ID}
        </foreach>
    </delete>
</mapper>