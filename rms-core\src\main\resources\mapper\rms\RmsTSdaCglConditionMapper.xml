<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaCglConditionMapper">
    
    <resultMap type="RmsTSdaCglCondition" id="RmsTSdaCglConditionResult">
        <result property="id"    column="id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="ageMin"    column="age_min"    />
        <result property="ageMax"    column="age_max"    />
        <result property="gender"    column="gender"    />
        <result property="countType"    column="count_type"    />
        <result property="weightMin"    column="weight_min"    />
        <result property="weightMax"    column="weight_max"    />
        <result property="adminRoutine"    column="admin_routine"    />
    </resultMap>

    <sql id="selectRmsTSdaCglConditionVo">
        select id, sda_id, age_min, age_max, gender, count_type, weight_min, weight_max, admin_routine from rms_t_sda_cgl_condition
    </sql>

    <select id="selectRmsTSdaCglConditionList" parameterType="RmsTSdaCglCondition" resultMap="RmsTSdaCglConditionResult">
        <include refid="selectRmsTSdaCglConditionVo"/>
        <where>  
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="ageMin != null "> and age_min = #{ageMin}</if>
            <if test="ageMax != null "> and age_max = #{ageMax}</if>
            <if test="gender != null "> and gender = #{gender}</if>
            <if test="countType != null "> and count_type = #{countType}</if>
            <if test="weightMin != null "> and weight_min = #{weightMin}</if>
            <if test="weightMax != null "> and weight_max = #{weightMax}</if>
            <if test="adminRoutine != null  and adminRoutine != ''"> and admin_routine = #{adminRoutine}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaCglConditionById" parameterType="Long" resultMap="RmsTSdaCglConditionResult">
        <include refid="selectRmsTSdaCglConditionVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaCglCondition" parameterType="RmsTSdaCglCondition" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_sda_cgl_condition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">sda_id,</if>
            <if test="ageMin != null">age_min,</if>
            <if test="ageMax != null">age_max,</if>
            <if test="gender != null">gender,</if>
            <if test="countType != null">count_type,</if>
            <if test="weightMin != null">weight_min,</if>
            <if test="weightMax != null">weight_max,</if>
            <if test="adminRoutine != null">admin_routine,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">#{sdaId},</if>
            <if test="ageMin != null">#{ageMin},</if>
            <if test="ageMax != null">#{ageMax},</if>
            <if test="gender != null">#{gender},</if>
            <if test="countType != null">#{countType},</if>
            <if test="weightMin != null">#{weightMin},</if>
            <if test="weightMax != null">#{weightMax},</if>
            <if test="adminRoutine != null">#{adminRoutine},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaCglCondition" parameterType="RmsTSdaCglCondition">
        update rms_t_sda_cgl_condition
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="ageMin != null">age_min = #{ageMin},</if>
            <if test="ageMax != null">age_max = #{ageMax},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="countType != null">count_type = #{countType},</if>
            <if test="weightMin != null">weight_min = #{weightMin},</if>
            <if test="weightMax != null">weight_max = #{weightMax},</if>
            <if test="adminRoutine != null">admin_routine = #{adminRoutine},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaCglConditionById" parameterType="Long">
        delete from rms_t_sda_cgl_condition where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaCglConditionByIds" parameterType="String">
        delete from rms_t_sda_cgl_condition where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>