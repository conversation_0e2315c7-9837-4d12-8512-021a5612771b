package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSdaIcd10Info;
import com.rms.core.domain.DiagnosisRuleResult;

/**
 * 药物与诊断信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface RmsTSdaIcd10InfoMapper 
{
    /**
     * 查询药物与诊断信息
     * 
     * @param id 药物与诊断信息主键
     * @return 药物与诊断信息
     */
    public RmsTSdaIcd10Info selectRmsTSdaIcd10InfoById(Long id);

    /**
     * 查询药物与诊断信息列表
     * 
     * @param rmsTSdaIcd10Info 药物与诊断信息
     * @return 药物与诊断信息集合
     */
    public List<RmsTSdaIcd10Info> selectRmsTSdaIcd10InfoList(RmsTSdaIcd10Info rmsTSdaIcd10Info);

    /**
     * 新增药物与诊断信息
     * 
     * @param rmsTSdaIcd10Info 药物与诊断信息
     * @return 结果
     */
    public int insertRmsTSdaIcd10Info(RmsTSdaIcd10Info rmsTSdaIcd10Info);

    /**
     * 修改药物与诊断信息
     * 
     * @param rmsTSdaIcd10Info 药物与诊断信息
     * @return 结果
     */
    public int updateRmsTSdaIcd10Info(RmsTSdaIcd10Info rmsTSdaIcd10Info);

    /**
     * 删除药物与诊断信息
     * 
     * @param id 药物与诊断信息主键
     * @return 结果
     */
    public int deleteRmsTSdaIcd10InfoById(Long id);

    /**
     * 批量删除药物与诊断信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaIcd10InfoByIds(Long[] ids);

    /**
     * 根据sdaId查询药物与诊断信息（包含ICD信息）
     * 
     * @param sdaId 药品说明书ID
     * @return 药物与诊断信息集合
     */
    public List<DiagnosisRuleResult> selectDiagnosisRulesWithIcdBySdaId(Long sdaId);

    /**
     * 根据sdaId查询药物禁忌症信息（包含ICD信息）
     * 
     * @param sdaId 药品说明书ID
     * @return 药物禁忌症信息集合
     */
    public List<DiagnosisRuleResult> selectContraindicationRulesWithIcdBySdaId(Long sdaId);
}
