package com.rms.core.service;

import java.util.List;
import com.rms.core.domain.RmsTGmbase;

/**
 * 过敏基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface IRmsTGmbaseService 
{
    /**
     * 查询过敏基础信息
     * 
     * @param code 过敏基础信息主键
     * @return 过敏基础信息
     */
    public RmsTGmbase selectRmsTGmbaseByCode(String code);

    /**
     * 查询过敏基础信息列表
     * 
     * @param rmsTGmbase 过敏基础信息
     * @return 过敏基础信息集合
     */
    public List<RmsTGmbase> selectRmsTGmbaseList(RmsTGmbase rmsTGmbase);

    /**
     * 新增过敏基础信息
     * 
     * @param rmsTGmbase 过敏基础信息
     * @return 结果
     */
    public int insertRmsTGmbase(RmsTGmbase rmsTGmbase);

    /**
     * 修改过敏基础信息
     * 
     * @param rmsTGmbase 过敏基础信息
     * @return 结果
     */
    public int updateRmsTGmbase(RmsTGmbase rmsTGmbase);

    /**
     * 批量删除过敏基础信息
     * 
     * @param codes 需要删除的过敏基础信息主键集合
     * @return 结果
     */
    public int deleteRmsTGmbaseByCodes(String[] codes);

    /**
     * 删除过敏基础信息信息
     * 
     * @param code 过敏基础信息主键
     * @return 结果
     */
    public int deleteRmsTGmbaseByCode(String code);
}
