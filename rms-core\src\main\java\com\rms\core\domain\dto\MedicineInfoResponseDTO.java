package com.rms.core.domain.dto;

/**
 * 药品信息查询响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class MedicineInfoResponseDTO {

    /**
     * 药品信息
     */
    private MedicineInfoDataDTO info;

    public MedicineInfoDataDTO getInfo() {
        return info;
    }

    public void setInfo(MedicineInfoDataDTO info) {
        this.info = info;
    }

    @Override
    public String toString() {
        return "MedicineInfoResponseDTO{" +
                "info=" + info +
                '}';
    }

    /**
     * 药品信息数据DTO
     */
    public static class MedicineInfoDataDTO {
        /**
         * 药名
         */
        private String ym;

        /**
         * 拼音缩写
         */
        private String sp;

        public String getYm() {
            return ym;
        }

        public void setYm(String ym) {
            this.ym = ym;
        }

        public String getSp() {
            return sp;
        }

        public void setSp(String sp) {
            this.sp = sp;
        }

        @Override
        public String toString() {
            return "MedicineInfoDataDTO{" +
                    "ym='" + ym + '\'' +
                    ", sp='" + sp + '\'' +
                    '}';
        }
    }
}
