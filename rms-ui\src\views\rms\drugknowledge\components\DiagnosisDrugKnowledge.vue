<template>
  <div class="diagnosis-drug-knowledge">
    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button
        type="primary"
        plain
        icon="el-icon-plus"
        size="mini"
        @click="handleAdd"
      >添加诊断</el-button>
      <el-button
        type="danger"
        plain
        icon="el-icon-delete"
        size="mini"
        :disabled="selectedRules.length === 0"
        @click="handleBatchDelete"
      >批量删除</el-button>
    </div>

    <!-- 诊断信息表格 -->
    <el-table
      v-loading="loading"
      :data="diagnosisList"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="诊断编码" prop="diagnoses" width="140" align="center" />
      <el-table-column label="ICD编码" prop="icdCode" width="120" align="center" />
      <el-table-column label="ICD诊断名称" prop="icdName" show-overflow-tooltip />
      <el-table-column label="标识" prop="bs" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.bs === 5 ? 'success' : 'info'"
            size="mini"
          >
            {{ scope.row.bs === 5 ? '适应症' : '其他' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否屏蔽" prop="ispb" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.ispb === '1' ? 'danger' : 'success'"
            size="mini"
          >
            {{ scope.row.ispb === '1' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
      <template slot="empty">
        <div class="empty-content">
          <i class="el-icon-document" style="font-size: 48px; color: #ddd;"></i>
          <p style="color: #999; margin-top: 20px;">暂无关联的诊断信息</p>
        </div>
      </template>
    </el-table>

    <!-- 添加诊断对话框 -->
    <el-dialog
      title="添加诊断"
      :visible.sync="dialogVisible"
      width="900px"
      @close="handleDialogClose"
    >
      <div class="dialog-content">
        <!-- 搜索区域 -->
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入ICD编码、诊断名称或拼音缩写进行搜索"
            clearable
            @input="handleSearch"
            style="width: 400px;"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </el-input>
        </div>

        <!-- 诊断列表 -->
        <div class="diagnosis-list">
          <el-table
            v-loading="searchLoading"
            :data="availableDiagnosis"
            border
            height="400"
            @selection-change="handleDiagnosisSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="诊断编码" prop="code" width="140" align="center" />
            <el-table-column label="ICD编码" prop="icdCode" width="120" align="center" />
            <el-table-column label="ICD诊断名称" prop="icdName" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirmAdd" :disabled="selectedDiagnosis.length === 0">
          确定添加（{{ selectedDiagnosis.length }}）
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDiagnosisRules, addDiagnosisRule, deleteDiagnosisRule, deleteDiagnosisRules, searchDiagnosis } from "@/api/rms/drugknowledge"

export default {
  name: "DiagnosisDrugKnowledge",
  props: {
    drug: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 诊断信息列表
      diagnosisList: [],
      // 选中的诊断规则
      selectedRules: [],
      // 对话框显示状态
      dialogVisible: false,
      // 搜索关键字
      searchKeyword: '',
      // 搜索加载状态
      searchLoading: false,
      // 可用诊断列表
      availableDiagnosis: [],
      // 选中的诊断
      selectedDiagnosis: []
    }
  },
  watch: {
    drug: {
      handler(newVal) {
        if (newVal && newVal.sdaId) {
          this.loadDiagnosisRules()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载诊断规则列表
    loadDiagnosisRules() {
      if (!this.drug || !this.drug.sdaId) return
      
      this.loading = true
      getDiagnosisRules(this.drug.sdaId).then(response => {
        this.diagnosisList = response.data || []
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedRules = selection
    },

    // 添加诊断
    handleAdd() {
      this.dialogVisible = true
      this.searchKeyword = ''
      this.availableDiagnosis = []
      this.selectedDiagnosis = []
    },

    // 搜索诊断
    handleSearch() {
      if (!this.searchKeyword.trim() || this.searchKeyword.length < 2) {
        this.availableDiagnosis = []
        return
      }
      
      this.searchLoading = true
      searchDiagnosis(this.searchKeyword).then(response => {
        this.availableDiagnosis = response.data || []
        this.searchLoading = false
      }).catch(() => {
        this.searchLoading = false
      })
    },

    // 诊断选择变化
    handleDiagnosisSelectionChange(selection) {
      this.selectedDiagnosis = selection
    },

    // 确认添加
    handleConfirmAdd() {
      if (this.selectedDiagnosis.length === 0) {
        this.$modal.msgWarning("请选择要添加的诊断")
        return
      }

      const promises = this.selectedDiagnosis.map(diagnosis => {
        const data = {
          sdaId: this.drug.sdaId,
          diagnoses: diagnosis.code,
          bs: 5, // 标识为药物适应症
          ispb: '0' // 默认不屏蔽
        }
        return addDiagnosisRule(data)
      })

      Promise.all(promises).then(() => {
        this.$modal.msgSuccess(`成功添加 ${this.selectedDiagnosis.length} 个诊断`)
        this.dialogVisible = false
        this.loadDiagnosisRules()
      }).catch(() => {
        this.$modal.msgError("添加失败")
      })
    },

    // 删除诊断
    handleDelete(row) {
      const that = this
      this.$modal.confirm('是否确认删除该诊断信息？').then(function() {
        return deleteDiagnosisRule(row.id)
      }).then(() => {
        that.loadDiagnosisRules()
        that.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },

    // 批量删除
    handleBatchDelete() {
      const that = this
      const ids = this.selectedRules.map(item => item.id)
      this.$modal.confirm('是否确认删除选中的诊断信息？').then(function() {
        return deleteDiagnosisRules(ids)
      }).then(() => {
        that.loadDiagnosisRules()
        that.$modal.msgSuccess("删除成功")
        that.selectedRules = []
      }).catch(() => {})
    },

    // 关闭对话框
    handleDialogClose() {
      this.dialogVisible = false
      this.searchKeyword = ''
      this.availableDiagnosis = []
      this.selectedDiagnosis = []
    }
  }
}
</script>

<style scoped>
.diagnosis-drug-knowledge {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.empty-content {
  text-align: center;
  padding: 60px 0;
}

.dialog-content {
  padding: 10px 0;
}

.search-section {
  margin-bottom: 20px;
}

.diagnosis-list {
  margin-top: 15px;
}

.dialog-footer {
  text-align: right;
}
</style> 