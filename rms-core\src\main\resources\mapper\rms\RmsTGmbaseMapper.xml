<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTGmbaseMapper">
    
    <resultMap type="RmsTGmbase" id="RmsTGmbaseResult">
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="sp"    column="sp"    />
    </resultMap>

    <sql id="selectRmsTGmbaseVo">
        select code, name, sp from rms_t_gmbase
    </sql>

    <select id="selectRmsTGmbaseList" parameterType="RmsTGmbase" resultMap="RmsTGmbaseResult">
        <include refid="selectRmsTGmbaseVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sp != null  and sp != ''"> and sp = #{sp}</if>
        </where>
    </select>
    
    <select id="selectRmsTGmbaseByCode" parameterType="String" resultMap="RmsTGmbaseResult">
        <include refid="selectRmsTGmbaseVo"/>
        where code = #{code}
    </select>

    <insert id="insertRmsTGmbase" parameterType="RmsTGmbase">
        insert into rms_t_gmbase
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="sp != null">sp,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="sp != null">#{sp},</if>
         </trim>
    </insert>

    <update id="updateRmsTGmbase" parameterType="RmsTGmbase">
        update rms_t_gmbase
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="sp != null">sp = #{sp},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteRmsTGmbaseByCode" parameterType="String">
        delete from rms_t_gmbase where code = #{code}
    </delete>

    <delete id="deleteRmsTGmbaseByCodes" parameterType="String">
        delete from rms_t_gmbase where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>

    <!-- 根据关键字搜索过敏基础数据 -->
    <select id="searchAllergyBaseByKeyword" parameterType="String" resultMap="RmsTGmbaseResult">
        <include refid="selectRmsTGmbaseVo"/>
        <where>
            <if test="_parameter != null and _parameter != ''">
                AND (
                    name LIKE CONCAT('%', #{_parameter}, '%')
                    OR sp LIKE CONCAT('%', #{_parameter}, '%')
                )
            </if>
        </where>
        ORDER BY name
    </select>
</mapper>