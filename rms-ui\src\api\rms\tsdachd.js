import request from '@/utils/request'

// 查询儿童用药规则库列表
export function listTsdachd(query) {
  return request({
    url: '/rms/tsdachd/list',
    method: 'get',
    params: query
  })
}

// 查询儿童用药规则库详细
export function getTsdachd(id) {
  return request({
    url: '/rms/tsdachd/' + id,
    method: 'get'
  })
}

// 新增儿童用药规则库
export function addTsdachd(data) {
  return request({
    url: '/rms/tsdachd',
    method: 'post',
    data: data
  })
}

// 修改儿童用药规则库
export function updateTsdachd(data) {
  return request({
    url: '/rms/tsdachd',
    method: 'put',
    data: data
  })
}

// 删除儿童用药规则库
export function delTsdachd(id) {
  return request({
    url: '/rms/tsdachd/' + id,
    method: 'delete'
  })
}
