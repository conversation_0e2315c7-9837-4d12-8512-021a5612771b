package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSdaAge;

/**
 * 年龄代码Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface RmsTSdaAgeMapper 
{
    /**
     * 查询年龄代码
     * 
     * @param id 年龄代码主键
     * @return 年龄代码
     */
    public RmsTSdaAge selectRmsTSdaAgeById(Long id);

    /**
     * 查询年龄代码列表
     * 
     * @param rmsTSdaAge 年龄代码
     * @return 年龄代码集合
     */
    public List<RmsTSdaAge> selectRmsTSdaAgeList(RmsTSdaAge rmsTSdaAge);

    /**
     * 新增年龄代码
     * 
     * @param rmsTSdaAge 年龄代码
     * @return 结果
     */
    public int insertRmsTSdaAge(RmsTSdaAge rmsTSdaAge);

    /**
     * 修改年龄代码
     * 
     * @param rmsTSdaAge 年龄代码
     * @return 结果
     */
    public int updateRmsTSdaAge(RmsTSdaAge rmsTSdaAge);

    /**
     * 删除年龄代码
     * 
     * @param id 年龄代码主键
     * @return 结果
     */
    public int deleteRmsTSdaAgeById(Long id);

    /**
     * 批量删除年龄代码
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaAgeByIds(Long[] ids);
}
