package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaGytjMapper;
import com.rms.core.domain.RmsTSdaGytj;
import com.rms.core.service.IRmsTSdaGytjService;

/**
 * 标准药品给药途径表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class RmsTSdaGytjServiceImpl implements IRmsTSdaGytjService
{
    @Autowired
    private RmsTSdaGytjMapper rmsTSdaGytjMapper;

    /**
     * 查询标准药品给药途径表
     *
     * @param id 标准药品给药途径表主键
     * @return 标准药品给药途径表
     */
    @Override
    public RmsTSdaGytj selectRmsTSdaGytjById(Long id)
    {
        return rmsTSdaGytjMapper.selectRmsTSdaGytjById(id);
    }

    /**
     * 查询标准药品给药途径表列表
     *
     * @param rmsTSdaGytj 标准药品给药途径表
     * @return 标准药品给药途径表
     */
    @Override
    public List<RmsTSdaGytj> selectRmsTSdaGytjList(RmsTSdaGytj rmsTSdaGytj)
    {
        return rmsTSdaGytjMapper.selectRmsTSdaGytjList(rmsTSdaGytj);
    }

    /**
     * 新增标准药品给药途径表
     *
     * @param rmsTSdaGytj 标准药品给药途径表
     * @return 结果
     */
    @Override
    public int insertRmsTSdaGytj(RmsTSdaGytj rmsTSdaGytj)
    {
        return rmsTSdaGytjMapper.insertRmsTSdaGytj(rmsTSdaGytj);
    }

    /**
     * 修改标准药品给药途径表
     *
     * @param rmsTSdaGytj 标准药品给药途径表
     * @return 结果
     */
    @Override
    public int updateRmsTSdaGytj(RmsTSdaGytj rmsTSdaGytj)
    {
        return rmsTSdaGytjMapper.updateRmsTSdaGytj(rmsTSdaGytj);
    }

    /**
     * 批量删除标准药品给药途径表
     *
     * @param ids 需要删除的标准药品给药途径表主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaGytjByIds(Long[] ids)
    {
        return rmsTSdaGytjMapper.deleteRmsTSdaGytjByIds(ids);
    }

    /**
     * 删除标准药品给药途径表信息
     *
     * @param id 标准药品给药途径表主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaGytjById(Long id)
    {
        return rmsTSdaGytjMapper.deleteRmsTSdaGytjById(id);
    }
}
