package com.rms.core.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 儿童用药规则库对象 rms_t_sda_chd
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public class RmsTSdaChd extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 药品说明书ID */
    @Excel(name = "药品说明书ID")
    private Long sdaId;

    /** 最小使用年龄代码，从rms_t_sda_age获取 */
    @Excel(name = "最小使用年龄代码，从rms_t_sda_age获取")
    private Long chdJdMin;

    /** 最大使用年龄代码，从rms_t_sda_age获取 */
    @Excel(name = "最大使用年龄代码，从rms_t_sda_age获取")
    private Long chdJdMax;

    /** 用药规则：0-禁用；1-慎用；2-提示；9-忽略/停用 */
    @Excel(name = "用药规则：0-禁用；1-慎用；2-提示；9-忽略/停用")
    private String result;

    /** 标识（未用） */
    @Excel(name = "标识", readConverterExp = "未=用")
    private String chBs;

    /** 最小使用年龄（未用） */
    @Excel(name = "最小使用年龄", readConverterExp = "未=用")
    private BigDecimal ageMin;

    /** 最大使用年龄（未用） */
    @Excel(name = "最大使用年龄", readConverterExp = "未=用")
    private BigDecimal ageMax;

    /** 是否屏蔽 */
    @Excel(name = "是否屏蔽")
    private String ispb;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setChdJdMin(Long chdJdMin)
    {
        this.chdJdMin = chdJdMin;
    }

    public Long getChdJdMin()
    {
        return chdJdMin;
    }

    public void setChdJdMax(Long chdJdMax)
    {
        this.chdJdMax = chdJdMax;
    }

    public Long getChdJdMax()
    {
        return chdJdMax;
    }

    public void setResult(String result)
    {
        this.result = result;
    }

    public String getResult()
    {
        return result;
    }

    public void setChBs(String chBs)
    {
        this.chBs = chBs;
    }

    public String getChBs()
    {
        return chBs;
    }

    public void setAgeMin(BigDecimal ageMin)
    {
        this.ageMin = ageMin;
    }

    public BigDecimal getAgeMin()
    {
        return ageMin;
    }

    public void setAgeMax(BigDecimal ageMax)
    {
        this.ageMax = ageMax;
    }

    public BigDecimal getAgeMax()
    {
        return ageMax;
    }

    public void setIspb(String ispb)
    {
        this.ispb = ispb;
    }

    public String getIspb()
    {
        return ispb;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sdaId", getSdaId())
            .append("chdJdMin", getChdJdMin())
            .append("chdJdMax", getChdJdMax())
            .append("result", getResult())
            .append("chBs", getChBs())
            .append("ageMin", getAgeMin())
            .append("ageMax", getAgeMax())
            .append("ispb", getIspb())
            .toString();
    }
}
