import request from '@/utils/request'

// 查询孕期用药规则库列表
export function listTsdagestation(query) {
  return request({
    url: '/rms/tsdagestation/list',
    method: 'get',
    params: query
  })
}

// 查询孕期用药规则库详细
export function getTsdagestation(id) {
  return request({
    url: '/rms/tsdagestation/' + id,
    method: 'get'
  })
}

// 新增孕期用药规则库
export function addTsdagestation(data) {
  return request({
    url: '/rms/tsdagestation',
    method: 'post',
    data: data
  })
}

// 修改孕期用药规则库
export function updateTsdagestation(data) {
  return request({
    url: '/rms/tsdagestation',
    method: 'put',
    data: data
  })
}

// 删除孕期用药规则库
export function delTsdagestation(id) {
  return request({
    url: '/rms/tsdagestation/' + id,
    method: 'delete'
  })
}
