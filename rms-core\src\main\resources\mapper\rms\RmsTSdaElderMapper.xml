<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaElderMapper">
    
    <resultMap type="RmsTSdaElder" id="RmsTSdaElderResult">
        <result property="id"    column="id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="ageMin"    column="age_min"    />
        <result property="ageMax"    column="age_max"    />
        <result property="jsbs"    column="jsbs"    />
        <result property="remark"    column="remark"    />
        <result property="ispb"    column="ispb"    />
    </resultMap>

    <sql id="selectRmsTSdaElderVo">
        select id, sda_id, age_min, age_max, jsbs, remark, ispb from rms_t_sda_elder
    </sql>

    <select id="selectRmsTSdaElderList" parameterType="RmsTSdaElder" resultMap="RmsTSdaElderResult">
        <include refid="selectRmsTSdaElderVo"/>
        <where>  
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="ageMin != null "> and age_min = #{ageMin}</if>
            <if test="ageMax != null "> and age_max = #{ageMax}</if>
            <if test="jsbs != null "> and jsbs = #{jsbs}</if>
            <if test="ispb != null  and ispb != ''"> and ispb = #{ispb}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaElderById" parameterType="Long" resultMap="RmsTSdaElderResult">
        <include refid="selectRmsTSdaElderVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaElder" parameterType="RmsTSdaElder" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_sda_elder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">sda_id,</if>
            <if test="ageMin != null">age_min,</if>
            <if test="ageMax != null">age_max,</if>
            <if test="jsbs != null">jsbs,</if>
            <if test="remark != null">remark,</if>
            <if test="ispb != null">ispb,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">#{sdaId},</if>
            <if test="ageMin != null">#{ageMin},</if>
            <if test="ageMax != null">#{ageMax},</if>
            <if test="jsbs != null">#{jsbs},</if>
            <if test="remark != null">#{remark},</if>
            <if test="ispb != null">#{ispb},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaElder" parameterType="RmsTSdaElder">
        update rms_t_sda_elder
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="ageMin != null">age_min = #{ageMin},</if>
            <if test="ageMax != null">age_max = #{ageMax},</if>
            <if test="jsbs != null">jsbs = #{jsbs},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="ispb != null">ispb = #{ispb},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaElderById" parameterType="Long">
        delete from rms_t_sda_elder where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaElderByIds" parameterType="String">
        delete from rms_t_sda_elder where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>