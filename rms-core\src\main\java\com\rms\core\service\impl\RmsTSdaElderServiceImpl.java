package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaElderMapper;
import com.rms.core.domain.RmsTSdaElder;
import com.rms.core.service.IRmsTSdaElderService;

/**
 * 老年人用药规则库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class RmsTSdaElderServiceImpl implements IRmsTSdaElderService
{
    @Autowired
    private RmsTSdaElderMapper rmsTSdaElderMapper;

    /**
     * 查询老年人用药规则库
     *
     * @param id 老年人用药规则库主键
     * @return 老年人用药规则库
     */
    @Override
    public RmsTSdaElder selectRmsTSdaElderById(Long id)
    {
        return rmsTSdaElderMapper.selectRmsTSdaElderById(id);
    }

    /**
     * 查询老年人用药规则库列表
     *
     * @param rmsTSdaElder 老年人用药规则库
     * @return 老年人用药规则库
     */
    @Override
    public List<RmsTSdaElder> selectRmsTSdaElderList(RmsTSdaElder rmsTSdaElder)
    {
        return rmsTSdaElderMapper.selectRmsTSdaElderList(rmsTSdaElder);
    }

    /**
     * 新增老年人用药规则库
     *
     * @param rmsTSdaElder 老年人用药规则库
     * @return 结果
     */
    @Override
    public int insertRmsTSdaElder(RmsTSdaElder rmsTSdaElder)
    {
        return rmsTSdaElderMapper.insertRmsTSdaElder(rmsTSdaElder);
    }

    /**
     * 修改老年人用药规则库
     *
     * @param rmsTSdaElder 老年人用药规则库
     * @return 结果
     */
    @Override
    public int updateRmsTSdaElder(RmsTSdaElder rmsTSdaElder)
    {
        return rmsTSdaElderMapper.updateRmsTSdaElder(rmsTSdaElder);
    }

    /**
     * 批量删除老年人用药规则库
     *
     * @param ids 需要删除的老年人用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaElderByIds(Long[] ids)
    {
        return rmsTSdaElderMapper.deleteRmsTSdaElderByIds(ids);
    }

    /**
     * 删除老年人用药规则库信息
     *
     * @param id 老年人用药规则库主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaElderById(Long id)
    {
        return rmsTSdaElderMapper.deleteRmsTSdaElderById(id);
    }
}
