package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 药品常规用量规则对象 rms_t_sda_cgl_result
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public class RmsTSdaCglResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 条件ID */
    @Excel(name = "条件ID")
    private Long conditionId;

    /** 标准药品ID */
    @Excel(name = "标准药品ID")
    private Long sdaId;

    /** 用法类型：0-单次用量；1-单日用量；2-频次； */
    @Excel(name = "用法类型：0-单次用量；1-单日用量；2-频次；")
    private Long recoType;

    /** 用量最小值 */
    @Excel(name = "用量最小值")
    private Long ylMin;

    /** 用量最大值 */
    @Excel(name = "用量最大值")
    private Long ylMax;

    /** 用量单位 */
    @Excel(name = "用量单位")
    private String ylUnit;

    public void setConditionId(Long conditionId)
    {
        this.conditionId = conditionId;
    }

    public Long getConditionId()
    {
        return conditionId;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setRecoType(Long recoType)
    {
        this.recoType = recoType;
    }

    public Long getRecoType()
    {
        return recoType;
    }

    public void setYlMin(Long ylMin)
    {
        this.ylMin = ylMin;
    }

    public Long getYlMin()
    {
        return ylMin;
    }

    public void setYlMax(Long ylMax)
    {
        this.ylMax = ylMax;
    }

    public Long getYlMax()
    {
        return ylMax;
    }

    public void setYlUnit(String ylUnit)
    {
        this.ylUnit = ylUnit;
    }

    public String getYlUnit()
    {
        return ylUnit;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("conditionId", getConditionId())
            .append("sdaId", getSdaId())
            .append("recoType", getRecoType())
            .append("ylMin", getYlMin())
            .append("ylMax", getYlMax())
            .append("ylUnit", getYlUnit())
            .toString();
    }
}
