<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsTSdaGytjMapper">
    
    <resultMap type="RmsTSdaGytj" id="RmsTSdaGytjResult">
        <result property="id"    column="id"    />
        <result property="sdaId"    column="sda_id"    />
        <result property="gytjCode"    column="gytj_code"    />
        <result property="bs"    column="bs"    />
        <result property="ispb"    column="ispb"    />
    </resultMap>

    <sql id="selectRmsTSdaGytjVo">
        select id, sda_id, gytj_code, bs, ispb from rms_t_sda_gytj
    </sql>

    <select id="selectRmsTSdaGytjList" parameterType="RmsTSdaGytj" resultMap="RmsTSdaGytjResult">
        <include refid="selectRmsTSdaGytjVo"/>
        <where>  
            <if test="sdaId != null "> and sda_id = #{sdaId}</if>
            <if test="gytjCode != null  and gytjCode != ''"> and gytj_code = #{gytjCode}</if>
            <if test="bs != null  and bs != ''"> and bs = #{bs}</if>
            <if test="ispb != null  and ispb != ''"> and ispb = #{ispb}</if>
        </where>
    </select>
    
    <select id="selectRmsTSdaGytjById" parameterType="Long" resultMap="RmsTSdaGytjResult">
        <include refid="selectRmsTSdaGytjVo"/>
        where id = #{id}
    </select>

    <insert id="insertRmsTSdaGytj" parameterType="RmsTSdaGytj" useGeneratedKeys="true" keyProperty="id">
        insert into rms_t_sda_gytj
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">sda_id,</if>
            <if test="gytjCode != null">gytj_code,</if>
            <if test="bs != null">bs,</if>
            <if test="ispb != null">ispb,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sdaId != null">#{sdaId},</if>
            <if test="gytjCode != null">#{gytjCode},</if>
            <if test="bs != null">#{bs},</if>
            <if test="ispb != null">#{ispb},</if>
         </trim>
    </insert>

    <update id="updateRmsTSdaGytj" parameterType="RmsTSdaGytj">
        update rms_t_sda_gytj
        <trim prefix="SET" suffixOverrides=",">
            <if test="sdaId != null">sda_id = #{sdaId},</if>
            <if test="gytjCode != null">gytj_code = #{gytjCode},</if>
            <if test="bs != null">bs = #{bs},</if>
            <if test="ispb != null">ispb = #{ispb},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRmsTSdaGytjById" parameterType="Long">
        delete from rms_t_sda_gytj where id = #{id}
    </delete>

    <delete id="deleteRmsTSdaGytjByIds" parameterType="String">
        delete from rms_t_sda_gytj where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>