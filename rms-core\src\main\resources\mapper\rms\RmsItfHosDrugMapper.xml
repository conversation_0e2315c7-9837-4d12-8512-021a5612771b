<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsItfHosDrugMapper">
    
    <resultMap type="RmsItfHosDrug" id="RmsItfHosDrugResult">
        <result property="drugId"    column="drug_id"    />
        <result property="drugCode"    column="drug_code"    />
        <result property="drugSpec"    column="drug_spec"    />
        <result property="packingSpec"    column="packing_spec"    />
        <result property="packingUom"    column="packing_uom"    />
        <result property="packingMinSpec"    column="packing_min_spec"    />
        <result property="packingMinQty"    column="packing_min_qty"    />
        <result property="drugQty"    column="drug_qty"    />
        <result property="drugUom"    column="drug_uom"    />
        <result property="drugManuf"    column="drug_manuf"    />
        <result property="drugProductName"    column="drug_product_name"    />
        <result property="drugName"    column="drug_name"    />
        <result property="drugSp"    column="drug_sp"    />
        <result property="drugGeneralName"    column="drug_general_name"    />
        <result property="drugForName"    column="drug_for_name"    />
        <result property="isAntibac"    column="is_antibac"    />
        <result property="antitumorType"    column="antitumor_type"    />
        <result property="zxFlag"    column="zx_flag"    />
        <result property="isBasicDrug"    column="is_basic_drug"    />
        <result property="isInjection"    column="is_injection"    />
        <result property="lawType"    column="law_type"    />
        <result property="drugType"    column="drug_type"    />
        <result property="antibacType"    column="antibac_type"    />
        <result property="isPurchaseInternet"    column="is_purchase_internet"    />
        <result property="stopFlag"    column="stop_flag"    />
        <result property="stopDate"    column="stop_date"    />
        <result property="medicareCode"    column="medicare_code"    />
        <result property="medicareType"    column="medicare_type"    />
        <result property="medicareRemark"    column="medicare_remark"    />
        <result property="approvalCertif"    column="approval_certif"    />
        <result property="hospCode"    column="hosp_code"    />
        <result property="impDate"    column="imp_date"    />
        <result property="isOpIp"    column="is_op_ip"    />
        <result property="isRm"    column="is_rm"    />
        <result property="isDjm"    column="is_djm"    />
        <result property="dj"    column="dj"    />
        <result property="je"    column="je"    />
        <result property="cancerDrug"    column="cancer_drug"    />
        <result property="coldDrug"    column="cold_drug"    />
        <result property="protonPumpDrug"    column="proton_pump_drug"    />
        <result property="glucocorticoid"    column="glucocorticoid"    />
        <result property="keyDrug"    column="key_drug"    />
        <result property="bloodDrug"    column="blood_drug"    />
        <result property="spiritAnaesthesiaDrug"    column="spirit_anaesthesia_drug"    />
        <result property="isZdjk"    column="is_zdjk"    />
        <result property="expensiveDrug"    column="expensive_drug"    />
        <result property="isTsyp"    column="is_tsyp"    />
        <result property="gt20"    column="gt_20"    />
        <result property="gt21"    column="gt_21"    />
        <result property="isJc"    column="is_jc"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRmsItfHosDrugVo">
        select drug_id, drug_code, drug_spec, packing_spec, packing_uom, packing_min_spec, packing_min_qty, drug_qty, drug_uom, drug_manuf, drug_product_name, drug_name, drug_sp, drug_general_name, drug_for_name, is_antibac, antitumor_type, zx_flag, is_basic_drug, is_injection, law_type, drug_type, antibac_type, is_purchase_internet, stop_flag, stop_date, medicare_code, medicare_type, medicare_remark, approval_certif, hosp_code, imp_date, is_op_ip, is_rm, is_djm, dj, je, cancer_drug, cold_drug, proton_pump_drug, glucocorticoid, key_drug, blood_drug, spirit_anaesthesia_drug, is_zdjk, expensive_drug, is_tsyp, gt_20, gt_21, is_jc, create_time, update_time from rms_itf_hos_drug
    </sql>

    <select id="selectRmsItfHosDrugList" parameterType="RmsItfHosDrug" resultMap="RmsItfHosDrugResult">
        <include refid="selectRmsItfHosDrugVo"/>
        <where>  
            <if test="drugId != null "> and drug_id = #{drugId}</if>
            <if test="drugSpec != null  and drugSpec != ''"> and drug_spec = #{drugSpec}</if>
            <if test="packingSpec != null  and packingSpec != ''"> and packing_spec = #{packingSpec}</if>
            <if test="packingUom != null  and packingUom != ''"> and packing_uom = #{packingUom}</if>
            <if test="packingMinSpec != null  and packingMinSpec != ''"> and packing_min_spec = #{packingMinSpec}</if>
            <if test="packingMinQty != null  and packingMinQty != ''"> and packing_min_qty = #{packingMinQty}</if>
            <if test="drugQty != null  and drugQty != ''"> and drug_qty = #{drugQty}</if>
            <if test="drugUom != null  and drugUom != ''"> and drug_uom = #{drugUom}</if>
            <if test="drugManuf != null  and drugManuf != ''"> and drug_manuf = #{drugManuf}</if>
            <if test="drugProductName != null  and drugProductName != ''"> and drug_product_name like concat('%', #{drugProductName}, '%')</if>
            <if test="drugName != null  and drugName != ''"> and drug_name like concat('%', #{drugName}, '%')</if>
            <if test="drugSp != null  and drugSp != ''"> and drug_sp = #{drugSp}</if>
            <if test="drugGeneralName != null  and drugGeneralName != ''"> and drug_general_name like concat('%', #{drugGeneralName}, '%')</if>
            <if test="drugForName != null  and drugForName != ''"> and drug_for_name like concat('%', #{drugForName}, '%')</if>
            <if test="isAntibac != null  and isAntibac != ''"> and is_antibac = #{isAntibac}</if>
            <if test="antitumorType != null  and antitumorType != ''"> and antitumor_type = #{antitumorType}</if>
            <if test="zxFlag != null  and zxFlag != ''"> and zx_flag = #{zxFlag}</if>
            <if test="isBasicDrug != null  and isBasicDrug != ''"> and is_basic_drug = #{isBasicDrug}</if>
            <if test="isInjection != null  and isInjection != ''"> and is_injection = #{isInjection}</if>
            <if test="lawType != null  and lawType != ''"> and law_type = #{lawType}</if>
            <if test="drugType != null  and drugType != ''"> and drug_type = #{drugType}</if>
            <if test="antibacType != null  and antibacType != ''"> and antibac_type = #{antibacType}</if>
            <if test="isPurchaseInternet != null  and isPurchaseInternet != ''"> and is_purchase_internet = #{isPurchaseInternet}</if>
            <if test="stopFlag != null  and stopFlag != ''"> and stop_flag = #{stopFlag}</if>
            <if test="stopDate != null  and stopDate != ''"> and stop_date = #{stopDate}</if>
            <if test="medicareCode != null  and medicareCode != ''"> and medicare_code = #{medicareCode}</if>
            <if test="medicareType != null  and medicareType != ''"> and medicare_type = #{medicareType}</if>
            <if test="medicareRemark != null  and medicareRemark != ''"> and medicare_remark = #{medicareRemark}</if>
            <if test="approvalCertif != null  and approvalCertif != ''"> and approval_certif = #{approvalCertif}</if>
            <if test="hospCode != null  and hospCode != ''"> and hosp_code = #{hospCode}</if>
            <if test="impDate != null  and impDate != ''"> and imp_date = #{impDate}</if>
            <if test="isOpIp != null  and isOpIp != ''"> and is_op_ip = #{isOpIp}</if>
            <if test="isRm != null  and isRm != ''"> and is_rm = #{isRm}</if>
            <if test="isDjm != null  and isDjm != ''"> and is_djm = #{isDjm}</if>
            <if test="dj != null "> and dj = #{dj}</if>
            <if test="je != null "> and je = #{je}</if>
            <if test="cancerDrug != null  and cancerDrug != ''"> and cancer_drug = #{cancerDrug}</if>
            <if test="coldDrug != null  and coldDrug != ''"> and cold_drug = #{coldDrug}</if>
            <if test="protonPumpDrug != null  and protonPumpDrug != ''"> and proton_pump_drug = #{protonPumpDrug}</if>
            <if test="glucocorticoid != null  and glucocorticoid != ''"> and glucocorticoid = #{glucocorticoid}</if>
            <if test="keyDrug != null  and keyDrug != ''"> and key_drug = #{keyDrug}</if>
            <if test="bloodDrug != null  and bloodDrug != ''"> and blood_drug = #{bloodDrug}</if>
            <if test="spiritAnaesthesiaDrug != null  and spiritAnaesthesiaDrug != ''"> and spirit_anaesthesia_drug = #{spiritAnaesthesiaDrug}</if>
            <if test="isZdjk != null  and isZdjk != ''"> and is_zdjk = #{isZdjk}</if>
            <if test="expensiveDrug != null  and expensiveDrug != ''"> and expensive_drug = #{expensiveDrug}</if>
            <if test="isTsyp != null  and isTsyp != ''"> and is_tsyp = #{isTsyp}</if>
            <if test="gt20 != null  and gt20 != ''"> and gt_20 = #{gt20}</if>
            <if test="gt21 != null  and gt21 != ''"> and gt_21 = #{gt21}</if>
            <if test="isJc != null  and isJc != ''"> and is_jc = #{isJc}</if>
        </where>
    </select>
    
    <select id="selectRmsItfHosDrugByDrugCode" parameterType="String" resultMap="RmsItfHosDrugResult">
        <include refid="selectRmsItfHosDrugVo"/>
        where drug_code = #{drugCode}
    </select>

    <insert id="insertRmsItfHosDrug" parameterType="RmsItfHosDrug">
        insert into rms_itf_hos_drug
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="drugId != null">drug_id,</if>
            <if test="drugCode != null">drug_code,</if>
            <if test="drugSpec != null">drug_spec,</if>
            <if test="packingSpec != null">packing_spec,</if>
            <if test="packingUom != null">packing_uom,</if>
            <if test="packingMinSpec != null">packing_min_spec,</if>
            <if test="packingMinQty != null">packing_min_qty,</if>
            <if test="drugQty != null">drug_qty,</if>
            <if test="drugUom != null">drug_uom,</if>
            <if test="drugManuf != null">drug_manuf,</if>
            <if test="drugProductName != null">drug_product_name,</if>
            <if test="drugName != null">drug_name,</if>
            <if test="drugSp != null">drug_sp,</if>
            <if test="drugGeneralName != null">drug_general_name,</if>
            <if test="drugForName != null">drug_for_name,</if>
            <if test="isAntibac != null">is_antibac,</if>
            <if test="antitumorType != null">antitumor_type,</if>
            <if test="zxFlag != null">zx_flag,</if>
            <if test="isBasicDrug != null">is_basic_drug,</if>
            <if test="isInjection != null">is_injection,</if>
            <if test="lawType != null">law_type,</if>
            <if test="drugType != null">drug_type,</if>
            <if test="antibacType != null">antibac_type,</if>
            <if test="isPurchaseInternet != null">is_purchase_internet,</if>
            <if test="stopFlag != null">stop_flag,</if>
            <if test="stopDate != null">stop_date,</if>
            <if test="medicareCode != null">medicare_code,</if>
            <if test="medicareType != null">medicare_type,</if>
            <if test="medicareRemark != null">medicare_remark,</if>
            <if test="approvalCertif != null">approval_certif,</if>
            <if test="hospCode != null">hosp_code,</if>
            <if test="impDate != null">imp_date,</if>
            <if test="isOpIp != null">is_op_ip,</if>
            <if test="isRm != null">is_rm,</if>
            <if test="isDjm != null">is_djm,</if>
            <if test="dj != null">dj,</if>
            <if test="je != null">je,</if>
            <if test="cancerDrug != null">cancer_drug,</if>
            <if test="coldDrug != null">cold_drug,</if>
            <if test="protonPumpDrug != null">proton_pump_drug,</if>
            <if test="glucocorticoid != null">glucocorticoid,</if>
            <if test="keyDrug != null">key_drug,</if>
            <if test="bloodDrug != null">blood_drug,</if>
            <if test="spiritAnaesthesiaDrug != null">spirit_anaesthesia_drug,</if>
            <if test="isZdjk != null">is_zdjk,</if>
            <if test="expensiveDrug != null">expensive_drug,</if>
            <if test="isTsyp != null">is_tsyp,</if>
            <if test="gt20 != null">gt_20,</if>
            <if test="gt21 != null">gt_21,</if>
            <if test="isJc != null">is_jc,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="drugId != null">#{drugId},</if>
            <if test="drugCode != null">#{drugCode},</if>
            <if test="drugSpec != null">#{drugSpec},</if>
            <if test="packingSpec != null">#{packingSpec},</if>
            <if test="packingUom != null">#{packingUom},</if>
            <if test="packingMinSpec != null">#{packingMinSpec},</if>
            <if test="packingMinQty != null">#{packingMinQty},</if>
            <if test="drugQty != null">#{drugQty},</if>
            <if test="drugUom != null">#{drugUom},</if>
            <if test="drugManuf != null">#{drugManuf},</if>
            <if test="drugProductName != null">#{drugProductName},</if>
            <if test="drugName != null">#{drugName},</if>
            <if test="drugSp != null">#{drugSp},</if>
            <if test="drugGeneralName != null">#{drugGeneralName},</if>
            <if test="drugForName != null">#{drugForName},</if>
            <if test="isAntibac != null">#{isAntibac},</if>
            <if test="antitumorType != null">#{antitumorType},</if>
            <if test="zxFlag != null">#{zxFlag},</if>
            <if test="isBasicDrug != null">#{isBasicDrug},</if>
            <if test="isInjection != null">#{isInjection},</if>
            <if test="lawType != null">#{lawType},</if>
            <if test="drugType != null">#{drugType},</if>
            <if test="antibacType != null">#{antibacType},</if>
            <if test="isPurchaseInternet != null">#{isPurchaseInternet},</if>
            <if test="stopFlag != null">#{stopFlag},</if>
            <if test="stopDate != null">#{stopDate},</if>
            <if test="medicareCode != null">#{medicareCode},</if>
            <if test="medicareType != null">#{medicareType},</if>
            <if test="medicareRemark != null">#{medicareRemark},</if>
            <if test="approvalCertif != null">#{approvalCertif},</if>
            <if test="hospCode != null">#{hospCode},</if>
            <if test="impDate != null">#{impDate},</if>
            <if test="isOpIp != null">#{isOpIp},</if>
            <if test="isRm != null">#{isRm},</if>
            <if test="isDjm != null">#{isDjm},</if>
            <if test="dj != null">#{dj},</if>
            <if test="je != null">#{je},</if>
            <if test="cancerDrug != null">#{cancerDrug},</if>
            <if test="coldDrug != null">#{coldDrug},</if>
            <if test="protonPumpDrug != null">#{protonPumpDrug},</if>
            <if test="glucocorticoid != null">#{glucocorticoid},</if>
            <if test="keyDrug != null">#{keyDrug},</if>
            <if test="bloodDrug != null">#{bloodDrug},</if>
            <if test="spiritAnaesthesiaDrug != null">#{spiritAnaesthesiaDrug},</if>
            <if test="isZdjk != null">#{isZdjk},</if>
            <if test="expensiveDrug != null">#{expensiveDrug},</if>
            <if test="isTsyp != null">#{isTsyp},</if>
            <if test="gt20 != null">#{gt20},</if>
            <if test="gt21 != null">#{gt21},</if>
            <if test="isJc != null">#{isJc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRmsItfHosDrug" parameterType="RmsItfHosDrug">
        update rms_itf_hos_drug
        <trim prefix="SET" suffixOverrides=",">
            <if test="drugId != null">drug_id = #{drugId},</if>
            <if test="drugSpec != null">drug_spec = #{drugSpec},</if>
            <if test="packingSpec != null">packing_spec = #{packingSpec},</if>
            <if test="packingUom != null">packing_uom = #{packingUom},</if>
            <if test="packingMinSpec != null">packing_min_spec = #{packingMinSpec},</if>
            <if test="packingMinQty != null">packing_min_qty = #{packingMinQty},</if>
            <if test="drugQty != null">drug_qty = #{drugQty},</if>
            <if test="drugUom != null">drug_uom = #{drugUom},</if>
            <if test="drugManuf != null">drug_manuf = #{drugManuf},</if>
            <if test="drugProductName != null">drug_product_name = #{drugProductName},</if>
            <if test="drugName != null">drug_name = #{drugName},</if>
            <if test="drugSp != null">drug_sp = #{drugSp},</if>
            <if test="drugGeneralName != null">drug_general_name = #{drugGeneralName},</if>
            <if test="drugForName != null">drug_for_name = #{drugForName},</if>
            <if test="isAntibac != null">is_antibac = #{isAntibac},</if>
            <if test="antitumorType != null">antitumor_type = #{antitumorType},</if>
            <if test="zxFlag != null">zx_flag = #{zxFlag},</if>
            <if test="isBasicDrug != null">is_basic_drug = #{isBasicDrug},</if>
            <if test="isInjection != null">is_injection = #{isInjection},</if>
            <if test="lawType != null">law_type = #{lawType},</if>
            <if test="drugType != null">drug_type = #{drugType},</if>
            <if test="antibacType != null">antibac_type = #{antibacType},</if>
            <if test="isPurchaseInternet != null">is_purchase_internet = #{isPurchaseInternet},</if>
            <if test="stopFlag != null">stop_flag = #{stopFlag},</if>
            <if test="stopDate != null">stop_date = #{stopDate},</if>
            <if test="medicareCode != null">medicare_code = #{medicareCode},</if>
            <if test="medicareType != null">medicare_type = #{medicareType},</if>
            <if test="medicareRemark != null">medicare_remark = #{medicareRemark},</if>
            <if test="approvalCertif != null">approval_certif = #{approvalCertif},</if>
            <if test="hospCode != null">hosp_code = #{hospCode},</if>
            <if test="impDate != null">imp_date = #{impDate},</if>
            <if test="isOpIp != null">is_op_ip = #{isOpIp},</if>
            <if test="isRm != null">is_rm = #{isRm},</if>
            <if test="isDjm != null">is_djm = #{isDjm},</if>
            <if test="dj != null">dj = #{dj},</if>
            <if test="je != null">je = #{je},</if>
            <if test="cancerDrug != null">cancer_drug = #{cancerDrug},</if>
            <if test="coldDrug != null">cold_drug = #{coldDrug},</if>
            <if test="protonPumpDrug != null">proton_pump_drug = #{protonPumpDrug},</if>
            <if test="glucocorticoid != null">glucocorticoid = #{glucocorticoid},</if>
            <if test="keyDrug != null">key_drug = #{keyDrug},</if>
            <if test="bloodDrug != null">blood_drug = #{bloodDrug},</if>
            <if test="spiritAnaesthesiaDrug != null">spirit_anaesthesia_drug = #{spiritAnaesthesiaDrug},</if>
            <if test="isZdjk != null">is_zdjk = #{isZdjk},</if>
            <if test="expensiveDrug != null">expensive_drug = #{expensiveDrug},</if>
            <if test="isTsyp != null">is_tsyp = #{isTsyp},</if>
            <if test="gt20 != null">gt_20 = #{gt20},</if>
            <if test="gt21 != null">gt_21 = #{gt21},</if>
            <if test="isJc != null">is_jc = #{isJc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where drug_code = #{drugCode}
    </update>

    <delete id="deleteRmsItfHosDrugByDrugCode" parameterType="String">
        delete from rms_itf_hos_drug where drug_code = #{drugCode}
    </delete>

    <delete id="deleteRmsItfHosDrugByDrugCodes" parameterType="String">
        delete from rms_itf_hos_drug where drug_code in 
        <foreach item="drugCode" collection="array" open="(" separator="," close=")">
            #{drugCode}
        </foreach>
    </delete>
</mapper>