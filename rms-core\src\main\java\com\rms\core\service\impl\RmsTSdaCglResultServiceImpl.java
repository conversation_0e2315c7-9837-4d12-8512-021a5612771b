package com.rms.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.mapper.RmsTSdaCglResultMapper;
import com.rms.core.domain.RmsTSdaCglResult;
import com.rms.core.service.IRmsTSdaCglResultService;

/**
 * 药品常规用量规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class RmsTSdaCglResultServiceImpl implements IRmsTSdaCglResultService
{
    @Autowired
    private RmsTSdaCglResultMapper rmsTSdaCglResultMapper;

    /**
     * 查询药品常规用量规则
     *
     * @param conditionId 药品常规用量规则主键
     * @return 药品常规用量规则
     */
    @Override
    public RmsTSdaCglResult selectRmsTSdaCglResultByConditionId(Long conditionId)
    {
        return rmsTSdaCglResultMapper.selectRmsTSdaCglResultByConditionId(conditionId);
    }

    /**
     * 查询药品常规用量规则列表
     *
     * @param rmsTSdaCglResult 药品常规用量规则
     * @return 药品常规用量规则
     */
    @Override
    public List<RmsTSdaCglResult> selectRmsTSdaCglResultList(RmsTSdaCglResult rmsTSdaCglResult)
    {
        return rmsTSdaCglResultMapper.selectRmsTSdaCglResultList(rmsTSdaCglResult);
    }

    /**
     * 新增药品常规用量规则
     *
     * @param rmsTSdaCglResult 药品常规用量规则
     * @return 结果
     */
    @Override
    public int insertRmsTSdaCglResult(RmsTSdaCglResult rmsTSdaCglResult)
    {
        return rmsTSdaCglResultMapper.insertRmsTSdaCglResult(rmsTSdaCglResult);
    }

    /**
     * 修改药品常规用量规则
     *
     * @param rmsTSdaCglResult 药品常规用量规则
     * @return 结果
     */
    @Override
    public int updateRmsTSdaCglResult(RmsTSdaCglResult rmsTSdaCglResult)
    {
        return rmsTSdaCglResultMapper.updateRmsTSdaCglResult(rmsTSdaCglResult);
    }

    /**
     * 批量删除药品常规用量规则
     *
     * @param conditionIds 需要删除的药品常规用量规则主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaCglResultByConditionIds(Long[] conditionIds)
    {
        return rmsTSdaCglResultMapper.deleteRmsTSdaCglResultByConditionIds(conditionIds);
    }

    /**
     * 删除药品常规用量规则信息
     *
     * @param conditionId 药品常规用量规则主键
     * @return 结果
     */
    @Override
    public int deleteRmsTSdaCglResultByConditionId(Long conditionId)
    {
        return rmsTSdaCglResultMapper.deleteRmsTSdaCglResultByConditionId(conditionId);
    }
}
