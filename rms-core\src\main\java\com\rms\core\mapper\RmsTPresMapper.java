package com.rms.core.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.rms.core.domain.RmsTPres;

/**
 * 处方信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface RmsTPresMapper 
{
    /**
     * 查询处方信息
     * 
     * @param code 处方信息主键
     * @return 处方信息
     */
    public RmsTPres selectRmsTPresByCode(String code);

    /**
     * 查询处方信息列表
     * 
     * @param rmsTPres 处方信息
     * @return 处方信息集合
     */
    public List<RmsTPres> selectRmsTPresList(RmsTPres rmsTPres);

    /**
     * 新增处方信息
     * 
     * @param rmsTPres 处方信息
     * @return 结果
     */
    public int insertRmsTPres(RmsTPres rmsTPres);

    /**
     * 修改处方信息
     * 
     * @param rmsTPres 处方信息
     * @return 结果
     */
    public int updateRmsTPres(RmsTPres rmsTPres);

    /**
     * 删除处方信息
     * 
     * @param code 处方信息主键
     * @return 结果
     */
    public int deleteRmsTPresByCode(String code);

    /**
     * 批量删除处方信息
     *
     * @param codes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTPresByCodes(String[] codes);

    /**
     * 批量更新处方状态
     *
     * @param codes 处方编码数组
     * @param flag 新的状态标志
     * @return 更新的记录数
     */
    public int batchUpdatePrescriptionStatus(@Param("codes") String[] codes, @Param("flag") Long flag);

    /**
     * 查询所有有处方的科室
     *
     * @return 科室列表
     */
    public List<Map<String, Object>> selectDistinctDepartments();
}
