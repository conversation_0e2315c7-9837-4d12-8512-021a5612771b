package com.rms.core.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 年龄代码对象 rms_t_sda_age
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public class RmsTSdaAge extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 年龄代码 */
    @Excel(name = "年龄代码")
    private String agetype;

    /** 年龄 */
    @Excel(name = "年龄")
    private BigDecimal age;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setAgetype(String agetype)
    {
        this.agetype = agetype;
    }

    public String getAgetype()
    {
        return agetype;
    }

    public void setAge(BigDecimal age)
    {
        this.age = age;
    }

    public BigDecimal getAge()
    {
        return age;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agetype", getAgetype())
            .append("age", getAge())
            .toString();
    }
}
