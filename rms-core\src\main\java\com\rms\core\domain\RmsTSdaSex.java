package com.rms.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.rms.common.annotation.Excel;
import com.rms.common.core.domain.BaseEntity;

/**
 * 标准药品性别库对象 rms_t_sda_sex
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public class RmsTSdaSex extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 药品说明书ID */
    @Excel(name = "药品说明书ID")
    private Long sdaId;

    /** 性别：1-男；2-女 */
    @Excel(name = "性别：1-男；2-女")
    private String sex;

    /** 标识（未用） */
    @Excel(name = "标识", readConverterExp = "未=用")
    private String bs;

    /** 是否屏蔽 */
    @Excel(name = "是否屏蔽")
    private String ispb;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setSdaId(Long sdaId)
    {
        this.sdaId = sdaId;
    }

    public Long getSdaId()
    {
        return sdaId;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getSex()
    {
        return sex;
    }

    public void setBs(String bs)
    {
        this.bs = bs;
    }

    public String getBs()
    {
        return bs;
    }

    public void setIspb(String ispb)
    {
        this.ispb = ispb;
    }

    public String getIspb()
    {
        return ispb;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sdaId", getSdaId())
            .append("sex", getSex())
            .append("bs", getBs())
            .append("ispb", getIspb())
            .toString();
    }
}
